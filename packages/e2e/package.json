{"name": "e2e", "version": "0.1.0", "private": true, "type": "commonjs", "scripts": {"cy:open": "cypress open", "cy:run": "cypress run", "clean": "rimraf .turbo .next .nyc_output cypress-coverage", "start:landing": "(cd ../../apps/landing && pnpm run dev)", "start:x247": "(cd ../../apps/x247 && pnpm run dev)", "start:lms": "(cd ../../apps/lms && pnpm run dev)", "start:server": "pnpm start:landing & pnpm start:x247 & pnpm start:lms", "test:e2e": "start-server-and-test start:server \"3000\" cy:run", "e2e:coverage": "pnpm nyc report"}, "nyc": {"extends": ".nycrc.json", "report-dir": "cypress-coverage", "reporter": ["json", "lcov", "text-summary"]}, "dependencies": {"dotenv": "^16.4.5", "landing": "workspace:*", "lms": "workspace:*", "x247": "workspace:*"}, "devDependencies": {"@cypress/code-coverage": "^3.13.9", "@dtbx/typescript-config": "workspace:*", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@types/node": "^20", "cypress": "^13.16.1", "nyc": "^17.1.0", "rimraf": "^6.0.1", "start-server-and-test": "^2.0.8", "typescript": "^5.8.2"}}