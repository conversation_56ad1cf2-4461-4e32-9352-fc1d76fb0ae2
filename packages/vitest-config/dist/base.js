import { defineConfig } from 'vitest/config';
import tsconfigPaths from 'vite-tsconfig-paths';
import react from '@vitejs/plugin-react';
export const baseConfig = defineConfig({
    plugins: [react(), tsconfigPaths()],
    test: {
        globals: true,
        include: [
            './__tests__/*.test.ts',
            './__tests__/*.test.tsx',
            './__tests__/**/*.test.ts',
            './__tests__/**/*.test.tsx',
        ],
        environment: 'jsdom',
        coverage: {
            provider: 'istanbul',
            reporter: [
                'html',
                'text-summary',
                [
                    'json',
                    {
                        file: `./coverage.json`,
                    },
                ],
            ],
            reportsDirectory: './__tests__/coverage',
            enabled: true,
            exclude: [
                '.eslintrc.mjs',
                '.eslintrc.js',
                'next.config.ts',
                'next.config.ts',
                'node_modules',
                'dist',
                'out',
                '.next',
                '.next-client',
                'e2e',
                '__tests__',
            ],
        },
    },
});
