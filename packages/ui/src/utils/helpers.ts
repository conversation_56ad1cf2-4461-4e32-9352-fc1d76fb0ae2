/**
 * <AUTHOR> on 12/06/2025
 */
import { ReadonlyURLSearchParams } from 'next/navigation'

/*
 * This function accepts the current path, pathname and search-params to accommodate lms product paths and matches it with existing paths to determine the active path and returns a boolean
 * */
type MatchPathOptions = {
  path: string
  end: boolean
}

export const isNavMatch = (
  path: string,
  pathname: string,
  searchParams: string | ReadonlyURLSearchParams
) => {
  const url = `${pathname}?${searchParams}`
  const matchPath = (options: MatchPathOptions, pathname: string) => {
    const { path, end } = options
    const regex = new RegExp(`^${path}${end ? '$' : ''}`, 'i')
    return regex.test(pathname)
  }
  return path && searchParams.toString().length > 0
    ? url.includes(path)
    : path && searchParams.toString().length === 0
      ? matchPath({ path, end: false }, pathname)
      : false
}
