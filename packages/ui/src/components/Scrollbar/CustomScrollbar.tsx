'use client'
import React from 'react'
import SimpleBar from 'simplebar-react'
import 'simplebar-react/dist/simplebar.min.css'
import { styled } from '@mui/material'

const StyledBar = styled(SimpleBar)(() => ({
  maxHeight: '100vh',
  '& .simplebar-scrollbar': {
    '&:before': {
      backgroundColor: '#667085',
    },
  },
  '& .simplebar-track.simplebar-vertical': {
    width: 15,
    backgroundColor: '#f1f2f6',
  },
}))

export function CustomScrollbar({ children }: { children: React.ReactNode }) {
  return <StyledBar autoHide={false}>{children}</StyledBar>
}
