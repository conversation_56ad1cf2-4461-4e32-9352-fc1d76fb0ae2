'use client'
import React, { FC, ReactNode, useEffect } from 'react'

import { LoadingFullScreen } from '../Loading'
import { useCustomRouter } from '../../hooks'

export interface AuthWrapperProps {
  children: ReactNode
  requiresAuth?: boolean
  isLoggedIn: () => void | boolean
  loginUrl?: string
  homeUrl?: string
}

export const AuthWrapper: FC<AuthWrapperProps> = ({
  children,
  requiresAuth = true,
  isLoggedIn,
  loginUrl = '/',
  homeUrl = '/landing',
}) => {
  const router = useCustomRouter()
  const isAuthenticated = isLoggedIn()
  useEffect(() => {
    if (requiresAuth && !isAuthenticated) {
      console.log('Should route here')
      window.location.assign(loginUrl)
    } else if (!requiresAuth && isAuthenticated) {
      console.log('Should route here')
      router.push(homeUrl)
    }
  }, [isAuthenticated, requiresAuth, router])

  if (
    (requiresAuth && !isAuthenticated) ||
    (!requiresAuth && isAuthenticated)
  ) {
    return (
      <>
        <LoadingFullScreen />
      </>
    )
  }

  return <>{children}</>
}
