import type { IconsProps } from './IconTypes'

export const PaymentService = ({
  width = '52',
  height = '52',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 52 52"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g filter="url(#filter0_d_7814_146683)">
        <path
          d="M2.5 11C2.5 5.75329 6.75329 1.5 12 1.5H40C45.2467 1.5 49.5 5.75329 49.5 11V39C49.5 44.2467 45.2467 48.5 40 48.5H12C6.75329 48.5 2.5 44.2467 2.5 39V11Z"
          stroke="#EAECF0"
          shape-rendering="crispEdges"
        />
        <path
          d="M29.9377 28.9377C33.3603 28.4795 36 25.548 36 22C36 18.134 32.866 15 29 15C25.452 15 22.5205 17.6397 22.0623 21.0623M30 28C30 31.866 26.866 35 23 35C19.134 35 16 31.866 16 28C16 24.134 19.134 21 23 21C26.866 21 30 24.134 30 28Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_7814_146683"
          x="0"
          y="0"
          width="52"
          height="52"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_7814_146683"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_7814_146683"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}

export const PaymentServiceIcon2 = ({
  width = '12',
  height = '12',
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 12 12"
      fill="none"
    >
      <g clipPath="url(#clip0_7679_230334)">
        <path
          d="M5.09586 1.96896C5.69767 1.37923 6.52189 1.01562 7.43104 1.01562C9.27383 1.01562 10.7677 2.5095 10.7677 4.35229C10.7677 5.26146 10.4041 6.08569 9.81435 6.6875M7.90771 7.21229C7.90771 9.05508 6.41383 10.549 4.57104 10.549C2.72825 10.549 1.23438 9.05508 1.23438 7.21229C1.23438 5.3695 2.72825 3.87563 4.57104 3.87563C6.41383 3.87563 7.90771 5.3695 7.90771 7.21229Z"
          stroke="#2A3339"
          strokeWidth="0.762667"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_7679_230334">
          <rect
            width="11.44"
            height="11.44"
            fill="white"
            transform="translate(0.28125 0.0625)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
