import type { IconsProps } from './IconTypes'

export const ExportIcon = ({
  width = '14',
  height = '18',
  fill = 'none',
  stroke = '#555C61',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 18"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M8.5 1.7021V4.8C8.5 5.22004 8.5 5.43006 8.58175 5.59049C8.65365 5.73161 8.76839 5.84635 8.90951 5.91825C9.06994 6 9.27996 6 9.7 6H12.7979M13 7.49117V12.9C13 14.1601 13 14.7902 12.7548 15.2715C12.539 15.6948 12.1948 16.039 11.7715 16.2548C11.2902 16.5 10.6601 16.5 9.4 16.5H4.6C3.33988 16.5 2.70982 16.5 2.22852 16.2548C1.80516 16.039 1.46095 15.6948 1.24524 15.2715C1 14.7902 1 14.1601 1 12.9V5.1C1 3.83988 1 3.20982 1.24524 2.72852C1.46095 2.30516 1.80516 1.96095 2.22852 1.74524C2.70982 1.5 3.33988 1.5 4.6 1.5H7.00883C7.55916 1.5 7.83432 1.5 8.09327 1.56217C8.32285 1.61729 8.54233 1.7082 8.74364 1.83156C8.9707 1.9707 9.16527 2.16527 9.55442 2.55442L11.9456 4.94558C12.3347 5.33473 12.5293 5.5293 12.6684 5.75636C12.7918 5.95767 12.8827 6.17715 12.9378 6.40673C13 6.66568 13 6.94084 13 7.49117Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const FileDownloadIcon = ({
  width = '18',
  height = '18',
  fill = 'none',
  stroke = '#555C61',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 18"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M15 9.375V5.1C15 3.83988 15 3.20982 14.7548 2.72852C14.539 2.30516 14.1948 1.96095 13.7715 1.74524C13.2902 1.5 12.6601 1.5 11.4 1.5H6.6C5.33988 1.5 4.70982 1.5 4.22852 1.74524C3.80516 1.96095 3.46095 2.30516 3.24524 2.72852C3 3.20982 3 3.83988 3 5.1V12.9C3 14.1601 3 14.7902 3.24524 15.2715C3.46095 15.6948 3.80516 16.039 4.22852 16.2548C4.70982 16.5 5.33985 16.5 6.59989 16.5H9.375M11.25 14.25L13.5 16.5M13.5 16.5L15.75 14.25M13.5 16.5V12"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const ExportFinalIcon = ({
  width = '15',
  height = '14',
  fill = 'none',
  stroke = 'white',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 15 14"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M3.08203 7H12.4154M12.4154 7L8.91536 3.5M12.4154 7L8.91536 10.5"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const LoadingIcon = ({
  width = '512',
  height = '113',
  fill = 'none',
  stroke = 'white',
  strokeWidth = '8',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 512 113"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <mask
        id="mask0_5209_226795"
        mask-type="alpha"
        maskUnits="userSpaceOnUse"
        x="220"
        y="-3"
        width="72"
        height="72"
      >
        <rect
          x="220"
          y="-3"
          width="72"
          height="72"
          rx="36"
          fill="url(#paint0_angular_5209_226795)"
        />
      </mask>
      <g mask="url(#mask0_5209_226795)">
        <path
          d="M285 33C285 36.8083 284.25 40.5794 282.793 44.0978C281.335 47.6163 279.199 50.8132 276.506 53.5061C273.813 56.199 270.616 58.3351 267.098 59.7925C263.579 61.2499 259.808 62 256 62C252.192 62 248.421 61.2499 244.902 59.7925C241.384 58.3351 238.187 56.199 235.494 53.5061C232.801 50.8132 230.665 47.6163 229.207 44.0978C227.75 40.5794 227 36.8083 227 33C227 29.1917 227.75 25.4206 229.207 21.9022C230.665 18.3837 232.801 15.1868 235.494 12.4939C238.187 9.801 241.384 7.66487 244.902 6.20749C248.421 4.7501 252.192 4 256 4C259.808 4 263.579 4.75011 267.098 6.2075C270.616 7.66489 273.813 9.80101 276.506 12.4939C279.199 15.1868 281.335 18.3838 282.793 21.9022C284.25 25.4206 285 29.1917 285 33L285 33Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeDasharray="0.1 8"
        />
      </g>
      <path
        d="M214.735 105V93.138H221.017L220.477 94.74H216.625V98.16H220.459V99.762H216.625V103.398H221.071V105H214.735ZM222.245 105L225.395 100.716L222.353 96.486H224.369L226.439 99.438L228.545 96.486H230.525L227.447 100.716L230.525 105H228.545L226.403 101.994L224.243 105H222.245ZM231.758 109.266V96.81C232.676 96.486 233.738 96.27 234.836 96.27C237.77 96.27 239.372 97.962 239.372 100.716C239.372 103.38 237.59 105.18 234.998 105.18C234.512 105.18 234.044 105.09 233.63 104.946V109.266H231.758ZM233.63 103.344C234.026 103.524 234.386 103.614 234.872 103.614C236.564 103.614 237.446 102.462 237.446 100.698C237.446 98.736 236.366 97.836 234.764 97.836C234.35 97.836 233.99 97.89 233.63 97.998V103.344ZM244.836 105.18C242.352 105.18 240.678 103.254 240.678 100.716C240.678 98.178 242.334 96.27 244.836 96.27C247.32 96.27 248.976 98.178 248.976 100.716C248.976 103.254 247.32 105.18 244.836 105.18ZM244.836 103.65C246.258 103.65 247.05 102.426 247.05 100.716C247.05 99.024 246.258 97.8 244.836 97.8C243.414 97.8 242.604 99.024 242.604 100.716C242.604 102.426 243.414 103.65 244.836 103.65ZM250.708 105V96.486H252.274L252.49 97.476C253.318 96.63 254.038 96.27 254.848 96.27C255.226 96.27 255.658 96.378 255.964 96.576L255.298 98.178C254.974 97.998 254.668 97.944 254.398 97.944C253.822 97.944 253.318 98.214 252.58 98.898V105H250.708ZM260.007 105.18C258.585 105.18 257.739 104.37 257.739 102.984V97.962H256.245V96.486H257.739V94.524L259.611 93.948V96.486H262.167L261.699 97.962H259.611V102.57C259.611 103.344 259.899 103.65 260.457 103.65C260.979 103.65 261.447 103.452 261.789 103.218L262.383 104.514C261.789 104.928 260.853 105.18 260.007 105.18ZM263.806 105V96.486H265.678V105H263.806ZM264.742 94.92C264.058 94.92 263.572 94.416 263.572 93.768C263.572 93.12 264.058 92.616 264.742 92.616C265.426 92.616 265.912 93.102 265.912 93.768C265.912 94.416 265.408 94.92 264.742 94.92ZM267.916 105V96.486H269.5L269.698 97.494C270.598 96.756 271.57 96.27 272.632 96.27C273.982 96.27 275.296 96.99 275.296 99.33V105H273.424V99.924C273.424 98.808 273.172 97.908 272.038 97.908C271.246 97.908 270.472 98.358 269.788 98.934V105H267.916ZM280.523 109.266C278.093 109.266 276.635 108.276 276.635 106.782C276.635 105.864 277.229 105.108 278.183 104.676C277.823 104.478 277.607 104.064 277.607 103.632C277.607 103.146 277.877 102.534 278.597 101.994C277.715 101.472 277.211 100.518 277.211 99.384C277.211 97.764 278.399 96.27 280.559 96.27C281.315 96.27 281.873 96.45 282.233 96.63C282.323 96.684 282.359 96.666 282.431 96.612C282.881 96.162 283.439 95.874 284.393 95.874C284.591 95.874 284.825 95.91 284.933 95.928L284.555 97.296C284.357 97.26 284.141 97.224 283.961 97.224C283.745 97.224 283.475 97.278 283.277 97.404C283.817 97.962 284.087 98.7 284.087 99.474C284.087 101.364 282.683 102.606 280.649 102.606C280.253 102.606 279.875 102.552 279.587 102.444C279.353 102.75 279.281 102.966 279.281 103.182C279.281 104.766 284.879 103.326 284.879 106.512C284.879 108.078 283.331 109.266 280.523 109.266ZM280.739 107.916C282.197 107.916 283.061 107.394 283.061 106.71C283.061 105.378 280.469 105.648 279.173 105.18C278.849 105.396 278.327 105.81 278.327 106.458C278.327 107.358 279.281 107.916 280.739 107.916ZM280.649 101.148C281.567 101.148 282.287 100.554 282.287 99.438C282.287 98.502 281.639 97.728 280.613 97.728C279.677 97.728 279.011 98.34 279.011 99.42C279.011 100.392 279.605 101.148 280.649 101.148ZM287.491 105.162C286.879 105.162 286.411 104.658 286.411 104.028C286.411 103.398 286.879 102.912 287.491 102.912C288.085 102.912 288.571 103.398 288.571 104.028C288.571 104.658 288.085 105.162 287.491 105.162ZM291.991 105.162C291.379 105.162 290.911 104.658 290.911 104.028C290.911 103.398 291.379 102.912 291.991 102.912C292.585 102.912 293.071 103.398 293.071 104.028C293.071 104.658 292.585 105.162 291.991 105.162ZM296.491 105.162C295.879 105.162 295.411 104.658 295.411 104.028C295.411 103.398 295.879 102.912 296.491 102.912C297.085 102.912 297.571 103.398 297.571 104.028C297.571 104.658 297.085 105.162 296.491 105.162Z"
        fill="white"
      />
      <defs>
        <radialGradient
          id="paint0_angular_5209_226795"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(256 33) rotate(169.077) scale(36.0321)"
        >
          <stop />
          <stop offset="0.921875" stop-opacity="0" />
          <stop offset="1" stop-opacity="0" />
        </radialGradient>
      </defs>
    </svg>
  )
}
