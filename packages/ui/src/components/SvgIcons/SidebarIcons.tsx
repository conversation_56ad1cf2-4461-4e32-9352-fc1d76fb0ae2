import type { IconsProps } from './IconTypes'

export const HomeIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M6.96794 14.1667H13.6346M9.48268 2.30333L3.83076 6.69927C3.45295 6.99312 3.26405 7.14005 3.12796 7.32405C3.00741 7.48704 2.9176 7.67065 2.86296 7.86588C2.80127 8.08627 2.80127 8.32558 2.80127 8.80421V14.8333C2.80127 15.7667 2.80127 16.2335 2.98293 16.59C3.14271 16.9036 3.39768 17.1585 3.71129 17.3183C4.0678 17.5 4.53452 17.5 5.46794 17.5H15.1346C16.068 17.5 16.5347 17.5 16.8913 17.3183C17.2049 17.1585 17.4598 16.9036 17.6196 16.59C17.8013 16.2335 17.8013 15.7667 17.8013 14.8333V8.80421C17.8013 8.32558 17.8013 8.08627 17.7396 7.86588C17.6849 7.67065 17.5951 7.48704 17.4746 7.32405C17.3385 7.14005 17.1496 6.99312 16.7718 6.69927L11.1199 2.30333C10.8271 2.07562 10.6807 1.96177 10.5191 1.918C10.3764 1.87938 10.2261 1.87938 10.0835 1.918C9.92184 1.96177 9.77545 2.07562 9.48268 2.30333Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ChevronRightIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g id="chevron-right-double">
        <path
          id="Icon"
          d="M5 14.1663L9.16667 9.99967L5 5.83301M10.8333 14.1663L15 9.99967L10.8333 5.83301"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export const ChevronRightIconSingle = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M7.5 15L12.5 10L7.5 5"
        stroke="#029327"
        strokeWidth="1.66667"
        strokeLinecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}

export const MenuIcon = ({
  width = '22',
  height = '22',
  fill = 'none',
  stroke = '#344054',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 12 13"
      fill={fill}
      {...rest}
    >
      <g id="menu-02">
        <path
          id="Icon"
          d="M1.5 6.5H7.5M1.5 3.5H10.5M1.5 9.5H10.5"
          stroke={stroke}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  )
}

export const RequestsApprovalIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      {...rest}
    >
      <g clipPath="url(#clip0_1069_24414)">
        <path
          d="M19.2179 11.25L17.5517 9.58333L15.8846 11.25M17.8013 10C17.8013 14.1421 14.4434 17.5 10.3013 17.5C6.15913 17.5 2.80127 14.1421 2.80127 10C2.80127 5.85786 6.15913 2.5 10.3013 2.5C13.0529 2.5 15.4584 3.98178 16.7634 6.19091M10.3013 5.83333V10L12.8013 11.6667"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1069_24414">
          <rect
            width="20"
            height={height}
            fill="white"
            transform="translate(0.30127)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const StaffUsersIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M9.71977 18.0124C9.90427 18.12 9.99651 18.1739 10.1267 18.2018C10.2277 18.2234 10.3751 18.2234 10.4762 18.2018C10.6064 18.1739 10.6986 18.12 10.8831 18.0124C12.5065 17.0653 16.9681 14.0904 16.9681 10V6.83335C16.9681 5.93844 16.9681 5.49098 16.8302 5.17335C16.6899 4.85052 16.5502 4.67878 16.2627 4.47577C15.9798 4.27604 15.4253 4.1607 14.3162 3.93003C13.0938 3.6758 12.155 3.21671 11.2967 2.55278C10.8852 2.23441 10.6794 2.07522 10.5184 2.0318C10.3485 1.98598 10.2544 1.98598 10.0845 2.0318C9.92345 2.07522 9.71768 2.23441 9.30612 2.55278C8.44787 3.21671 7.5091 3.6758 6.28668 3.93003C5.17758 4.1607 4.62303 4.27604 4.34014 4.47577C4.05262 4.67878 3.91292 4.85052 3.77271 5.17335C3.63477 5.49098 3.63477 5.93844 3.63477 6.83335V10C3.63477 14.0904 8.0964 17.0653 9.71977 18.0124Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const CustomersIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M18.6344 17.5V15.8333C18.6344 14.2801 17.5721 12.9751 16.1344 12.605M13.2178 2.7423C14.4394 3.23679 15.3011 4.43443 15.3011 5.83333C15.3011 7.23224 14.4394 8.42988 13.2178 8.92437M14.4678 17.5C14.4678 15.9469 14.4678 15.1703 14.214 14.5577C13.8757 13.741 13.2268 13.092 12.4101 12.7537C11.7975 12.5 11.0209 12.5 9.46777 12.5H6.96777C5.41463 12.5 4.63807 12.5 4.0255 12.7537C3.20874 13.092 2.55982 13.741 2.22151 14.5577C1.96777 15.1703 1.96777 15.9469 1.96777 17.5M11.5511 5.83333C11.5511 7.67428 10.0587 9.16667 8.21777 9.16667C6.37682 9.16667 4.88444 7.67428 4.88444 5.83333C4.88444 3.99238 6.37682 2.5 8.21777 2.5C10.0587 2.5 11.5511 3.99238 11.5511 5.83333Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const TransactionLimitsIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M6.1346 9.16667V6.66667C6.1346 4.36548 8.00008 2.5 10.3013 2.5C12.3171 2.5 13.9985 3.93147 14.3846 5.83333M6.80127 17.5H13.8013C15.2014 17.5 15.9015 17.5 16.4362 17.2275C16.9067 16.9878 17.2891 16.6054 17.5288 16.135C17.8013 15.6002 17.8013 14.9001 17.8013 13.5V13.1667C17.8013 11.7665 17.8013 11.0665 17.5288 10.5317C17.2891 10.0613 16.9067 9.67883 16.4362 9.43915C15.9015 9.16667 15.2014 9.16667 13.8013 9.16667H6.80127C5.40114 9.16667 4.70107 9.16667 4.16629 9.43915C3.69589 9.67883 3.31344 10.0613 3.07375 10.5317C2.80127 11.0665 2.80127 11.7665 2.80127 13.1667V13.5C2.80127 14.9001 2.80127 15.6002 3.07375 16.135C3.31344 16.6054 3.69589 16.9878 4.16629 17.2275C4.70107 17.5 5.40114 17.5 6.80127 17.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ChargesIcon = ({
  width = '20',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M2.5 6.66797L12.5 6.66797M12.5 6.66797C12.5 8.04868 13.6193 9.16797 15 9.16797C16.3807 9.16797 17.5 8.04868 17.5 6.66797C17.5 5.28726 16.3807 4.16797 15 4.16797C13.6193 4.16797 12.5 5.28726 12.5 6.66797ZM7.5 13.3346L17.5 13.3346M7.5 13.3346C7.5 14.7153 6.38071 15.8346 5 15.8346C3.61929 15.8346 2.5 14.7153 2.5 13.3346C2.5 11.9539 3.61929 10.8346 5 10.8346C6.38071 10.8346 7.5 11.9539 7.5 13.3346Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ConfigurationItemIcon = ({
  width = '28',
  height = '28',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '0.967105',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 28 28"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <rect width="28" height="28" rx="2.947" fill="#E7E8E9" />
      <path
        d="M8.83752 11.9L16.1875 11.9M16.1875 11.9C16.1875 12.9148 17.0102 13.7375 18.025 13.7375C19.0398 13.7375 19.8625 12.9148 19.8625 11.9C19.8625 10.8852 19.0398 10.0625 18.025 10.0625C17.0102 10.0625 16.1875 10.8852 16.1875 11.9ZM12.5125 16.8L19.8625 16.8M12.5125 16.8C12.5125 17.8148 11.6898 18.6375 10.675 18.6375C9.6602 18.6375 8.83752 17.8148 8.83752 16.8C8.83752 15.7852 9.6602 14.9625 10.675 14.9625C11.6898 14.9625 12.5125 15.7852 12.5125 16.8Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const TarrifsIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M10.3011 14.1667C10.3011 16.4679 12.1666 18.3333 14.4678 18.3333C16.769 18.3333 18.6344 16.4679 18.6344 14.1667C18.6344 11.8655 16.769 10 14.4678 10C12.1666 10 10.3011 11.8655 10.3011 14.1667ZM10.3011 14.1667C10.3011 13.2285 10.6112 12.3627 11.1344 11.6663V4.16667M10.3011 14.1667C10.3011 14.8545 10.4678 15.5033 10.7629 16.0751C10.0608 16.6681 8.43931 17.0833 6.55111 17.0833C4.0198 17.0833 1.96777 16.3371 1.96777 15.4167V4.16667M11.1344 4.16667C11.1344 5.08714 9.08241 5.83333 6.55111 5.83333C4.0198 5.83333 1.96777 5.08714 1.96777 4.16667M11.1344 4.16667C11.1344 3.24619 9.08241 2.5 6.55111 2.5C4.0198 2.5 1.96777 3.24619 1.96777 4.16667M1.96777 11.6667C1.96777 12.5871 4.0198 13.3333 6.55111 13.3333C8.37527 13.3333 9.95054 12.9458 10.6883 12.3848M11.1344 7.91667C11.1344 8.83714 9.08241 9.58333 6.55111 9.58333C4.0198 9.58333 1.96777 8.83714 1.96777 7.91667"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const BeneficiaryBanksIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M4.46794 7.50006V14.1667M8.21794 7.50006V14.1667M12.3846 7.50006V14.1667M16.1346 7.50006V14.1667M2.80127 15.5001L2.80127 16.1667C2.80127 16.6334 2.80127 16.8668 2.8921 17.045C2.97199 17.2019 3.09948 17.3293 3.25628 17.4092C3.43454 17.5001 3.66789 17.5001 4.1346 17.5001H16.4679C16.9346 17.5001 17.168 17.5001 17.3463 17.4092C17.5031 17.3293 17.6305 17.2019 17.7104 17.045C17.8013 16.8668 17.8013 16.6334 17.8013 16.1667V15.5001C17.8013 15.0333 17.8013 14.8 17.7104 14.6217C17.6305 14.4649 17.5031 14.3374 17.3463 14.2576C17.168 14.1667 16.9346 14.1667 16.4679 14.1667H4.1346C3.66789 14.1667 3.43454 14.1667 3.25628 14.2576C3.09948 14.3374 2.97199 14.4649 2.8921 14.6217C2.80127 14.8 2.80127 15.0333 2.80127 15.5001ZM10.012 2.56433L3.84536 3.9347C3.47281 4.01749 3.28654 4.05889 3.14749 4.15906C3.02484 4.24742 2.92854 4.36747 2.86889 4.50636C2.80127 4.66383 2.80127 4.85465 2.80127 5.23629L2.80127 6.16672C2.80127 6.63343 2.80127 6.86679 2.8921 7.04505C2.97199 7.20185 3.09948 7.32933 3.25628 7.40923C3.43454 7.50006 3.66789 7.50006 4.1346 7.50006H16.4679C16.9346 7.50006 17.168 7.50006 17.3463 7.40923C17.5031 7.32933 17.6305 7.20185 17.7104 7.04505C17.8013 6.86679 17.8013 6.63343 17.8013 6.16672V5.23629C17.8013 4.85465 17.8013 4.66383 17.7336 4.50636C17.674 4.36747 17.5777 4.24742 17.455 4.15906C17.316 4.05889 17.1297 4.01749 16.7572 3.9347L10.5905 2.56433C10.4826 2.54035 10.4286 2.52835 10.3741 2.52357C10.3256 2.51932 10.2769 2.51932 10.2285 2.52357C10.1739 2.52835 10.12 2.54035 10.012 2.56433Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const BranchesIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M13.6344 11.1454C16.5776 11.724 18.6344 13.0456 18.6344 14.5833C18.6344 16.6544 14.9035 18.3333 10.3011 18.3333C5.69873 18.3333 1.96777 16.6544 1.96777 14.5833C1.96777 13.0456 4.0246 11.724 6.96777 11.1454M10.3011 14.1667V7.5M10.3011 7.5C11.6818 7.5 12.8011 6.38071 12.8011 5C12.8011 3.61929 11.6818 2.5 10.3011 2.5C8.92039 2.5 7.80111 3.61929 7.80111 5C7.80111 6.38071 8.92039 7.5 10.3011 7.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const LogsIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M11.1344 5.83333L10.2048 3.9741C9.93728 3.439 9.80349 3.17144 9.60392 2.97597C9.42742 2.80311 9.21471 2.67164 8.98117 2.59109C8.71708 2.5 8.41795 2.5 7.81968 2.5H4.63444C3.70102 2.5 3.23431 2.5 2.87779 2.68166C2.56419 2.84144 2.30922 3.09641 2.14943 3.41002C1.96777 3.76654 1.96777 4.23325 1.96777 5.16667V5.83333M1.96777 5.83333H14.6344C16.0346 5.83333 16.7346 5.83333 17.2694 6.10582C17.7398 6.3455 18.1223 6.72795 18.362 7.19836C18.6344 7.73314 18.6344 8.4332 18.6344 9.83333V13.5C18.6344 14.9001 18.6344 15.6002 18.362 16.135C18.1223 16.6054 17.7398 16.9878 17.2694 17.2275C16.7346 17.5 16.0346 17.5 14.6344 17.5H5.96777C4.56764 17.5 3.86758 17.5 3.3328 17.2275C2.86239 16.9878 2.47994 16.6054 2.24026 16.135C1.96777 15.6002 1.96777 14.9001 1.96777 13.5V5.83333Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const ReportsIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M6.96794 10.8333V14.1667M13.6346 9.16667V14.1667M10.3013 5.83333V14.1667M6.80127 17.5H13.8013C15.2014 17.5 15.9015 17.5 16.4362 17.2275C16.9067 16.9878 17.2891 16.6054 17.5288 16.135C17.8013 15.6002 17.8013 14.9001 17.8013 13.5V6.5C17.8013 5.09987 17.8013 4.3998 17.5288 3.86502C17.2891 3.39462 16.9067 3.01217 16.4362 2.77248C15.9015 2.5 15.2014 2.5 13.8013 2.5H6.80127C5.40114 2.5 4.70107 2.5 4.16629 2.77248C3.69589 3.01217 3.31344 3.39462 3.07375 3.86502C2.80127 4.3998 2.80127 5.09987 2.80127 6.5V13.5C2.80127 14.9001 2.80127 15.6002 3.07375 16.135C3.31344 16.6054 3.69589 16.9878 4.16629 17.2275C4.70107 17.5 5.40114 17.5 6.80127 17.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const AuditTrailIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M16.9681 10.4166V5.66663C16.9681 4.26649 16.9681 3.56643 16.6956 3.03165C16.4559 2.56124 16.0735 2.17879 15.6031 1.93911C15.0683 1.66663 14.3682 1.66663 12.9681 1.66663H7.63477C6.23463 1.66663 5.53457 1.66663 4.99979 1.93911C4.52938 2.17879 4.14693 2.56124 3.90725 3.03165C3.63477 3.56643 3.63477 4.26649 3.63477 5.66663V14.3333C3.63477 15.7334 3.63477 16.4335 3.90725 16.9683C4.14693 17.4387 4.52938 17.8211 4.99979 18.0608C5.53457 18.3333 6.23461 18.3333 7.63471 18.3333H10.3014M12.3848 15.8333L14.0514 17.5L17.8014 13.75"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const AdvertisingIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M18.6344 6.66672V10M8.84277 4.58338H5.96777C4.56764 4.58338 3.86758 4.58338 3.3328 4.85587C2.86239 5.09555 2.47994 5.478 2.24026 5.94841C1.96777 6.48319 1.96777 7.18325 1.96777 8.58338L1.96777 9.58338C1.96777 10.36 1.96777 10.7482 2.09464 11.0545C2.2638 11.4629 2.58826 11.7874 2.99663 11.9565C3.30292 12.0834 3.69121 12.0834 4.46778 12.0834V15.625C4.46778 15.8185 4.46778 15.9153 4.4758 15.9967C4.55374 16.788 5.17979 17.4141 5.97108 17.492C6.05256 17.5 6.1493 17.5 6.34278 17.5C6.53625 17.5 6.63299 17.5 6.71447 17.492C7.50577 17.4141 8.13181 16.788 8.20975 15.9967C8.21778 15.9153 8.21778 15.8185 8.21778 15.625V12.0834H8.84278C10.3148 12.0834 12.1155 12.8725 13.5047 13.6298C14.3151 14.0715 14.7203 14.2924 14.9858 14.2599C15.2319 14.2298 15.4179 14.1193 15.5622 13.9176C15.7178 13.7002 15.7178 13.2651 15.7178 12.3948V4.27195C15.7178 3.40171 15.7178 2.96659 15.5622 2.74912C15.4179 2.54747 15.2319 2.43697 14.9858 2.40682C14.7203 2.37432 14.3151 2.59521 13.5047 3.03701C12.1155 3.79431 10.3148 4.58338 8.84277 4.58338Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const NavMenuIcon = ({
  width = '42',
  height = '42',
  fill = 'none',
  stroke = '#344054',
  strokeWidth = '1.54545',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 42 42"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g filter="url(#filter0_d_1760_109205)">
        <path
          d="M4 11.5556C4 7.38274 7.38274 4 11.5556 4H30.4444C34.6173 4 38 7.38274 38 11.5556V30.4444C38 34.6173 34.6173 38 30.4444 38H11.5556C7.38274 38 4 34.6173 4 30.4444V11.5556Z"
          fill="white"
        />
        <path
          d="M14.0449 21.0006H23.3176M14.0449 16.3643H27.954M14.0449 25.637H27.954"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_1760_109205"
          x="0"
          y="0"
          width="42"
          height="42"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="4"
            operator="dilate"
            in="SourceAlpha"
            result="effect1_dropShadow_1760_109205"
          />
          <feOffset />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.596078 0 0 0 0 0.635294 0 0 0 0 0.701961 0 0 0 0.14 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_1760_109205"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_1760_109205"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}

export const RequestsIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g clipPath="url(#clip0_489_853)">
        <path
          d="M18.6354 10H15.3021L12.8021 17.5L7.80208 2.5L5.30208 10H1.96875"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_489_853">
          <rect
            width="20"
            height={height}
            fill="white"
            transform="translate(0.301758)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const SettingsIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M8.13063 16.1426L8.61767 17.2379C8.76245 17.564 8.99874 17.841 9.29786 18.0355C9.59698 18.2299 9.9461 18.3334 10.3029 18.3333C10.6596 18.3334 11.0087 18.2299 11.3079 18.0355C11.607 17.841 11.8433 17.564 11.988 17.2379L12.4751 16.1426C12.6484 15.7539 12.9401 15.4298 13.3084 15.2166C13.6791 15.0028 14.1079 14.9118 14.5334 14.9564L15.7251 15.0833C16.0798 15.1208 16.4378 15.0546 16.7556 14.8927C17.0735 14.7309 17.3376 14.4802 17.5158 14.1713C17.6943 13.8625 17.7793 13.5085 17.7606 13.1524C17.7419 12.7962 17.6202 12.4531 17.4103 12.1648L16.7047 11.1953C16.4535 10.8476 16.3192 10.429 16.3214 9.99996C16.3213 9.57212 16.4568 9.15525 16.7084 8.80922L17.414 7.83977C17.6239 7.55142 17.7456 7.20835 17.7643 6.85219C17.783 6.49602 17.698 6.14208 17.5195 5.83329C17.3413 5.52432 17.0772 5.2737 16.7594 5.11182C16.4415 4.94993 16.0835 4.88373 15.7288 4.92126L14.5371 5.04811C14.1116 5.0928 13.6828 5.00173 13.3121 4.78792C12.9431 4.5735 12.6513 4.24776 12.4788 3.85737L11.988 2.762C11.8433 2.43594 11.607 2.15889 11.3079 1.96446C11.0087 1.77003 10.6596 1.66657 10.3029 1.66663C9.9461 1.66657 9.59698 1.77003 9.29786 1.96446C8.99874 2.15889 8.76245 2.43594 8.61767 2.762L8.13063 3.85737C7.95807 4.24776 7.66636 4.5735 7.2973 4.78792C6.92665 5.00173 6.49786 5.0928 6.0723 5.04811L4.87693 4.92126C4.52221 4.88373 4.16421 4.94993 3.84636 5.11182C3.52851 5.2737 3.26444 5.52432 3.08619 5.83329C2.90771 6.14208 2.82268 6.49602 2.84141 6.85219C2.86015 7.20835 2.98185 7.55142 3.19175 7.83977L3.8973 8.80922C4.14892 9.15525 4.28442 9.57212 4.28434 9.99996C4.28442 10.4278 4.14892 10.8447 3.8973 11.1907L3.19175 12.1601C2.98185 12.4485 2.86015 12.7916 2.84141 13.1477C2.82268 13.5039 2.90771 13.8578 3.08619 14.1666C3.26462 14.4754 3.52871 14.7259 3.84652 14.8878C4.16433 15.0496 4.52223 15.1159 4.87693 15.0787L6.0686 14.9518C6.49415 14.9071 6.92294 14.9982 7.2936 15.212C7.66404 15.4258 7.9571 15.7516 8.13063 16.1426Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.3014 12.5C11.6821 12.5 12.8014 11.3807 12.8014 9.99996C12.8014 8.61925 11.6821 7.49996 10.3014 7.49996C8.92065 7.49996 7.80137 8.61925 7.80137 9.99996C7.80137 11.3807 8.92065 12.5 10.3014 12.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const TransactionHistoryIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#667085',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      {...rest}
    >
      <path
        d="M13 5.00012C13 6.10469 10.5376 7.00012 7.5 7.00012C4.46243 7.00012 2 6.10469 2 5.00012M13 5.00012C13 3.89555 10.5376 3.00012 7.5 3.00012C4.46243 3.00012 2 3.89555 2 5.00012M13 5.00012V9.45727C11.7785 9.82411 11 10.3791 11 11.0001M2 5.00012V17.0001C2 18.1047 4.46243 19.0001 7.5 19.0001C8.82963 19.0001 10.0491 18.8286 11 18.543V11.0001M2 9.00012C2 10.1047 4.46243 11.0001 7.5 11.0001C8.82963 11.0001 10.0491 10.8286 11 10.543M2 13.0001C2 14.1047 4.46243 15.0001 7.5 15.0001C8.82963 15.0001 10.0491 14.8286 11 14.543M22 11.0001C22 12.1047 19.5376 13.0001 16.5 13.0001C13.4624 13.0001 11 12.1047 11 11.0001M22 11.0001C22 9.89555 19.5376 9.00012 16.5 9.00012C13.4624 9.00012 11 9.89555 11 11.0001M22 11.0001V19.0001C22 20.1047 19.5376 21.0001 16.5 21.0001C13.4624 21.0001 11 20.1047 11 19.0001V11.0001M22 15.0001C22 16.1047 19.5376 17.0001 16.5 17.0001C13.4624 17.0001 11 16.1047 11 15.0001"
        stroke="#667085"
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export const CreditCardIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill={fill}
    >
      <g clip-path="url(#clip0_50_1052)">
        <path
          d="M13.6354 14.9993L16.1354 12.4993M16.1354 12.4993L18.6354 14.9993M16.1354 12.4993V17.4993M18.6354 8.33268H1.96875M18.6354 9.99935V6.83268C18.6354 5.89926 18.6354 5.43255 18.4538 5.07603C18.294 4.76243 18.039 4.50746 17.7254 4.34767C17.3689 4.16602 16.9022 4.16602 15.9688 4.16602H4.63542C3.702 4.16602 3.23529 4.16602 2.87877 4.34767C2.56516 4.50746 2.3102 4.76243 2.15041 5.07603C1.96875 5.43255 1.96875 5.89926 1.96875 6.83268V13.166C1.96875 14.0994 1.96875 14.5661 2.15041 14.9227C2.31019 15.2363 2.56516 15.4912 2.87877 15.651C3.23529 15.8327 3.702 15.8327 4.63542 15.8327H10.3021"
          stroke="#2A3339"
          strokeWidth="2"
          strokeLinecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_50_1052">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.301758)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const DebitCardIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 21 20"
      fill={fill}
    >
      <g clip-path="url(#clip0_50_1042)">
        <path
          d="M18.6354 8.33268H1.96875M9.46875 11.666H5.30208M1.96875 6.83268L1.96875 13.166C1.96875 14.0994 1.96875 14.5661 2.15041 14.9227C2.31019 15.2363 2.56516 15.4912 2.87877 15.651C3.23529 15.8327 3.702 15.8327 4.63542 15.8327L15.9688 15.8327C16.9022 15.8327 17.3689 15.8327 17.7254 15.651C18.039 15.4912 18.294 15.2363 18.4538 14.9227C18.6354 14.5661 18.6354 14.0994 18.6354 13.166V6.83268C18.6354 5.89926 18.6354 5.43255 18.4538 5.07603C18.294 4.76243 18.039 4.50746 17.7254 4.34767C17.3689 4.16602 16.9022 4.16602 15.9688 4.16602L4.63542 4.16602C3.702 4.16602 3.23529 4.16602 2.87877 4.34767C2.56516 4.50746 2.3102 4.76243 2.15041 5.07603C1.96875 5.43255 1.96875 5.89926 1.96875 6.83268Z"
          stroke="#2A3339"
          strokeWidth="2"
          strokeLinecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_50_1042">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.301758)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const PrepaidCardIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="21"
      height="20"
      viewBox="0 0 21 20"
      fill={fill}
    >
      <g clip-path="url(#clip0_50_1061)">
        <path
          d="M16.1354 17.4993V12.4993M13.6354 14.9993H18.6354M18.6354 8.33268H1.96875M18.6354 9.99935V6.83268C18.6354 5.89926 18.6354 5.43255 18.4538 5.07603C18.294 4.76243 18.039 4.50746 17.7254 4.34767C17.3689 4.16602 16.9022 4.16602 15.9688 4.16602H4.63542C3.702 4.16602 3.23529 4.16602 2.87877 4.34767C2.56516 4.50746 2.3102 4.76243 2.15041 5.07603C1.96875 5.43255 1.96875 5.89926 1.96875 6.83268V13.166C1.96875 14.0994 1.96875 14.5661 2.15041 14.9227C2.31019 15.2363 2.56516 15.4912 2.87877 15.651C3.23529 15.8327 3.702 15.8327 4.63542 15.8327H10.3021"
          stroke="#2A3339"
          strokeWidth="2"
          strokeLinecap="round"
          stroke-linejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_50_1061">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0.301758)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}

export const CardHeaderIcon = ({
  width = '21',
  height = '20',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 22 22"
      fill={fill}
    >
      <path
        d="M20.1668 9.16732H1.8335M10.0835 12.834H5.50016M1.8335 7.51732L1.8335 14.484C1.8335 15.5107 1.8335 16.0241 2.03332 16.4163C2.20909 16.7613 2.48955 17.0417 2.83451 17.2175C3.22669 17.4173 3.74007 17.4173 4.76683 17.4173L17.2335 17.4173C18.2603 17.4173 18.7736 17.4173 19.1658 17.2175C19.5108 17.0417 19.7912 16.7613 19.967 16.4163C20.1668 16.0241 20.1668 15.5107 20.1668 14.484V7.51732C20.1668 6.49056 20.1668 5.97717 19.967 5.585C19.7912 5.24004 19.5108 4.95957 19.1658 4.78381C18.7736 4.58399 18.2603 4.58399 17.2335 4.58399L4.76683 4.58398C3.74007 4.58398 3.22669 4.58398 2.83451 4.78381C2.48955 4.95957 2.20909 5.24004 2.03332 5.585C1.8335 5.97717 1.8335 6.49055 1.8335 7.51732Z"
        stroke="#000A12"
        strokeWidth="1.83333"
        strokeLinecap="round"
        stroke-linejoin="round"
      />
    </svg>
  )
}
