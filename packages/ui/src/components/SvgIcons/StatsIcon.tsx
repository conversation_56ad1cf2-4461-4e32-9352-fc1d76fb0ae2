/**
 * <AUTHOR> on 18/10/2024
 */

import type { IconsProps } from './IconTypes'

export const StatsIcon = ({
  width = '12',
  height = '12',
  fill = 'none',
  stroke = '#555C61',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 12 12"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M4.5 4.3C4.5 4.01997 4.5 3.87996 4.5545 3.773C4.60243 3.67892 4.67892 3.60243 4.77301 3.5545C4.87996 3.5 5.01997 3.5 5.3 3.5H7.5L7.5 10.5H4.5V4.3Z"
        fill="#D9D9D9"
      />
      <path
        d="M7.5 10.5H9.7C9.98003 10.5 10.12 10.5 10.227 10.4455C10.3211 10.3976 10.3976 10.3211 10.4455 10.227C10.5 10.12 10.5 9.98003 10.5 9.7V2.3C10.5 2.01997 10.5 1.87996 10.4455 1.773C10.3976 1.67892 10.3211 1.60243 10.227 1.5545C10.12 1.5 9.98003 1.5 9.7 1.5H8.3C8.01997 1.5 7.87996 1.5 7.77301 1.5545C7.67892 1.60243 7.60243 1.67892 7.5545 1.773C7.5 1.87996 7.5 2.01997 7.5 2.3V3.5M7.5 10.5H4.5M7.5 10.5L7.5 3.5M7.5 3.5H5.3C5.01997 3.5 4.87996 3.5 4.77301 3.5545C4.67892 3.60243 4.60243 3.67892 4.5545 3.773C4.5 3.87996 4.5 4.01997 4.5 4.3V10.5M4.5 5.5H2.3C2.01997 5.5 1.87996 5.5 1.77301 5.5545C1.67892 5.60243 1.60243 5.67892 1.5545 5.773C1.5 5.87996 1.5 6.01997 1.5 6.3V9.7C1.5 9.98003 1.5 10.12 1.5545 10.227C1.60243 10.3211 1.67892 10.3976 1.77301 10.4455C1.87996 10.5 2.01998 10.5 2.3 10.5H4.5"
        stroke={stroke}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
