import type { IconsProps } from './IconTypes'

export const LinkIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
      {...rest}
    >
      <path
        d="M8.4 16C8.4 14.29 9.79 12.9 11.5 12.9H15.5V11H11.5C8.74 11 6.5 13.24 6.5 16C6.5 18.76 8.74 21 11.5 21H15.5V19.1H11.5C9.79 19.1 8.4 17.71 8.4 16ZM12.5 17H20.5V15H12.5V17ZM21.5 11H17.5V12.9H21.5C23.21 12.9 24.6 14.29 24.6 16C24.6 17.71 23.21 19.1 21.5 19.1H17.5V21H21.5C24.26 21 26.5 18.76 26.5 16C26.5 13.24 24.26 11 21.5 11Z"
        fill={fill}
      />
    </svg>
  )
}
