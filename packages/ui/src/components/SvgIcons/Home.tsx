import type { IconsProps } from './IconTypes'

export const Home = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 21 21"
      fill={fill}
      {...rest}
    >
      <path
        d="M11.1203 2.36742C10.8276 2.13971 10.6812 2.02585 10.5195 1.98209C10.3769 1.94347 10.2266 1.94347 10.084 1.98209C9.92233 2.02585 9.77594 2.13971 9.48317 2.36742L3.83125 6.76336C3.45344 7.05721 3.26454 7.20413 3.12845 7.38813C3.0079 7.55112 2.91809 7.73474 2.86345 7.92996C2.80176 8.15035 2.80176 8.38967 2.80176 8.8683V14.8974C2.80176 15.8308 2.80176 16.2975 2.98341 16.6541C3.1432 16.9677 3.39817 17.2226 3.71177 17.3824C4.06829 17.5641 4.535 17.5641 5.46842 17.5641H7.13509C7.36845 17.5641 7.48512 17.5641 7.57425 17.5187C7.65265 17.4787 7.7164 17.415 7.75634 17.3366C7.80176 17.2474 7.80176 17.1308 7.80176 16.8974V11.3974C7.80176 10.9307 7.80176 10.6973 7.89259 10.5191C7.97248 10.3623 8.09996 10.2348 8.25677 10.1549C8.43503 10.0641 8.66838 10.0641 9.13509 10.0641H11.4684C11.9351 10.0641 12.1685 10.0641 12.3468 10.1549C12.5036 10.2348 12.631 10.3623 12.7109 10.5191C12.8018 10.6973 12.8018 10.9307 12.8018 11.3974V16.8974C12.8018 17.1308 12.8018 17.2474 12.8472 17.3366C12.8871 17.415 12.9509 17.4787 13.0293 17.5187C13.1184 17.5641 13.2351 17.5641 13.4684 17.5641H15.1351C16.0685 17.5641 16.5352 17.5641 16.8917 17.3824C17.2053 17.2226 17.4603 16.9677 17.6201 16.6541C17.8018 16.2975 17.8018 15.8308 17.8018 14.8974V8.8683C17.8018 8.38967 17.8018 8.15035 17.7401 7.92996C17.6854 7.73474 17.5956 7.55112 17.4751 7.38813C17.339 7.20413 17.1501 7.05721 16.7723 6.76336L11.1203 2.36742Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </svg>
  )
}
