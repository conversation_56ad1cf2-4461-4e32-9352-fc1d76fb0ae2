import type { IconsProps } from './IconTypes'

export const CommentIcon = ({
  width = '18',
  height = '18',
  fill = 'none',
  stroke = '#1570EF',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 18 18"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M1.5 5.5C1.5 4.09987 1.5 3.3998 1.77248 2.86502C2.01217 2.39462 2.39462 2.01217 2.86502 1.77248C3.3998 1.5 4.09987 1.5 5.5 1.5H12.5C13.9001 1.5 14.6002 1.5 15.135 1.77248C15.6054 2.01217 15.9878 2.39462 16.2275 2.86502C16.5 3.3998 16.5 4.09987 16.5 5.5V10C16.5 11.4001 16.5 12.1002 16.2275 12.635C15.9878 13.1054 15.6054 13.4878 15.135 13.7275C14.6002 14 13.9001 14 12.5 14H10.4031C9.88308 14 9.62306 14 9.37435 14.051C9.15369 14.0963 8.94017 14.1712 8.73957 14.2737C8.51347 14.3892 8.31043 14.5517 7.90434 14.8765L5.91646 16.4668C5.56973 16.7442 5.39636 16.8829 5.25045 16.8831C5.12356 16.8832 5.00352 16.8255 4.92436 16.7263C4.83333 16.6123 4.83333 16.3903 4.83333 15.9463V14C4.05836 14 3.67087 14 3.35295 13.9148C2.49022 13.6836 1.81635 13.0098 1.58519 12.147C1.5 11.8291 1.5 11.4416 1.5 10.6667V5.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const IconClose = ({
  width = '12',
  height = '12',
  fill = 'none',
  stroke = '#98A2B3',
  strokeWidth = '1.5',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 12 12"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M9 3L3 9M3 3L9 9"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const DeactivateBrokerIcon = ({
  width = '48',
  height = '48',
  fill = 'none',
  stroke = '#D92D20',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 48 48"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z"
        fill="#FEE4E2"
      />
      <path
        d="M28.5 16L33.5 21M33.5 16L28.5 21M28 33V31.8C28 30.1198 28 29.2798 27.673 28.638C27.3854 28.0735 26.9265 27.6146 26.362 27.327C25.7202 27 24.8802 27 23.2 27H18.8C17.1198 27 16.2798 27 15.638 27.327C15.0735 27.6146 14.6146 28.0735 14.327 28.638C14 29.2798 14 30.1198 14 31.8V33M24.5 19.5C24.5 21.433 22.933 23 21 23C19.067 23 17.5 21.433 17.5 19.5C17.5 17.567 19.067 16 21 16C22.933 16 24.5 17.567 24.5 19.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
export const EditTariffIcon = ({
  width = '14',
  height = '14',
  fill = 'none',
  stroke = '#555C61',
  strokeWidth = '0.7',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M1.67736 10.5663C1.70417 10.3251 1.71757 10.2045 1.75406 10.0918C1.78643 9.99179 1.83218 9.89662 1.89005 9.80886C1.95527 9.70994 2.04108 9.62414 2.21268 9.45253L9.91636 1.74887C10.5607 1.10454 11.6054 1.10454 12.2497 1.74887C12.894 2.39321 12.894 3.43788 12.2497 4.08221L4.54602 11.7859C4.37441 11.9575 4.28861 12.0433 4.18969 12.1085C4.10194 12.1664 4.00676 12.2121 3.90675 12.2445C3.79403 12.281 3.67343 12.2944 3.43222 12.3212L1.45801 12.5405L1.67736 10.5663Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
