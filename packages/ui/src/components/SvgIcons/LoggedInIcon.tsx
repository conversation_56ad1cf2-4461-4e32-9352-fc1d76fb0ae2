import type { IconsProps } from './IconTypes'

export const LoggedInIcon = ({
  width = '19',
  height = '19',
  fill = 'none',
  stroke = '#ABEFC6',
  strokeWidth = '0.863636',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 19 19"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M0.431818 9.5C0.431818 4.49178 4.49178 0.431818 9.5 0.431818C14.5082 0.431818 18.5682 4.49178 18.5682 9.5C18.5682 14.5082 14.5082 18.5682 9.5 18.5682C4.49178 18.5682 0.431818 14.5082 0.431818 9.5Z"
        fill="#ECFDF3"
      />
      <path
        d="M0.431818 9.5C0.431818 4.49178 4.49178 0.431818 9.5 0.431818C14.5082 0.431818 18.5682 4.49178 18.5682 9.5C18.5682 14.5082 14.5082 18.5682 9.5 18.5682C4.49178 18.5682 0.431818 14.5082 0.431818 9.5Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
      />
      <g clipPath="url(#clip0_2234_42275)">
        <path
          d="M6.90874 11.6593C6.90874 11.811 6.90874 11.8868 6.9155 11.9528C6.97167 12.5002 7.36759 12.9524 7.90281 13.0804C7.96727 13.0959 8.04248 13.1059 8.19283 13.1259L11.0393 13.5055C11.8496 13.6135 12.2548 13.6675 12.5692 13.5425C12.8453 13.4327 13.0752 13.2314 13.2205 12.9723C13.386 12.6771 13.386 12.2684 13.386 11.4509V7.54943C13.386 6.73198 13.386 6.32325 13.2205 6.02809C13.0752 5.76895 12.8453 5.56763 12.5692 5.45785C12.2548 5.33283 11.8496 5.38684 11.0393 5.49488L8.19283 5.87442C8.04246 5.89447 7.96727 5.90449 7.90281 5.91991C7.36759 6.04794 6.97167 6.50015 6.9155 7.0476C6.90874 7.11354 6.90874 7.18938 6.90874 7.34108M9.49964 7.7729L11.2269 9.50018M11.2269 9.50018L9.49964 11.2274M11.2269 9.50018H5.61328"
          stroke="#027A48"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_2234_42275">
          <rect
            width="10.3636"
            height="10.3636"
            fill="white"
            transform="translate(4.31836 4.31836)"
          />
        </clipPath>
      </defs>
    </svg>
  )
}
