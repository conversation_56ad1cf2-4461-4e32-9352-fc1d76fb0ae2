import type { IconsProps } from './IconTypes'

export const QuoteIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
      {...rest}
    >
      <path
        d="M10.5 21H13.5L15.5 17V11H9.5V17H12.5L10.5 21ZM18.5 21H21.5L23.5 17V11H17.5V17H20.5L18.5 21Z"
        fill={fill}
      />
    </svg>
  )
}
