import type { IconsProps } from './IconTypes'

export const CustomShieldIcon = ({
  width = '44',
  height = '44',
  fill = 'none',
  stroke = '#344054',
  strokeWidth = '1.66667',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 44 44"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g filter="url(#filter0_d_658_130196)">
        <path
          d="M2.5 9C2.5 4.85786 5.85786 1.5 10 1.5H34C38.1421 1.5 41.5 4.85786 41.5 9V33C41.5 37.1421 38.1421 40.5 34 40.5H10C5.85786 40.5 2.5 37.1421 2.5 33V9Z"
          stroke="#EAECF0"
          shapeRendering="crispEdges"
        />
        <path
          d="M21.4183 29.0125C21.6028 29.1202 21.695 29.174 21.8252 29.2019C21.9262 29.2236 22.0736 29.2236 22.1747 29.2019C22.3048 29.174 22.3971 29.1202 22.5816 29.0125C24.205 28.0655 28.6666 25.0905 28.6666 21.0001V17.8335C28.6666 16.9386 28.6666 16.4911 28.5286 16.1735C28.3884 15.8506 28.2487 15.6789 27.9612 15.4759C27.6783 15.2762 27.1238 15.1608 26.0147 14.9302C24.7923 14.6759 23.8535 14.2168 22.9952 13.5529C22.5837 13.2345 22.3779 13.0753 22.2169 13.0319C22.047 12.9861 21.9529 12.9861 21.783 13.0319C21.6219 13.0753 21.4162 13.2345 21.0046 13.5529C20.1464 14.2168 19.2076 14.6759 17.9852 14.9302C16.8761 15.1608 16.3215 15.2762 16.0386 15.4759C15.7511 15.6789 15.6114 15.8506 15.4712 16.1735C15.3333 16.4911 15.3333 16.9386 15.3333 17.8335V21.0001C15.3333 25.0905 19.7949 28.0655 21.4183 29.0125Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_658_130196"
          x="0"
          y="0"
          width="44"
          height="44"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="1" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.05 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_658_130196"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_658_130196"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  )
}
