import type { IconsProps } from './IconTypes'

export const ItalicsIcon = ({
  width = '33',
  height = '32',
  fill = '#101828',
  stroke = 'none',
  strokeWidth = 'none',
  className,
  ...rest
}: IconsProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 33 32"
      fill="none"
      {...rest}
    >
      <path
        d="M14.5 8V11H16.71L13.29 19H10.5V22H18.5V19H16.29L19.71 11H22.5V8H14.5Z"
        fill={fill}
      />
    </svg>
  )
}
