import type { IconsProps } from './IconTypes'

export const PaymentsPageIcon = ({
  width = '24',
  height = '24',
  fill = 'none',
  stroke = '#667085',
  strokeWidth = '2',
  ...rest
}: IconsProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path
        d="M20 20.0001V13.0001M12 20.0001V10.0001M4 20.0001L4 16.0001M13.4067 5.02763L18.5751 6.96579M10.7988 5.40104L5.20023 9.59995M21.0607 6.43946C21.6464 7.02525 21.6464 7.975 21.0607 8.56078C20.4749 9.14657 19.5251 9.14657 18.9393 8.56078C18.3536 7.975 18.3536 7.02525 18.9393 6.43946C19.5251 5.85368 20.4749 5.85368 21.0607 6.43946ZM5.06066 9.43946C5.64645 10.0252 5.64645 10.975 5.06066 11.5608C4.47487 12.1466 3.52513 12.1466 2.93934 11.5608C2.35355 10.975 2.35355 10.0252 2.93934 9.43946C3.52513 8.85368 4.47487 8.85368 5.06066 9.43946ZM13.0607 3.43946C13.6464 4.02525 13.6464 4.975 13.0607 5.56078C12.4749 6.14657 11.5251 6.14657 10.9393 5.56078C10.3536 4.975 10.3536 4.02525 10.9393 3.43946C11.5251 2.85368 12.4749 2.85368 13.0607 3.43946Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
