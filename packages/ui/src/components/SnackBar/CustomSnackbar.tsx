'use client'
import React, { useEffect, useState } from 'react'
import Snackbar from '@mui/material/Snackbar'
import Mu<PERSON><PERSON><PERSON>t, { AlertProps } from '@mui/material/Alert'
import { styled } from '@mui/material/styles'
import { Portal } from '@mui/material'

const Alert = React.forwardRef<HTMLDivElement, AlertProps>((props, ref) => (
  <MuiAlert elevation={6} ref={ref} variant="outlined" {...props} />
))

const CustomSnackbar = styled(Snackbar)(({ theme }) => ({
  marginTop: theme.spacing(15),
  zIndex: theme.zIndex.modal + 1,
  [theme.breakpoints.up('md')]: {
    marginBottom: theme.spacing(5),
  },
}))

interface LocalNotificationProps {
  clearMessage?: () => void
  clearNotification: () => void
  notification: string
  notificationType: ILocalNotificationType
}

type ILocalNotificationType = 'info' | 'success' | 'warning' | 'error'

export const LocalNotification: React.FC<LocalNotificationProps> = ({
  clearNotification,
  notification,
  notificationType,
}) => {
  const [open, setOpen] = useState(false)

  // Consider extracting the duration to a constants file if used across components
  const DEFAULT_DURATION = 10000
  const ERROR_DURATION = 6000

  useEffect(() => {
    if (notification) {
      setOpen(true)
    }
  }, [notification, notificationType])

  const handleClose = (_event?: unknown, reason?: string) => {
    if (reason === 'clickaway') {
      return
    }
    clearNotification()
    setOpen(false)
  }

  if (!notification) return null

  return (
    <Portal>
      <CustomSnackbar
        open={open}
        autoHideDuration={
          notificationType === 'error' ? ERROR_DURATION : DEFAULT_DURATION
        }
        onClose={handleClose}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        sx={{ zIndex: 2000 }}
        data-testid="notification-snackbar"
      >
        <Alert
          onClose={handleClose}
          severity={notificationType}
          sx={{
            border: 'none',
            borderLeft: '5px solid',
            borderColor: `${
              notificationType === 'success'
                ? '#12B76A'
                : notificationType === 'warning'
                  ? '#E16012'
                  : '#D92D20'
            }`,
            background: '#FFFFFF',
            px: '10px',
            py: '10px',
          }}
          data-testid="notification-alert"
        >
          {notification}
        </Alert>
      </CustomSnackbar>
    </Portal>
  )
}
