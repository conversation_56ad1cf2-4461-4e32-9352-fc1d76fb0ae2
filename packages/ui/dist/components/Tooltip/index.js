import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../../chunk-BBZEL7EG.js";

// src/components/Tooltip/CustomTooltips.tsx
import { styled, Tooltip, tooltipClasses } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var Tooltips = styled(
  (_a) => {
    var _b = _a, { className } = _b, props = __objRest(_b, ["className"]);
    return /* @__PURE__ */ jsx(Tooltip, __spreadProps(__spreadValues({}, props), { arrow: true, classes: { popper: className } }));
  }
)(() => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: "#FFF"
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#FFF",
    color: "#000000",
    boxShadow: "0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)",
    right: "50px",
    maxWidth: "110px",
    cursor: "pointer"
  }
}));
var SidebarTooltip = styled(
  (_a) => {
    var _b = _a, { className } = _b, props = __objRest(_b, ["className"]);
    return /* @__PURE__ */ jsx(Tooltip, __spreadProps(__spreadValues({}, props), { arrow: true, classes: { popper: className } }));
  }
)(() => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: "#2A3339"
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: "#2A3339",
    color: "#FFF",
    boxShadow: "0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)",
    right: "5px",
    minWidth: "90px",
    padding: "8px 12px",
    fontSize: "12px",
    lineHeight: "16px",
    fontWeight: 400
  }
}));
export {
  SidebarTooltip,
  Tooltips
};
