import * as react_jsx_runtime from 'react/jsx-runtime';
import { FC } from 'react';
import { ChipProps as ChipProps$1, ChipOwnProps } from '@mui/material';
import { StyledComponent } from '@emotion/styled';

interface ChipProps extends ChipProps$1 {
}
type SeverityStatus = 'success' | 'error' | 'info' | 'warn' | 'processing' | 'default' | 'neutral';
type IStatus = 'ACTIVE' | 'DEACTIVATED' | 'PENDING' | 'NEW' | 'REJECTED' | 'RESTRICTED';
type StatusChipProps = ChipProps & {
    status?: SeverityStatus;
};
declare const CustomStatusChip: StyledComponent<ChipOwnProps>;
declare const CustomSuccessChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomBlockedChip: StyledComponent<ChipProps>;
declare const CustomPendingOrgApprovalChip: FC<ChipProps>;
declare const CustomActiveBrokerChip: StyledComponent<ChipProps>;
declare const CustomInactiveBrokerChip: StyledComponent<ChipProps>;
declare const CustomPendingApprovalChip: StyledComponent<ChipProps>;
declare const StatusChip: ({ status, sx, ...props }: StatusChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomActiveChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomPaymentStatusChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomWarningChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomErrorChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomLoanSuccessChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomLoanFailedChip: (props: ChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomDrawerChip: FC<ChipProps>;
declare const CustomerStatusChip: StyledComponent<ChipProps>;
interface CustomerInfoChipProps {
    label: string;
    requests: string[];
}
declare const CustomerInfoChip: ({ label, requests, }: CustomerInfoChipProps) => react_jsx_runtime.JSX.Element;
declare const CustomChip: StyledComponent<ChipProps>;

export { type ChipProps, CustomActiveBrokerChip, CustomActiveChip, CustomBlockedChip, CustomChip, CustomDrawerChip, CustomErrorChip, CustomInactiveBrokerChip, CustomLoanFailedChip, CustomLoanSuccessChip, CustomPaymentStatusChip, CustomPendingApprovalChip, CustomPendingOrgApprovalChip, CustomStatusChip, CustomSuccessChip, CustomWarningChip, CustomerInfoChip, type CustomerInfoChipProps, CustomerStatusChip, type IStatus, type SeverityStatus, StatusChip };
