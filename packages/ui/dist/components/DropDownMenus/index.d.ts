import React__default, { FC, SetStateAction, ReactNode } from 'react';
import { Dayjs } from 'dayjs';

interface IFilterOption {
    key: string;
    value: string;
    label: string;
}
interface IDropDownMenuRadio {
    menuItems: string[];
    onClick: (item: string) => void;
    buttonVariant?: 'text' | 'outlined' | 'contained';
    buttonText: string;
}
interface ITableDropDownMenu {
    menuItems: {
        label: string;
        onClick: () => void;
    }[];
    disabled: boolean;
    buttonVariant?: 'text' | 'outlined' | 'contained';
    buttonText?: string;
}
interface IDotsDropDownMenu {
    menuItems: {
        label: string;
        onClick: () => void;
    }[];
}
declare const TableDropDownMenu: React__default.FC<ITableDropDownMenu>;
declare const DropDownMenuRadio: React__default.FC<IDropDownMenuRadio>;
interface IDropDownCheckBoxWithSearch {
    label: string;
    key: string;
    onClick: (selectedFilters: IFilterOption[]) => void;
    filters: IFilterOption[];
    selectedFilter?: IFilterOption[];
    open: boolean;
    handleOpenMenu: () => void;
    onClear?: boolean;
    setOpen: (open: boolean) => void;
}
declare const DropdownMenuCheckBoxWithSearch: React__default.FC<IDropDownCheckBoxWithSearch>;
interface IDropMenu {
    menuItems: Array<{
        label: string;
        id: string;
    }>;
    onSelect: (item: string) => void;
    buttonVariant?: 'text' | 'outlined' | 'contained';
    onButtonClick?: (setOpen: React__default.Dispatch<SetStateAction<boolean>>) => void;
    buttonText: string;
    loading?: boolean;
}
declare const DropDownMenu: FC<IDropMenu>;
declare const DotsDropdown: React__default.FC<IDotsDropDownMenu>;

interface IDatePicker {
    buttonText?: string;
    buttonIcon?: ReactNode;
    onApplyDateRange: (date: {
        start: Dayjs;
        end: Dayjs;
    }) => void;
    size?: 'small' | 'medium' | 'large';
    currentStartDate?: Dayjs;
    currentEndDate?: Dayjs;
}
declare const DateRangePicker: React__default.FC<IDatePicker>;

export { DateRangePicker, DotsDropdown, DropDownMenu, DropDownMenuRadio, DropdownMenuCheckBoxWithSearch, type IFilterOption, TableDropDownMenu };
