import * as react_jsx_runtime from 'react/jsx-runtime';

/**
 * <AUTHOR> on 20/09/2024
 */
interface IDecodeToken$1 {
    last_name: string;
    first_name: string;
    user_id: string;
    authorities: string[];
    sub: string;
    iat: number;
    exp: number;
    resources?: IResource$1[];
}
interface IResource$1 {
    resourceType: string;
    resourceIds: string[];
}
interface INavbarProps {
    profile: IDecodeToken$1;
    refreshToken: () => void;
}
declare const Navbar: ({ profile, refreshToken }: INavbarProps) => react_jsx_runtime.JSX.Element;

interface IDecodeToken {
    username?: string;
    last_name: string;
    first_name: string;
    user_id: string;
    authorities: string[];
    sub: string;
    iat: number;
    exp: number;
    resources?: IResource[];
}
interface IResource {
    resourceType: string;
    resourceIds: string[];
}
declare const InternalNavBar: ({ profile, refreshToken, refreshInterval, }: {
    profile: IDecodeToken;
    handleLogout?: () => void;
    refreshToken: () => void;
    refreshInterval?: number;
}) => react_jsx_runtime.JSX.Element;

export { type INavbarProps, InternalNavBar, Navbar };
