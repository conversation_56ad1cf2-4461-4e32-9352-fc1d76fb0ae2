import { DialogProps } from '@mui/material';
import { StyledComponent } from '@emotion/styled';
import React__default from 'react';

declare const CustomDialog: StyledComponent<DialogProps>;

type FileFormat = 'excel' | 'csv' | 'json' | 'pdf';
interface ExportPreferencesProps {
    open: boolean;
    onExport: (format: FileFormat) => void;
    onCancel: () => void;
    setOpen: React__default.Dispatch<React__default.SetStateAction<boolean>>;
    selectedIds: string[];
}
declare const ExportPreferences: React__default.FC<ExportPreferencesProps>;

interface reasonProps {
    open: boolean;
    title: string;
    buttonText: string;
    isLoading: boolean;
    setOpen: React__default.Dispatch<React__default.SetStateAction<boolean>> | ((val: boolean) => void);
    onClick?: (reasons: string) => void;
    buttonProps?: {
        color: string;
    };
}
declare const ReasonsDialog: React__default.FC<reasonProps>;

export { CustomDialog, ExportPreferences, type FileFormat, ReasonsDialog };
