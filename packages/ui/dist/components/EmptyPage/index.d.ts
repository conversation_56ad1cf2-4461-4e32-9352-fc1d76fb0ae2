import { FC, ReactNode } from 'react';

type EmptySearchAndFilterProps = {
    message?: string;
    onClick?: () => void;
    additionalText?: string;
};
declare const EmptySearchAndFilter: FC<EmptySearchAndFilterProps>;

type EmptyPageProps = {
    title?: string;
    message?: string;
    action?: ReactNode;
    bgUrl?: string;
};
declare const EmptyPage: React.FC<EmptyPageProps>;

export { EmptyPage, type EmptyPageProps, EmptySearchAndFilter, type EmptySearchAndFilterProps };
