import "../../chunk-BBZEL7EG.js";

// src/components/EmptyPage/EmptySearchAndFilter.tsx
import { Button, Stack, Typography } from "@mui/material";
import { jsx, jsxs } from "react/jsx-runtime";
var EmptySearchAndFilter = ({
  message = "No records found",
  onClick,
  additionalText
}) => {
  return /* @__PURE__ */ jsx(
    Stack,
    {
      sx: {
        height: "100%",
        width: "100%",
        justifyContent: "center",
        alignItems: "center",
        padding: "10px"
      },
      children: /* @__PURE__ */ jsxs(
        Stack,
        {
          sx: {
            width: "480px",
            height: "480px",
            backgroundImage: "url(/dashboard/combo.svg)",
            justifyContent: "flex-end",
            gap: "45px"
          },
          children: [
            /* @__PURE__ */ jsx(
              Stack,
              {
                sx: {
                  justifyContent: "center",
                  alignItems: "center"
                }
              }
            ),
            /* @__PURE__ */ jsxs(
              <PERSON>ack,
              {
                sx: {
                  justifyContent: "center",
                  alignItems: "center",
                  gap: "32px"
                },
                children: [
                  /* @__PURE__ */ jsxs(
                    Stack,
                    {
                      sx: {
                        justifyContent: "center",
                        alignItems: "center",
                        gap: "8px",
                        width: "65%"
                      },
                      children: [
                        /* @__PURE__ */ jsx(Typography, { variant: "subtitle1", children: message }),
                        additionalText && /* @__PURE__ */ jsx(
                          Typography,
                          {
                            variant: "subtitle3",
                            sx: {
                              textAlign: "center"
                            },
                            children: additionalText
                          }
                        )
                      ]
                    }
                  ),
                  onClick && /* @__PURE__ */ jsx(
                    Stack,
                    {
                      sx: {
                        justifyContent: "center",
                        flexDirection: "row",
                        gap: "20px",
                        marginTop: "20px"
                      },
                      children: /* @__PURE__ */ jsx(Button, { variant: "outlined", onClick, children: "Clear search" })
                    }
                  )
                ]
              }
            )
          ]
        }
      )
    }
  );
};

// src/components/EmptyPage/EmptyPage.tsx
import { Stack as Stack2, Typography as Typography2 } from "@mui/material";
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
var EmptyPage = ({
  title = "No records found",
  message = "Refresh page to reload data",
  action,
  bgUrl = "/dashboard/combo.svg"
}) => {
  return /* @__PURE__ */ jsx2(Stack2, { justifyContent: "center", alignItems: "center", padding: 3, children: /* @__PURE__ */ jsx2(
    Stack2,
    {
      sx: {
        width: "480px",
        height: "300px",
        justifyContent: "flex-end"
      },
      children: /* @__PURE__ */ jsxs2(
        Stack2,
        {
          justifyContent: "center",
          paddingBottom: 4,
          alignItems: "center",
          sx: { gap: "32px" },
          children: [
            /* @__PURE__ */ jsxs2(
              Stack2,
              {
                justifyContent: "center",
                alignItems: "center",
                sx: {
                  gap: "8px",
                  width: "80%"
                },
                children: [
                  /* @__PURE__ */ jsx2(Typography2, { fontWeight: "600", variant: "subtitle1", children: title }),
                  message && /* @__PURE__ */ jsx2(
                    Typography2,
                    {
                      variant: "subtitle3",
                      sx: {
                        textAlign: "center"
                      },
                      children: message
                    }
                  )
                ]
              }
            ),
            action && action
          ]
        }
      )
    }
  ) });
};
export {
  EmptyPage,
  EmptySearchAndFilter
};
