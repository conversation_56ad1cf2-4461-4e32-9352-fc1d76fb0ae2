import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default, { FC } from 'react';
import PropTypes from 'prop-types';
import { TabsOwnProps } from '@mui/material/Tabs';
import { TabOwnProps } from '@mui/material/Tab';

declare function TabPanel({ children, value, index, ...other }: {
    [x: string]: unknown;
    children: React__default.ReactNode;
    value: number;
    index: number;
}): react_jsx_runtime.JSX.Element;
declare namespace TabPanel {
    var propTypes: {
        children: PropTypes.Requireable<PropTypes.ReactNodeLike>;
        index: PropTypes.Validator<any>;
        value: PropTypes.Validator<any>;
    };
}
declare const AntTabs: FC<TabsOwnProps>;
declare const AntTab: FC<TabOwnProps>;
declare const SecondaryTabs: FC<TabsOwnProps>;
interface SecondaryTabProps extends TabOwnProps {
    label: string;
    key: string;
}
declare const SecondaryTab: FC<SecondaryTabProps>;

export { AntTab, AntTabs, SecondaryTab, SecondaryTabs, TabPanel };
