import {
  __objRest,
  __spreadProps,
  __spreadValues
} from "../../chunk-BBZEL7EG.js";

// src/components/Tabs/CustomTabs.tsx
import PropTypes from "prop-types";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { styled } from "@mui/material/styles";
import { jsx } from "react/jsx-runtime";
function TabPanel(_a) {
  var _b = _a, {
    children,
    value,
    index
  } = _b, other = __objRest(_b, [
    "children",
    "value",
    "index"
  ]);
  return /* @__PURE__ */ jsx(
    "div",
    __spreadProps(__spreadValues({
      role: "tabpanel",
      hidden: value !== index,
      id: `scrollable-auto-tabpanel-${index}`,
      "aria-labelledby": `scrollable-auto-tab-${index}`
    }, other), {
      children: value === index && /* @__PURE__ */ jsx("div", { children })
    })
  );
}
TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired
};
var AntTabs = styled(Tabs)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  marginRight: theme.spacing(3),
  marginBottom: theme.spacing(0),
  // borderBottom: '1px solid',
  borderBottomColor: theme.palette.primary.main,
  "& .MuiTabs-indicator": {
    backgroundColor: theme.palette.primary.main
  }
}));
var AntTab = styled((props) => /* @__PURE__ */ jsx(Tab, __spreadValues({ disableRipple: true }, props)))(({ theme }) => ({
  textTransform: "none",
  minWidth: 0,
  [theme.breakpoints.up("sm")]: {
    minWidth: 0
  },
  fontWeight: "500",
  color: "#667085",
  fontSize: "14px",
  lineHeight: "20px",
  marginRight: theme.spacing(1),
  "&:hover": {
    color: "#667085",
    opacity: 1
  },
  "&.Mui-selected": {
    color: "#2A3339",
    fontWeight: theme.typography.fontWeightBold,
    borderRadius: "5px"
  },
  "&.Mui-focusVisible": {
    backgroundColor: "#d1eaff"
  }
}));
var SecondaryTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  border: "1px solid var(--gray-300, #D0D5DD)",
  borderRadius: "4px",
  "& .MuiTabs-indicator": {
    backgroundColor: "transparent"
  }
}));
var SecondaryTab = styled(
  (props) => /* @__PURE__ */ jsx(Tab, __spreadValues({ disableRipple: true }, props))
)(({ theme }) => ({
  textTransform: "none",
  minWidth: 0,
  [theme.breakpoints.up("sm")]: {
    minWidth: 0
  },
  borderRight: "1px solid #D0D5DD",
  padding: "10px 36px",
  boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
  fontWeight: "500",
  fontSize: "16px",
  "&:hover": {
    color: "#667085",
    opacity: 1
  },
  "&.Mui-selected": {
    color: "#344054",
    background: "#D0D5DD"
  },
  "&.Mui-focusVisible": {
    backgroundColor: "#d1eaff"
  }
}));
export {
  AntTab,
  AntTabs,
  SecondaryTab,
  SecondaryTabs,
  TabPanel
};
