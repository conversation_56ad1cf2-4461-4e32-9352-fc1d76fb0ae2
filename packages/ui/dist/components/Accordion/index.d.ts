import * as react_jsx_runtime from 'react/jsx-runtime';

interface IAccordionProps {
    title?: string;
    variant?: string;
    color?: string;
    fontWeight?: string;
    categories: ICategory[];
    onChange: (newValue: {
        id: number;
        name: string;
    }, selectedOption: string | number) => void;
}
interface IDropDownProps {
    label: string;
    options: {
        id: number;
        name: string;
        description: string;
    }[];
    isOccupation?: boolean;
    onChange: (newValue: {
        id: number;
        name: string;
    }, categoryId: string | number) => void;
}
interface ICategory {
    id: string | number;
    label: string;
    options: {
        id: number;
        name: string;
        description?: string;
    }[];
}
declare const CustomAccordionWithDropdown: ({ title, variant, color, fontWeight, categories, onChange, }: IAccordionProps) => react_jsx_runtime.JSX.Element;
declare const DropDownComponent: ({ label, options, isOccupation, onChange, }: IDropDownProps) => react_jsx_runtime.JSX.Element;

export { CustomAccordionWithDropdown, DropDownComponent, type IAccordionProps, type ICategory, type IDropDownProps };
