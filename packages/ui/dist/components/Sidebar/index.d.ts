import * as react_jsx_runtime from 'react/jsx-runtime';
import { ReactElement, ReactNode } from 'react';

interface ISidebarConfigItem {
    path: string;
    title: string;
    id: string;
    module: string;
    icon: ReactElement;
    isProductionReady?: boolean;
    isUATReady?: boolean;
}

type SidebarProps = {
    sidebarConfig: ISidebarConfigItem[];
    sidebarCollapsed: (open: boolean) => void;
    bgColor?: string;
    linkHref?: string;
    footer?: ReactNode;
    logo?: ReactNode;
    collapsedLogo?: ReactNode;
};
declare const Sidebar: ({ sidebarConfig, sidebarCollapsed, bgColor, linkHref, footer, logo, collapsedLogo, }: SidebarProps) => react_jsx_runtime.JSX.Element;

export { type ISidebarConfigItem, Sidebar };
