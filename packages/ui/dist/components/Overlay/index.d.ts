import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default, { SetStateAction, FC } from 'react';

declare const IDView: ({ open, setDocumentViewer }: {
    open: boolean;
    setDocumentViewer: (props: {
        open: boolean;
        imageUrl: string;
    }) => void;
}) => react_jsx_runtime.JSX.Element;

interface IDialog {
    open: boolean;
    setOpen: React__default.Dispatch<SetStateAction<boolean>>;
    title: string;
    subtitle?: string;
    buttonText: string;
    buttonProps?: {
        color: string;
    };
    isLoading?: boolean;
    descriptionText: string;
    onClick?: (reasons: string[]) => void;
    reasons?: string[];
    concatReason?: boolean;
}
declare const CustomDialog: ({ open, setOpen, onClick, subtitle, buttonText, buttonProps, isLoading, descriptionText, title, reasons, concatReason, }: IDialog) => react_jsx_runtime.JSX.Element;

interface ConfirmCancelProps {
    open: boolean;
    onClose: () => void;
    onConfirmCancel: () => void;
    onConfirmSubmit: () => void;
}
declare const ConfirmCancelSave: FC<ConfirmCancelProps>;

export { ConfirmCancelSave, CustomDialog as Dialog, IDView };
