import {
  __spreadProps,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/Accordion/CustomAccordion.tsx
import { useState } from "react";
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Autocomplete,
  FormControl,
  Stack,
  TextField,
  Typography
} from "@mui/material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import { jsx, jsxs } from "react/jsx-runtime";
var CustomAccordionWithDropdown = ({
  title,
  variant,
  color,
  fontWeight,
  categories,
  onChange
}) => {
  return /* @__PURE__ */ jsx(Stack, { children: /* @__PURE__ */ jsxs(Accordion, { children: [
    /* @__PURE__ */ jsx(
      AccordionSummary,
      {
        expandIcon: /* @__PURE__ */ jsx(ExpandMoreIcon, {}),
        "aria-controls": "panel1-content",
        id: "panel1-header",
        children: /* @__PURE__ */ jsx(Typography, { sx: { color, fontWeight, variant }, children: title })
      }
    ),
    /* @__PURE__ */ jsx(AccordionDetails, { children: categories.map((category) => /* @__PURE__ */ jsx(
      DropDownComponent,
      {
        label: category.label,
        options: category.options.map((option) => {
          var _a;
          return __spreadProps(__spreadValues({}, option), {
            description: (_a = option.description) != null ? _a : ""
          });
        }),
        isOccupation: category.id === "occupation",
        onChange: (newValue) => onChange(newValue, category.id)
      },
      category.id
    )) })
  ] }) });
};
var DropDownComponent = ({
  label,
  options,
  isOccupation = false,
  onChange
}) => {
  const [selectedOption, setSelectedOption] = useState(null);
  const handleChange = (event, value) => {
    setSelectedOption(value);
    if (value) {
      onChange(value, value == null ? void 0 : value.id);
    }
  };
  return /* @__PURE__ */ jsx(FormControl, { fullWidth: true, margin: "normal", children: /* @__PURE__ */ jsx(
    Autocomplete,
    {
      value: selectedOption,
      onChange: handleChange,
      options,
      getOptionLabel: (option) => isOccupation ? option.description : option.name,
      renderOption: (props, option) => /* @__PURE__ */ jsx(
        "li",
        __spreadProps(__spreadValues({}, props), {
          title: isOccupation ? option.description : option.name,
          children: /* @__PURE__ */ jsx(
            "span",
            {
              style: {
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
                maxWidth: "13vw",
                display: "block"
              },
              children: isOccupation ? option.description : option.name
            }
          )
        })
      ),
      renderInput: (params) => /* @__PURE__ */ jsx(
        TextField,
        __spreadProps(__spreadValues({}, params), {
          label,
          placeholder: `Select ${label}`,
          variant: "outlined"
        })
      )
    }
  ) });
};

export {
  CustomAccordionWithDropdown,
  DropDownComponent
};
