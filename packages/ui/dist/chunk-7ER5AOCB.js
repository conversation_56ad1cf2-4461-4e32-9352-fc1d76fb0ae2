import {
  LoadingButton
} from "./chunk-QY6BZGO5.js";
import {
  ExportFinalIcon
} from "./chunk-YMEOZMFS.js";

// src/components/Dialogs/CustomDialog.tsx
import { styled } from "@mui/material/styles";
import { Dialog } from "@mui/material";
var CustomDialog = styled(Dialog)(
  ({ theme }) => ({
    "& .MuiDialogContent-root": {
      padding: theme.spacing(2)
    },
    "& .MuiDialogActions-root": {
      padding: theme.spacing(1)
    }
  })
);

// src/components/Dialogs/ExportPreferences.tsx
import { useState } from "react";
import {
  Box,
  Button,
  Dialog as Dialog2,
  Divider,
  FormControl,
  FormControlLabel,
  FormLabel,
  IconButton,
  RadioGroup as MuiRadioGroup,
  Radio,
  Stack,
  Typography
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { jsx, jsxs } from "react/jsx-runtime";
var ExportPreferences = ({
  open,
  onExport,
  onCancel,
  setOpen
}) => {
  const [selectedFormat, setSelectedFormat] = useState("excel");
  const formatOptions = [
    { label: "Excel", value: "excel", disabled: false },
    { label: "JSON", value: "json", disabled: true },
    { label: "PDF", value: "pdf", disabled: true },
    { label: "CSV", value: "csv", disabled: true }
  ];
  const handleClose = () => {
    setOpen(false);
  };
  const handleFormatChange = (event) => {
    setSelectedFormat(event.target.value);
  };
  return /* @__PURE__ */ jsx(Dialog2, { open, onClose: handleClose, maxWidth: "xs", fullWidth: true, children: /* @__PURE__ */ jsxs(
    Box,
    {
      sx: {
        bgcolor: "background.paper",
        border: "1px solid #ccc",
        borderRadius: "4px",
        boxShadow: 1,
        overflowY: {
          xs: "auto",
          md: "hidden"
        }
      },
      children: [
        /* @__PURE__ */ jsxs(
          Box,
          {
            sx: {
              bgcolor: "#F2F4F7",
              p: 2,
              borderBottom: "2px solid #e0e0e0",
              height: "42px",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between"
            },
            children: [
              /* @__PURE__ */ jsx(
                Typography,
                {
                  variant: "h6",
                  sx: { fontWeight: "bold", color: "#333", fontSize: "16px" },
                  children: "Export preferences"
                }
              ),
              /* @__PURE__ */ jsx(
                IconButton,
                {
                  "aria-label": "close",
                  onClick: handleClose,
                  sx: {
                    border: "0.8px solid #CBD5E1",
                    borderRadius: "50%",
                    padding: "4px",
                    "&:hover": {
                      backgroundColor: "#E2E8F0"
                    }
                  },
                  children: /* @__PURE__ */ jsx(Close, { fontSize: "small" })
                }
              )
            ]
          }
        ),
        /* @__PURE__ */ jsxs(Box, { sx: { p: 2 }, children: [
          /* @__PURE__ */ jsx(Stack, { direction: "column", spacing: 2, children: /* @__PURE__ */ jsxs(FormControl, { component: "fieldset", children: [
            /* @__PURE__ */ jsx(
              FormLabel,
              {
                component: "legend",
                sx: { fontSize: "1rem", color: "#2A3339", mb: 1 },
                children: "Data Format"
              }
            ),
            /* @__PURE__ */ jsx(
              MuiRadioGroup,
              {
                value: selectedFormat,
                onChange: handleFormatChange,
                children: formatOptions.map((option) => /* @__PURE__ */ jsx(
                  FormControlLabel,
                  {
                    value: option.value,
                    control: /* @__PURE__ */ jsx(Radio, { sx: { color: "#D0D5DD" } }),
                    label: option.label,
                    disabled: option.disabled
                  },
                  option.value
                ))
              }
            )
          ] }) }),
          /* @__PURE__ */ jsx(Divider, {})
        ] }),
        /* @__PURE__ */ jsxs(Box, { sx: { display: "flex", justifyContent: "flex-end", p: 2 }, children: [
          /* @__PURE__ */ jsx(
            Button,
            {
              variant: "outlined",
              onClick: onCancel,
              sx: {
                borderColor: "#D0D5DD",
                "&:hover": {
                  borderColor: "#D0D5DD",
                  backgroundColor: "rgba(208, 213, 221, 0.1)"
                }
              },
              fullWidth: true,
              children: "Cancel"
            }
          ),
          /* @__PURE__ */ jsx(
            Button,
            {
              variant: "contained",
              onClick: () => {
                onExport(selectedFormat);
              },
              sx: { ml: 1 },
              endIcon: /* @__PURE__ */ jsx(ExportFinalIcon, {}),
              disabled: !selectedFormat,
              fullWidth: true,
              children: "Export"
            }
          )
        ] })
      ]
    }
  ) });
};

// src/components/Dialogs/lmsReasonsDialog.tsx
import { useEffect, useState as useState2 } from "react";
import {
  Box as Box2,
  Button as Button2,
  Dialog as Dialog3,
  IconButton as IconButton2,
  TextField,
  Typography as Typography2
} from "@mui/material";
import { ChevronRight, Close as Close2 } from "@mui/icons-material";
import { jsx as jsx2, jsxs as jsxs2 } from "react/jsx-runtime";
var ReasonsDialog = ({
  open,
  title,
  buttonText,
  isLoading,
  onClick,
  setOpen,
  buttonProps
}) => {
  const [reason, setReason] = useState2("");
  const [hasTouched, setHasTouched] = useState2(false);
  const [error, setError] = useState2("");
  const handleKeyDown = (event) => {
    event.stopPropagation();
  };
  const handleClose = () => {
    setReason("");
    setHasTouched(false);
    setError("");
    setOpen(false);
  };
  useEffect(() => {
    if (!open) {
      setReason("");
      setHasTouched(false);
      setError("");
      setOpen(false);
    }
  }, [open]);
  const handleChange = (event) => {
    const input = event.target.value;
    setReason(input);
    setHasTouched(true);
    if (!validateReason(input)) {
      setError(
        "Reason must be at least 10 characters and contain only valid characters."
      );
    } else {
      setError("");
    }
  };
  const validateReason = (reason2) => {
    const trimmedReason = reason2.trim();
    return trimmedReason.length >= 10 && /^[\s\S]*$/.test(trimmedReason);
  };
  const isActionDisabled = reason.trim().length < 10 || !validateReason(reason);
  return /* @__PURE__ */ jsx2(Dialog3, { open, onClose: handleClose, maxWidth: "sm", fullWidth: true, children: /* @__PURE__ */ jsxs2(
    Box2,
    {
      sx: {
        bgcolor: "background.paper",
        border: "0.1vw solid #ccc",
        borderRadius: "0.3vw",
        boxShadow: 1,
        maxWidth: "100%",
        overflowY: {
          xs: "auto",
          md: "hidden"
        },
        padding: "1vw"
      },
      children: [
        /* @__PURE__ */ jsxs2(
          Box2,
          {
            sx: {
              height: "2vw",
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between"
            },
            children: [
              /* @__PURE__ */ jsx2(
                Typography2,
                {
                  variant: "h6",
                  sx: { fontWeight: "bold", color: "#333", fontSize: "1vw" },
                  children: title
                }
              ),
              /* @__PURE__ */ jsx2(
                IconButton2,
                {
                  "aria-label": "close",
                  onClick: handleClose,
                  sx: {
                    border: "0.8px solid #CBD5E1",
                    borderRadius: "50%",
                    padding: "0.2vw",
                    "&:hover": {
                      backgroundColor: "#E2E8F0"
                    }
                  },
                  children: /* @__PURE__ */ jsx2(Close2, { fontSize: "small" })
                }
              )
            ]
          }
        ),
        /* @__PURE__ */ jsx2(
          TextField,
          {
            sx: {
              marginTop: "1vw",
              marginBottom: "1vw"
            },
            variant: "filled",
            fullWidth: true,
            onKeyDown: handleKeyDown,
            multiline: true,
            rows: 4,
            placeholder: "Leave a comment:",
            value: reason,
            onChange: handleChange,
            error: Boolean(hasTouched && error),
            helperText: hasTouched && error ? error : ""
          }
        ),
        /* @__PURE__ */ jsx2(Typography2, { variant: "label1", children: "Once you click save changes, your updates will be submitted to your manager for verification." }),
        /* @__PURE__ */ jsxs2(
          Box2,
          {
            sx: {
              display: "flex",
              justifyContent: "flex-end",
              paddingTop: "1vw"
            },
            children: [
              /* @__PURE__ */ jsx2(
                Button2,
                {
                  variant: "outlined",
                  onClick: handleClose,
                  sx: {
                    color: "#ffffff",
                    marginRight: "1vw",
                    minWidth: "10vw",
                    background: buttonProps && buttonProps.color || "#EB0045",
                    border: `0.1vw solid ${buttonProps && buttonProps.color || "#EB0045"}`,
                    "&:hover": {
                      background: buttonProps && buttonProps.color || "#EB0045 ",
                      opacity: "0.8",
                      color: "#ffffff"
                    }
                  },
                  children: "Cancel"
                }
              ),
              isLoading ? /* @__PURE__ */ jsx2(LoadingButton, {}) : /* @__PURE__ */ jsx2(
                Button2,
                {
                  variant: "contained",
                  disabled: isActionDisabled,
                  onClick: () => {
                    onClick && onClick(reason);
                    setOpen(false);
                  },
                  sx: {
                    minWidth: "10vw"
                  },
                  endIcon: /* @__PURE__ */ jsx2(ChevronRight, {}),
                  children: buttonText
                }
              )
            ]
          }
        )
      ]
    }
  ) });
};

export {
  CustomDialog,
  ExportPreferences,
  ReasonsDialog
};
