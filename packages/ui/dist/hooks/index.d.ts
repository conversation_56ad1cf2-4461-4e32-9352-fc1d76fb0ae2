import * as next_dist_shared_lib_app_router_context_shared_runtime from 'next/dist/shared/lib/app-router-context.shared-runtime';

declare function useCustomRouter(): {
    pushWithTrailingSlash: (url: string) => void;
    back(): void;
    forward(): void;
    refresh(): void;
    push(href: string, options?: next_dist_shared_lib_app_router_context_shared_runtime.NavigateOptions): void;
    replace(href: string, options?: next_dist_shared_lib_app_router_context_shared_runtime.NavigateOptions): void;
    prefetch(href: string, options?: next_dist_shared_lib_app_router_context_shared_runtime.PrefetchOptions): void;
};

declare const useDebounce: (value: string, delay: number) => string;

export { useCustomRouter, useDebounce };
