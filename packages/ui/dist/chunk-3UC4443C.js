// src/components/Scrollbar/CustomScrollbar.tsx
import SimpleBar from "simplebar-react";
import "simplebar-react/dist/simplebar.min.css";
import { styled } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var StyledBar = styled(SimpleBar)(() => ({
  maxHeight: "100vh",
  "& .simplebar-scrollbar": {
    "&:before": {
      backgroundColor: "#667085"
    }
  },
  "& .simplebar-track.simplebar-vertical": {
    width: 15,
    backgroundColor: "#f1f2f6"
  }
}));
function CustomScrollbar({ children }) {
  return /* @__PURE__ */ jsx(StyledBar, { autoHide: false, children });
}

export {
  CustomScrollbar
};
