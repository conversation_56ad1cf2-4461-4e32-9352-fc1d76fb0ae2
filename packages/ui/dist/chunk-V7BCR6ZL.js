import {
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/SnackBar/CustomSnackbar.tsx
import React, { useEffect, useState } from "react";
import Snackbar from "@mui/material/Snackbar";
import <PERSON><PERSON><PERSON><PERSON>t from "@mui/material/Alert";
import { styled } from "@mui/material/styles";
import { Portal } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var Alert = React.forwardRef((props, ref) => /* @__PURE__ */ jsx(MuiAlert, __spreadValues({ elevation: 6, ref, variant: "outlined" }, props)));
var CustomSnackbar = styled(Snackbar)(({ theme }) => ({
  marginTop: theme.spacing(15),
  zIndex: theme.zIndex.modal + 1,
  [theme.breakpoints.up("md")]: {
    marginBottom: theme.spacing(5)
  }
}));
var LocalNotification = ({
  clearNotification,
  notification,
  notificationType
}) => {
  const [open, setOpen] = useState(false);
  const DEFAULT_DURATION = 1e4;
  const ERROR_DURATION = 6e3;
  useEffect(() => {
    if (notification) {
      setOpen(true);
    }
  }, [notification, notificationType]);
  const handleClose = (_event, reason) => {
    if (reason === "clickaway") {
      return;
    }
    clearNotification();
    setOpen(false);
  };
  if (!notification) return null;
  return /* @__PURE__ */ jsx(Portal, { children: /* @__PURE__ */ jsx(
    CustomSnackbar,
    {
      open,
      autoHideDuration: notificationType === "error" ? ERROR_DURATION : DEFAULT_DURATION,
      onClose: handleClose,
      anchorOrigin: { vertical: "top", horizontal: "right" },
      sx: { zIndex: 2e3 },
      "data-testid": "notification-snackbar",
      children: /* @__PURE__ */ jsx(
        Alert,
        {
          onClose: handleClose,
          severity: notificationType,
          sx: {
            border: "none",
            borderLeft: "5px solid",
            borderColor: `${notificationType === "success" ? "#12B76A" : notificationType === "warning" ? "#E16012" : "#D92D20"}`,
            background: "#FFFFFF",
            px: "10px",
            py: "10px"
          },
          "data-testid": "notification-alert",
          children: notification
        }
      )
    }
  ) });
};

export {
  LocalNotification
};
