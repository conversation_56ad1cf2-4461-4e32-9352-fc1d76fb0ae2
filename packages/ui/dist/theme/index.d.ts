import * as react_jsx_runtime from 'react/jsx-runtime';
import * as React from 'react';
import React__default from 'react';
import { Options, EmotionCache } from '@emotion/cache';

type ThemeType = 'main' | 'green' | 'brand';
declare function ThemeConfig({ children, themeType, }: {
    children: React__default.ReactNode;
    themeType?: ThemeType;
}): react_jsx_runtime.JSX.Element;

type NextAppDirEmotionCacheProviderProps = {
    /** This is the options passed to createCache() from 'import createCache from "@emotion/cache"' */
    options: Omit<Options, 'insertionPoint'>;
    /** By default <CacheProvider /> from 'import { CacheProvider } from "@emotion/react"' */
    CacheProvider?: (props: {
        value: EmotionCache;
        children: React.ReactNode;
    }) => React.JSX.Element | null;
    children: React.ReactNode;
};
declare function NextAppDirEmotionCacheProvider(props: NextAppDirEmotionCacheProviderProps): react_jsx_runtime.JSX.Element;

export { NextAppDirEmotionCacheProvider, type NextAppDirEmotionCacheProviderProps, ThemeConfig, type ThemeType };
