import {
  CustomStatusChip
} from "./chunk-3LNZ6JLA.js";
import {
  CustomCheckBox
} from "./chunk-7KHKZLSB.js";
import {
  CustomDialog
} from "./chunk-JJ7TFTNR.js";
import {
  IconClose,
  PdfIcon,
  SecurityDetailsIcon,
  UploadIcon
} from "./chunk-YMEOZMFS.js";
import {
  CustomSkeleton
} from "./chunk-QY6BZGO5.js";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-BBZEL7EG.js";

// src/components/Input/Autocomplete.tsx
import { Autocomplete, styled, TextField } from "@mui/material";
import { jsx } from "react/jsx-runtime";
var emails = [
  {
    firstName: "Caldewood",
    lastName: "Alikula",
    email: "<EMAIL>"
  },
  {
    firstName: "Caldewood",
    lastName: "<PERSON>ku<PERSON>",
    email: "<EMAIL>"
  },
  {
    firstName: "Caldewood",
    lastName: "<PERSON><PERSON><PERSON>",
    email: "<EMAIL>"
  },
  {
    firstName: "Caldewood",
    lastName: "Alikula",
    email: "<EMAIL>"
  },
  {
    firstName: "Caldewood",
    lastName: "Alikula",
    email: "<EMAIL>"
  },
  {
    firstName: "Caldewood",
    lastName: "Alikula",
    email: "<EMAIL>"
  },
  {
    firstName: "Caldewood",
    lastName: "Alikula",
    email: "<EMAIL>"
  }
];
var ListItem = styled("li")(
  () => `


`
);
var AutoComplete = ({
  label,
  options,
  value,
  optionLabel,
  fullWidth,
  onChange,
  renderListItem,
  isOptionEqualToValue
}) => {
  return /* @__PURE__ */ jsx(
    Autocomplete,
    {
      fullWidth,
      onChange,
      value,
      sx: {
        width: fullWidth ? "100%" : ""
      },
      getOptionLabel: (option) => String(option[optionLabel]),
      isOptionEqualToValue,
      renderOption: (params, option) => /* @__PURE__ */ jsx(ListItem, __spreadProps(__spreadValues({}, params), { sx: { width: "100%" }, children: renderListItem(option) })),
      renderInput: (params) => {
        return /* @__PURE__ */ jsx(
          TextField,
          __spreadProps(__spreadValues({}, params), {
            inputProps: __spreadValues({}, params.inputProps),
            label
          })
        );
      },
      options
    }
  );
};

// src/components/Input/CustomerModuleSearchFilter.tsx
import {
  Button as Button3,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem as MenuItem2,
  Select,
  Stack as Stack3,
  Typography as Typography3
} from "@mui/material";
import { useState as useState3 } from "react";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import CloseIcon from "@mui/icons-material/Close";
import {
  FilterListOffRounded,
  FilterListRounded,
  KeyboardArrowDownRounded as KeyboardArrowDownRounded3
} from "@mui/icons-material";

// src/components/Input/CustomSearchInput.tsx
import {
  Box,
  InputBase,
  OutlinedInput,
  styled as styled2
} from "@mui/material";
import { jsx as jsx2, jsxs } from "react/jsx-runtime";
var CustomSearchInput = styled2(OutlinedInput)(
  ({ theme, width = "400px", bgColor, height = 40 }) => ({
    width,
    height,
    transition: theme.transitions.create(["box-shadow", "width"], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.shorter
    }),
    boxShadow: "0px 1px 2px 0px #1018280D",
    "&.Mui-focused": {
      boxShadow: "0px 0px 0px 2px rgba(211, 207, 220, 0.15)"
    },
    "& fieldset": {
      borderRadius: "8px",
      border: `2px solid ${bgColor || "#D0D5DD"} !important`
    }
  })
);
CustomSearchInput.displayName = "CustomSearchInput";
var CustomSearchBox = ({
  startAdornment,
  endAdornment,
  onKeyDown,
  value,
  onChange,
  placeholder,
  sx
}) => {
  return /* @__PURE__ */ jsxs(
    Box,
    {
      sx: __spreadValues({
        display: "flex",
        alignItems: "center",
        backgroundColor: "#FFFFFF",
        border: "1px solid #D0D5DD",
        borderRadius: "4px",
        padding: "4px 8px"
      }, sx),
      children: [
        startAdornment && /* @__PURE__ */ jsx2(Box, { sx: { marginRight: 1 }, children: startAdornment }),
        /* @__PURE__ */ jsx2(
          InputBase,
          {
            fullWidth: true,
            placeholder,
            value,
            onChange: (e) => onChange == null ? void 0 : onChange(e),
            onKeyDown,
            sx: { flex: 1 }
          }
        ),
        endAdornment && /* @__PURE__ */ jsx2(Box, { sx: { marginLeft: 1 }, children: endAdornment })
      ]
    }
  );
};

// src/components/DropDownMenus/DropDown.tsx
import { KeyboardArrowDownRounded, SearchRounded } from "@mui/icons-material";
import {
  Box as Box2,
  Button,
  Chip,
  ClickAwayListener,
  FormControlLabel,
  Grow,
  IconButton,
  List,
  MenuItem,
  MenuList,
  Paper,
  Popper,
  Radio,
  Stack,
  Typography
} from "@mui/material";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from "react";
import { sentenceCase } from "tiny-case";
import { jsx as jsx3, jsxs as jsxs2 } from "react/jsx-runtime";
var TableDropDownMenu = (params) => {
  const { menuItems, buttonText, buttonVariant, disabled } = params;
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef(null);
  const handleToggle = () => {
    setOpen((prevOpen2) => !prevOpen2);
  };
  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };
  const handleListKeyDown = () => {
  };
  const prevOpen = React.useRef(open);
  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current.focus();
    }
    prevOpen.current = open;
  }, [open]);
  return /* @__PURE__ */ jsxs2(Box2, { children: [
    /* @__PURE__ */ jsx3(
      Button,
      {
        variant: buttonVariant || "outlined",
        sx: {
          height: "40px",
          width: "107px",
          textWrap: "nowrap",
          padding: "9px 28px",
          borderRadius: "6px",
          border: "1px solid  #AAADB0",
          background: buttonVariant === "contained" ? "#000A12" : "#FFF",
          boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
          gap: 0
        },
        ref: anchorRef,
        id: "composition-button",
        "aria-controls": open ? "composition-menu" : void 0,
        "aria-expanded": open ? "true" : void 0,
        "aria-haspopup": "true",
        onClick: handleToggle,
        endIcon: /* @__PURE__ */ jsx3(KeyboardArrowDownRounded, {}),
        disabled,
        children: /* @__PURE__ */ jsx3(Typography, { sx: {}, children: buttonText || "Action" })
      }
    ),
    /* @__PURE__ */ jsx3(
      Popper,
      {
        open,
        anchorEl: anchorRef.current,
        role: void 0,
        placement: "bottom-start",
        transition: true,
        disablePortal: true,
        sx: {
          zIndex: "2000"
        },
        children: ({ TransitionProps, placement }) => /* @__PURE__ */ jsx3(
          Grow,
          __spreadProps(__spreadValues({}, TransitionProps), {
            style: {
              transformOrigin: placement === "bottom-start" ? "left top" : "left bottom"
            },
            children: /* @__PURE__ */ jsx3(
              Paper,
              {
                sx: {
                  minWidth: "156px",
                  marginTop: "22px",
                  borderRadius: "8px",
                  border: "1px solid #ECECEC",
                  background: "#FFF",
                  boxShadow: "0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)"
                },
                children: /* @__PURE__ */ jsx3(ClickAwayListener, { onClickAway: handleClose, children: /* @__PURE__ */ jsx3(
                  MenuList,
                  {
                    autoFocusItem: open,
                    id: "composition-menu",
                    "aria-labelledby": "composition-button",
                    onKeyDown: handleListKeyDown,
                    children: menuItems.map((item) => /* @__PURE__ */ jsx3(MenuItem, { onClick: item.onClick, children: item.label }, item.label))
                  }
                ) })
              }
            )
          })
        )
      }
    )
  ] });
};
var DropDownMenuRadio = ({
  menuItems,
  onClick,
  buttonVariant,
  buttonText
}) => {
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef(null);
  const [selectedIndex, setSelectedIndex] = React.useState(0);
  const handleToggle = () => {
    setOpen((prevOpen2) => !prevOpen2);
  };
  const handleClose = () => {
    setOpen(false);
    onClick("");
  };
  const handleMenuItemClick = (index) => {
    setSelectedIndex(index);
  };
  const prevOpen = React.useRef(open);
  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current.focus();
    }
    prevOpen.current = open;
  }, [open]);
  return /* @__PURE__ */ jsxs2(Box2, { children: [
    /* @__PURE__ */ jsx3(
      Button,
      {
        sx: {
          height: "40px",
          width: "156px",
          textWrap: "nowrap",
          padding: "9px 28px",
          borderRadius: "4px",
          border: "1px solid  #AAADB0",
          background: buttonVariant === "contained" ? "#000A12" : "#FFF",
          boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
        },
        variant: buttonVariant,
        ref: anchorRef,
        id: "composition-button",
        "aria-controls": open ? "composition-menu" : void 0,
        "aria-expanded": open ? "true" : void 0,
        "aria-haspopup": "true",
        onClick: handleToggle,
        endIcon: /* @__PURE__ */ jsx3(KeyboardArrowDownRounded, {}),
        children: /* @__PURE__ */ jsx3(
          Typography,
          {
            variant: "label1",
            sx: {
              color: buttonVariant === "contained" ? "#FFF" : "#000A12"
            },
            children: buttonText
          }
        )
      }
    ),
    /* @__PURE__ */ jsx3(
      Popper,
      {
        open,
        anchorEl: anchorRef.current,
        role: void 0,
        placement: "bottom-start",
        transition: true,
        disablePortal: true,
        sx: {
          zIndex: "2000"
        },
        children: ({ TransitionProps, placement }) => /* @__PURE__ */ jsx3(
          Grow,
          __spreadProps(__spreadValues({}, TransitionProps), {
            style: {
              transformOrigin: placement === "bottom-start" ? "left top" : "left bottom"
            },
            children: /* @__PURE__ */ jsxs2(
              Paper,
              {
                sx: {
                  minWidth: "156px",
                  marginTop: "22px",
                  padding: "0px 0px 12px 0px",
                  borderRadius: "8px",
                  border: "1px solid #ECECEC",
                  background: "#FFF",
                  boxShadow: "0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)"
                },
                children: [
                  /* @__PURE__ */ jsx3(
                    ClickAwayListener,
                    {
                      onClickAway: () => {
                        setOpen(false);
                      },
                      children: /* @__PURE__ */ jsxs2(Stack, { children: [
                        " ",
                        /* @__PURE__ */ jsx3(
                          MenuList,
                          {
                            autoFocusItem: open,
                            id: "composition-menu",
                            "aria-labelledby": "composition-button",
                            children: menuItems.map((item, index) => /* @__PURE__ */ jsxs2(
                              MenuItem,
                              {
                                onClick: () => handleMenuItemClick(index),
                                children: [
                                  /* @__PURE__ */ jsx3(
                                    Radio,
                                    {
                                      checked: index === selectedIndex,
                                      onChange: () => {
                                        setSelectedIndex(index);
                                      },
                                      value: item,
                                      name: "radio-buttons-group"
                                    }
                                  ),
                                  /* @__PURE__ */ jsx3(Chip, { label: item })
                                ]
                              },
                              item
                            ))
                          }
                        )
                      ] })
                    }
                  ),
                  /* @__PURE__ */ jsxs2(
                    Box2,
                    {
                      sx: {
                        padding: "0px 20px",
                        display: "flex",
                        gap: "13px"
                      },
                      children: [
                        /* @__PURE__ */ jsx3(
                          Button,
                          {
                            variant: "outlined",
                            sx: {
                              height: "34px",
                              width: "96px"
                            },
                            onClick: handleClose,
                            children: "Cancel"
                          }
                        ),
                        /* @__PURE__ */ jsx3(
                          Button,
                          {
                            variant: "contained",
                            sx: {
                              height: "34px",
                              width: "96px"
                            },
                            onClick: () => {
                              onClick(menuItems[selectedIndex]);
                              setOpen(false);
                            },
                            children: "Apply"
                          }
                        )
                      ]
                    }
                  )
                ]
              }
            )
          })
        )
      }
    )
  ] });
};
var useDropdownMenuCheckBoxWithSearch = (filters, initialSelectedFilters) => {
  const [search, setSearch] = useState("");
  const [selectedFilters, setSelectedFilters] = useState(
    initialSelectedFilters
  );
  const filtered = useMemo(() => {
    if (search === "") return filters;
    return filters.filter(
      (option) => option.label.toLowerCase().includes(search.toLowerCase())
    );
  }, [filters, search]);
  const handleSelect = useCallback((selectedFilter) => {
    setSelectedFilters(
      (prev) => prev.some((filter) => filter.key === selectedFilter.key) ? prev.filter((filter) => filter.key !== selectedFilter.key) : [...prev, selectedFilter]
    );
  }, []);
  const handleSearch = useCallback((search2) => {
    setSearch(search2);
  }, []);
  return {
    search,
    selectedFilters,
    filtered,
    setSelectedFilters,
    handleSelect,
    handleSearch
  };
};
var SearchInput = ({ value, onChange }) => /* @__PURE__ */ jsx3(
  CustomSearchInput,
  {
    startAdornment: /* @__PURE__ */ jsx3(SearchRounded, { sx: { color: "black" } }),
    placeholder: "Search",
    sx: {
      width: "100%",
      "&.Mui-focused": {
        width: "100%",
        boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
      }
    },
    value,
    onChange: (e) => onChange(e.target.value)
  }
);
var FilterList = ({ filters, selectedFilters, onSelect }) => /* @__PURE__ */ jsx3(
  MenuList,
  {
    sx: {
      padding: 0,
      margin: 0,
      width: "100%",
      height: "100%",
      overflowY: "auto",
      "&::-webkit-scrollbar": {
        width: "6px",
        marginLeft: 2
      },
      "&::-webkit-scrollbar-track": {
        backgroundColor: "lightgray transparent",
        padding: "0px 4px"
      },
      "&::-webkit-scrollbar-thumb": {
        backgroundColor: "darkgray",
        borderRadius: "10px"
      }
    },
    children: filters.map((option) => /* @__PURE__ */ jsx3(
      MenuItem,
      {
        sx: { margin: 0, padding: "6px", width: "100%" },
        children: /* @__PURE__ */ jsx3(
          FormControlLabel,
          {
            control: /* @__PURE__ */ jsx3(
              CustomCheckBox,
              {
                sx: { minWidth: "40px", height: "30px", padding: "2px 8px" },
                checked: selectedFilters.some(
                  (filter) => filter.key === option.key
                ),
                onChange: () => onSelect(option)
              }
            ),
            label: /* @__PURE__ */ jsx3(
              CustomStatusChip,
              {
                sx: { height: "30px" },
                label: /* @__PURE__ */ jsx3(Typography, { variant: "label2", children: option.label })
              }
            )
          }
        )
      },
      option.key
    ))
  }
);
var DropdownMenuCheckBoxWithSearch = ({
  label,
  onClick,
  filters,
  selectedFilter = [],
  open,
  handleOpenMenu,
  setOpen
}) => {
  const anchorRef = useRef(null);
  const {
    search,
    selectedFilters,
    filtered,
    setSelectedFilters,
    handleSelect,
    handleSearch
  } = useDropdownMenuCheckBoxWithSearch(filters, selectedFilter);
  const handleApply = () => {
    onClick(selectedFilters);
    handleOpenMenu();
  };
  useEffect(() => {
    setSelectedFilters(selectedFilter || []);
  }, [selectedFilter]);
  const handleCancel = () => {
    setSelectedFilters([]);
    onClick([]);
    handleOpenMenu();
  };
  return /* @__PURE__ */ jsxs2(Box2, { children: [
    /* @__PURE__ */ jsxs2(
      Button,
      {
        variant: "outlined",
        endIcon: /* @__PURE__ */ jsx3(KeyboardArrowDownRounded, {}),
        ref: anchorRef,
        onClick: handleOpenMenu,
        "aria-controls": open ? "composition-menu" : void 0,
        "aria-expanded": open ? "true" : void 0,
        "aria-haspopup": "true",
        id: "composition-button",
        children: [
          /* @__PURE__ */ jsx3(Typography, { variant: "label1", sx: { textWrap: "nowrap" }, children: selectedFilters.length > 0 ? `${label}: ${selectedFilters.slice(0, 2).map((filt) => filt.label).join(", ")}` : label }),
          selectedFilters.length > 2 && /* @__PURE__ */ jsx3(
            Chip,
            {
              label: `+ ${selectedFilters.length - 2}`
            }
          )
        ]
      }
    ),
    /* @__PURE__ */ jsx3(Popper, { open, anchorEl: anchorRef.current, placement: "bottom-start", children: /* @__PURE__ */ jsxs2(
      Paper,
      {
        sx: {
          minWidth: "259px",
          maxHeight: "50vh",
          padding: "5% 4% 4% 4%",
          background: "#FFF",
          borderRadius: "6px",
          display: "flex",
          flexDirection: "column",
          gap: "14px",
          justifyContent: "flex-start",
          alignItems: "flex-start"
        },
        children: [
          /* @__PURE__ */ jsx3(SearchInput, { value: search, onChange: handleSearch }),
          /* @__PURE__ */ jsx3(
            FilterList,
            {
              filters: filtered,
              selectedFilters,
              onSelect: handleSelect
            }
          ),
          /* @__PURE__ */ jsx3(
            ClickAwayListener,
            {
              onClickAway: () => {
              },
              children: /* @__PURE__ */ jsx3(Stack, { children: /* @__PURE__ */ jsxs2(
                Stack,
                {
                  sx: {
                    flexDirection: "row",
                    justifyContent: "space-between",
                    width: "100%",
                    margin: 0,
                    padding: 0,
                    gap: "5%"
                  },
                  children: [
                    /* @__PURE__ */ jsx3(
                      Button,
                      {
                        variant: "outlined",
                        sx: {
                          width: "100%",
                          height: "34px",
                          border: "1px solid #AAADB0"
                        },
                        onClick: handleCancel,
                        children: "Cancel"
                      }
                    ),
                    /* @__PURE__ */ jsx3(
                      Button,
                      {
                        type: "button",
                        variant: "contained",
                        sx: { width: "100%", height: "34px" },
                        onClick: handleApply,
                        children: "Apply"
                      }
                    )
                  ]
                }
              ) })
            }
          )
        ]
      }
    ) })
  ] }, label);
};
var DropDownMenu = ({
  menuItems,
  onSelect,
  onButtonClick,
  buttonVariant,
  buttonText,
  loading
}) => {
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef(null);
  const handleToggle = () => {
    onButtonClick && onButtonClick(setOpen);
  };
  const handleMenuItemClick = (item) => {
    onSelect && onSelect(item);
    setOpen(false);
  };
  const prevOpen = React.useRef(open);
  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current.focus();
    }
    prevOpen.current = open;
  }, [open]);
  return /* @__PURE__ */ jsxs2(Box2, { children: [
    /* @__PURE__ */ jsx3(
      Button,
      {
        sx: {
          height: "40px",
          width: "156px",
          textWrap: "nowrap",
          padding: "9px 28px",
          borderRadius: "4px",
          border: "1px solid  #AAADB0",
          background: buttonVariant === "contained" ? "#000A12" : "#FFF",
          boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
        },
        variant: buttonVariant,
        ref: anchorRef,
        id: "composition-button",
        "aria-controls": open ? "composition-menu" : void 0,
        "aria-expanded": open ? "true" : void 0,
        "aria-haspopup": "true",
        onClick: handleToggle,
        endIcon: /* @__PURE__ */ jsx3(KeyboardArrowDownRounded, {}),
        children: /* @__PURE__ */ jsx3(
          Typography,
          {
            variant: "label1",
            sx: {
              color: buttonVariant === "contained" ? "#FFF" : "#000A12"
            },
            children: buttonText
          }
        )
      }
    ),
    /* @__PURE__ */ jsx3(
      Popper,
      {
        open,
        anchorEl: anchorRef.current,
        role: void 0,
        placement: "bottom-start",
        transition: true,
        disablePortal: true,
        sx: {
          zIndex: "2000"
        },
        children: ({ TransitionProps, placement }) => /* @__PURE__ */ jsx3(
          Grow,
          __spreadProps(__spreadValues({}, TransitionProps), {
            style: {
              transformOrigin: placement === "bottom-start" ? "left top" : "left bottom"
            },
            children: /* @__PURE__ */ jsx3(
              Paper,
              {
                sx: {
                  minWidth: "156px",
                  marginTop: "6px",
                  padding: "0px 0px 12px 0px",
                  borderRadius: "8px",
                  border: "1px solid #ECECEC",
                  background: "#FFF",
                  boxShadow: "0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)"
                },
                children: /* @__PURE__ */ jsx3(
                  ClickAwayListener,
                  {
                    onClickAway: () => {
                      setOpen(false);
                    },
                    children: /* @__PURE__ */ jsxs2(Stack, { children: [
                      " ",
                      /* @__PURE__ */ jsxs2(
                        List,
                        {
                          id: "composition-menu",
                          "aria-labelledby": "composition-button",
                          sx: {
                            overflowY: "auto",
                            maxHeight: "40vh",
                            display: "flex",
                            flexDirection: "column",
                            padding: "6px"
                          },
                          children: [
                            (!loading || loading === void 0) && menuItems.map((item, index) => /* @__PURE__ */ jsx3(
                              Button,
                              {
                                sx: {
                                  height: "40px",
                                  textWrap: "nowrap",
                                  padding: "10px"
                                },
                                onClick: () => handleMenuItemClick(item.id),
                                children: /* @__PURE__ */ jsx3(Typography, { children: sentenceCase(item.label) })
                              },
                              item.id || index
                            )),
                            loading && Array.from({ length: 5 }).map((_, index) => /* @__PURE__ */ jsx3(
                              CustomSkeleton,
                              {
                                variant: "rectangular",
                                sx: {
                                  height: "40px",
                                  width: "200px",
                                  marginTop: "10px"
                                }
                              },
                              index
                            ))
                          ]
                        }
                      )
                    ] })
                  }
                )
              }
            )
          })
        )
      }
    )
  ] });
};
var DotsDropdown = ({ menuItems }) => {
  const [open, setOpen] = React.useState(false);
  const anchorRef = React.useRef(null);
  const handleToggle = () => {
    setOpen((prevOpen2) => !prevOpen2);
  };
  const handleClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };
  const handleListKeyDown = () => {
  };
  const prevOpen = React.useRef(open);
  useEffect(() => {
    if (prevOpen.current && !open) {
      anchorRef.current.focus();
    }
    prevOpen.current = open;
  }, [open]);
  return /* @__PURE__ */ jsxs2(Box2, { children: [
    /* @__PURE__ */ jsx3(
      IconButton,
      {
        sx: {
          "&:hover": { backgroundColor: "#fff" }
        },
        ref: anchorRef,
        id: "composition-button",
        "aria-controls": open ? "composition-menu" : void 0,
        "aria-expanded": open ? "true" : void 0,
        "aria-haspopup": "true",
        onClick: handleToggle,
        children: /* @__PURE__ */ jsx3(SecurityDetailsIcon, {})
      }
    ),
    /* @__PURE__ */ jsx3(
      Popper,
      {
        open,
        anchorEl: anchorRef.current,
        role: void 0,
        placement: "bottom-start",
        transition: true,
        disablePortal: true,
        sx: {
          zIndex: "2000"
        },
        children: ({ TransitionProps, placement }) => /* @__PURE__ */ jsx3(
          Grow,
          __spreadProps(__spreadValues({}, TransitionProps), {
            style: {
              transformOrigin: placement === "bottom-start" ? "left top" : "left bottom"
            },
            children: /* @__PURE__ */ jsx3(
              Paper,
              {
                elevation: 0,
                sx: {
                  minWidth: "2vw",
                  marginTop: "0.5rem",
                  padding: "0",
                  borderRadius: "8px",
                  border: "1px solid #ECECEC",
                  background: "#FFF",
                  boxShadow: "0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)"
                },
                children: /* @__PURE__ */ jsx3(ClickAwayListener, { onClickAway: handleClose, children: /* @__PURE__ */ jsx3(
                  MenuList,
                  {
                    autoFocusItem: open,
                    id: "composition-menu",
                    "aria-labelledby": "composition-button",
                    onKeyDown: handleListKeyDown,
                    children: menuItems.map((item) => /* @__PURE__ */ jsx3(MenuItem, { onClick: item.onClick, children: item.label }, item.label))
                  }
                ) })
              }
            )
          })
        )
      }
    )
  ] });
};

// src/components/DropDownMenus/DateRangePicker.tsx
import {
  ChevronLeftRounded,
  ChevronRightRounded,
  KeyboardArrowDownRounded as KeyboardArrowDownRounded2
} from "@mui/icons-material";
import {
  Box as Box3,
  Button as Button2,
  ClickAwayListener as ClickAwayListener2,
  Fade,
  IconButton as IconButton2,
  Paper as Paper2,
  Popper as Popper2,
  Stack as Stack2,
  TextField as TextField2,
  Typography as Typography2
} from "@mui/material";
import { CalendarIcon } from "@mui/x-date-pickers";
import dayjs from "dayjs";
import React2, { useState as useState2 } from "react";
import { Fragment, jsx as jsx4, jsxs as jsxs3 } from "react/jsx-runtime";
var DateRangePicker = ({
  buttonText,
  buttonIcon,
  onApplyDateRange,
  size,
  currentStartDate,
  currentEndDate
}) => {
  const anchorRef = React2.useRef(null);
  const [open, setOpen] = useState2(false);
  const [currentDate, setCurrentDate] = useState2(dayjs());
  const [startDate, setstartDate] = useState2(
    currentStartDate || dayjs().subtract(1, "month")
  );
  const [endDate, setEndDate] = useState2(
    currentEndDate || dayjs()
  );
  const [calenderView, setCalenderView] = useState2(true);
  const [monthView, setMonthView] = useState2(false);
  const isStartOrEndDate = (day) => {
    return day.isSame(startDate, "day") || day.isSame(endDate, "day");
  };
  const isDateBetween = (day) => {
    if (!startDate || !endDate) return false;
    return day.isAfter(startDate, "day") && day.isBefore(endDate, "day");
  };
  const handleDateClicked = (clickedDate) => {
    if (!startDate || startDate && endDate) {
      setstartDate(clickedDate.startOf("day"));
      setEndDate(null);
    } else if (startDate && !endDate) {
      if (clickedDate.isBefore(startDate, "day")) {
        setEndDate(startDate.endOf("day"));
        setstartDate(clickedDate.startOf("day"));
      } else {
        setEndDate(clickedDate.endOf("day"));
      }
    }
  };
  const setNextMonth = () => {
    setCurrentDate(currentDate.clone().add(1, "month"));
  };
  const setPrevMonth = () => {
    setCurrentDate(currentDate.clone().subtract(1, "month"));
  };
  const handleClick = () => {
    setOpen((prev) => !prev);
  };
  const CalenderView = () => {
    return /* @__PURE__ */ jsxs3(
      Box3,
      {
        sx: {
          display: "grid",
          gridTemplateColumns: "repeat(7, 0fr)",
          gap: "0px",
          padding: "0px",
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center"
        },
        children: [
          ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].map((day) => /* @__PURE__ */ jsx4(
            Box3,
            {
              sx: {
                textAlign: "center",
                fontWeight: "bold"
              },
              children: day
            },
            day
          )),
          generateCalendar()
        ]
      }
    );
  };
  const MonthView = () => {
    return /* @__PURE__ */ jsx4(
      Box3,
      {
        sx: {
          display: "grid",
          gridTemplateColumns: "repeat(3, 0fr)",
          gap: "0px",
          padding: "3px",
          width: "100%",
          height: "200px",
          justifyContent: "space-between",
          alignItems: "center"
        },
        children: [
          "January",
          "February",
          "March",
          "April",
          "May",
          "June",
          "July",
          "August",
          "September",
          "October",
          "November",
          "December"
        ].map((month, index) => /* @__PURE__ */ jsx4(
          Button2,
          {
            sx: {
              textAlign: "center",
              fontWeight: "bold",
              color: "black"
            },
            variant: "text",
            onClick: () => {
              setCurrentDate(currentDate.month(index));
              setMonthView(false);
              setCalenderView(true);
            },
            children: month
          },
          month
        ))
      }
    );
  };
  const YearView = () => {
    return /* @__PURE__ */ jsx4(
      Box3,
      {
        sx: {
          display: "grid",
          gridTemplateColumns: "repeat(4, 0fr)",
          gap: "0px",
          padding: "3px",
          width: "100%",
          height: "200px",
          justifyContent: "space-between",
          alignItems: "center",
          overflowY: "auto",
          overflowX: "hidden",
          marginLeft: "2px",
          "&::-webkit-scrollbar": {
            width: "6px"
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "lightgray transparent",
            padding: "0px 4px"
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "darkgray",
            borderRadius: "10px"
          }
        },
        children: Array.from({ length: 70 }, (_, k) => dayjs().year() - k).map(
          (year) => /* @__PURE__ */ jsx4(
            Button2,
            {
              sx: {
                textAlign: "center",
                fontWeight: "bold",
                color: "black"
              },
              variant: "text",
              onClick: () => {
                setCurrentDate(currentDate.year(year));
                setMonthView(true);
                setCalenderView(false);
              },
              children: year
            },
            year
          )
        )
      }
    );
  };
  const DateBox = ({
    key,
    sx,
    date,
    onClick
  }) => {
    return /* @__PURE__ */ jsx4(Box3, { sx, onClick, children: /* @__PURE__ */ jsx4(
      Box3,
      {
        style: {
          padding: 0,
          height: "100%",
          width: "100%",
          backgroundColor: isStartOrEndDate(date) ? "black" : "inherit",
          borderRadius: isStartOrEndDate(date) ? "50%" : "none",
          color: isStartOrEndDate(date) ? "white" : "inherit",
          display: "flex",
          justifyContent: "center",
          alignItems: "center"
        },
        children: date.format("D")
      }
    ) }, key);
  };
  const generateCalendar = () => {
    const startOfMonth = currentDate.startOf("month").startOf("week");
    const endOfMonth = currentDate.endOf("month").endOf("week");
    const days = [];
    let day = startOfMonth;
    while (day.isBefore(endOfMonth.add(1, "day"))) {
      days.push(day);
      day = day.add(1, "day");
    }
    return days.map((day2) => /* @__PURE__ */ jsx4(
      DateBox,
      {
        sx: {
          textAlign: "center",
          padding: isStartOrEndDate(day2) ? 0 : "10px",
          width: "50px",
          height: "50px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          cursor: "pointer",
          "&:hover": {
            backgroundColor: "#f5f5f5"
          },
          // borderRadius: '5px solid',
          borderRadiusLeft: day2.isAfter(startDate, "D") ? "50%" : "none",
          borderRadiusRight: day2.isBefore(endDate, "D") ? "50%" : "none",
          color: isStartOrEndDate(day2) ? "white" : "inherit",
          backgroundColor: isStartOrEndDate(day2) ? "#f5f5f5" : isDateBetween(day2) ? "#f5f5f5" : "inherit"
        },
        onClick: () => handleDateClicked(day2),
        date: day2
      },
      day2.format("YYYY-MM-DD")
    ));
  };
  return /* @__PURE__ */ jsxs3(Fragment, { children: [
    /* @__PURE__ */ jsx4(
      Button2,
      {
        variant: "outlined",
        endIcon: buttonText ? buttonIcon ? buttonIcon : /* @__PURE__ */ jsx4(KeyboardArrowDownRounded2, {}) : /* @__PURE__ */ jsx4(Fragment, {}),
        ref: anchorRef,
        "aria-controls": open ? "date-range-menu" : void 0,
        "aria-expanded": open ? "true" : void 0,
        "aria-haspopup": "true",
        id: "date-range-button",
        onClick: handleClick,
        sx: {
          height: "42px",
          minWidth: "130px",
          backgroundColor: "#FFF",
          display: "flex",
          borderRadius: "6px",
          border: "1.5px solid #D0D5DD"
        },
        children: buttonText ? /* @__PURE__ */ jsxs3(
          Typography2,
          {
            sx: {
              color: "black",
              fontWeight: "400"
            },
            children: [
              buttonText,
              " "
            ]
          }
        ) : /* @__PURE__ */ jsxs3(
          Box3,
          {
            sx: {
              display: "flex",
              gap: "4px",
              alignItems: "center"
            },
            children: [
              /* @__PURE__ */ jsx4(CalendarIcon, {}),
              " ",
              /* @__PURE__ */ jsx4(
                Typography2,
                {
                  sx: {
                    textWrap: "nowrap"
                  },
                  children: startDate && endDate ? `${startDate.format("MMM D, YYYY")} - ${endDate.format(
                    "MMM D, YYYY"
                  )}` : "Select Date Range"
                }
              )
            ]
          }
        )
      }
    ),
    /* @__PURE__ */ jsx4(
      Popper2,
      {
        id: "date-range-menu",
        placement: "bottom-start",
        anchorEl: anchorRef.current,
        open,
        transition: true,
        disablePortal: true,
        sx: {
          zIndex: "2000"
        },
        children: ({ TransitionProps }) => /* @__PURE__ */ jsx4(Fade, __spreadProps(__spreadValues({}, TransitionProps), { timeout: 350, children: /* @__PURE__ */ jsxs3(
          Paper2,
          {
            sx: {
              maxWidth: size === "small" ? "300px" : "400px",
              marginTop: "-150px"
            },
            children: [
              /* @__PURE__ */ jsx4(
                ClickAwayListener2,
                {
                  onClickAway: () => {
                    setOpen(false);
                  },
                  children: /* @__PURE__ */ jsxs3(Stack2, { children: [
                    " ",
                    /* @__PURE__ */ jsxs3(
                      Box3,
                      {
                        component: "div",
                        sx: {
                          padding: "20px 28px",
                          display: "flex",
                          flexDirection: "column",
                          gap: "16px",
                          borderBottom: "1px solid #ccc"
                        },
                        children: [
                          /* @__PURE__ */ jsxs3(
                            Box3,
                            {
                              sx: {
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                gap: "4px"
                              },
                              children: [
                                /* @__PURE__ */ jsx4(
                                  TextField2,
                                  {
                                    size: "small",
                                    sx: {
                                      width: "100%"
                                    },
                                    value: startDate ? startDate.format("MMMM D, YYYY") : "",
                                    inputProps: { readonly: true },
                                    InputLabelProps: { shrink: true }
                                  }
                                ),
                                " ",
                                "-",
                                /* @__PURE__ */ jsx4(
                                  TextField2,
                                  {
                                    size: "small",
                                    sx: {
                                      width: "100%"
                                    },
                                    value: endDate ? endDate.format("MMMM D, YYYY") : "",
                                    inputProps: { readonly: true },
                                    InputLabelProps: { shrink: true }
                                  }
                                )
                              ]
                            }
                          ),
                          /* @__PURE__ */ jsxs3(
                            Box3,
                            {
                              sx: {
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                                gap: "4px"
                              },
                              children: [
                                /* @__PURE__ */ jsx4(
                                  IconButton2,
                                  {
                                    onClick: setPrevMonth,
                                    sx: {
                                      padding: "8px",
                                      width: "28px",
                                      height: "28px"
                                    },
                                    children: /* @__PURE__ */ jsx4(
                                      ChevronLeftRounded,
                                      {
                                        sx: {
                                          fontSize: "20px"
                                        }
                                      }
                                    )
                                  }
                                ),
                                /* @__PURE__ */ jsxs3(
                                  Box3,
                                  {
                                    sx: {
                                      display: "flex",
                                      gap: "4px",
                                      color: "#000000"
                                    },
                                    children: [
                                      /* @__PURE__ */ jsx4(
                                        Button2,
                                        {
                                          sx: {
                                            paddingRight: "4px",
                                            color: "black"
                                          },
                                          onClick: () => {
                                            setMonthView(true);
                                            setCalenderView(false);
                                          },
                                          children: currentDate.format("MMMM D,")
                                        }
                                      ),
                                      /* @__PURE__ */ jsx4(
                                        Button2,
                                        {
                                          sx: {
                                            paddingLeft: "0",
                                            color: "black"
                                          },
                                          onClick: () => {
                                            setMonthView(false);
                                            setCalenderView(false);
                                          },
                                          children: currentDate.format("YYYY")
                                        }
                                      )
                                    ]
                                  }
                                ),
                                /* @__PURE__ */ jsx4(
                                  IconButton2,
                                  {
                                    onClick: setNextMonth,
                                    sx: {
                                      padding: "8px",
                                      width: "28px",
                                      height: "28px"
                                    },
                                    children: /* @__PURE__ */ jsx4(
                                      ChevronRightRounded,
                                      {
                                        sx: {
                                          fontSize: "20px"
                                        }
                                      }
                                    )
                                  }
                                )
                              ]
                            }
                          ),
                          calenderView ? /* @__PURE__ */ jsx4(CalenderView, {}) : monthView ? /* @__PURE__ */ jsx4(MonthView, {}) : /* @__PURE__ */ jsx4(YearView, {})
                        ]
                      }
                    )
                  ] })
                }
              ),
              /* @__PURE__ */ jsxs3(
                Box3,
                {
                  sx: {
                    display: "flex",
                    justifyContent: "space-between",
                    padding: "16px",
                    gap: "16px"
                  },
                  children: [
                    /* @__PURE__ */ jsx4(
                      Button2,
                      {
                        variant: "outlined",
                        sx: {
                          padding: "8px 16px",
                          textTransform: "none",
                          width: "100%",
                          border: "1px solid "
                        },
                        onClick: () => {
                          setEndDate(null);
                          setstartDate(null);
                          setOpen(false);
                        },
                        children: "Cancel"
                      }
                    ),
                    /* @__PURE__ */ jsx4(
                      Button2,
                      {
                        variant: "contained",
                        sx: {
                          padding: "8px 16px",
                          textTransform: "none",
                          width: "100%"
                        },
                        onClick: () => {
                          if (startDate !== null && endDate !== null && endDate.isAfter(startDate)) {
                            const dateRange = {
                              start: startDate,
                              end: endDate
                            };
                            onApplyDateRange(dateRange);
                            setOpen(false);
                          }
                        },
                        children: "Apply"
                      }
                    )
                  ]
                }
              )
            ]
          }
        ) }))
      }
    )
  ] });
};

// src/components/Input/CustomerModuleSearchFilter.tsx
import { Fragment as Fragment2, jsx as jsx5, jsxs as jsxs4 } from "react/jsx-runtime";
var CustomerModuleSearchFilterBox = (props) => {
  const {
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    searchByValues,
    onFilterChange,
    setSearchByValue
  } = props;
  const [selectedFilters, setSelectedFilters] = useState3({});
  const [checkBoxValue, setCheckBoxValue] = useState3([]);
  const [openDropDownCheckbox, setOpenDropDownCheckbox] = useState3(null);
  const handleFilterChange = (filterName, selectedOption) => {
    const newFilters = __spreadProps(__spreadValues({}, selectedFilters), {
      [filterName]: selectedOption
    });
    setSelectedFilters(newFilters);
    onFilterChange(newFilters);
  };
  const handleFilterOpen = () => {
    setOpenFilter(!openFilter);
  };
  const handleClearAll = () => {
    setOpenFilter(false);
    setSelectedFilters({});
    onFilterChange({});
    setCheckBoxValue([]);
    setOpenDropDownCheckbox(null);
  };
  const handleDropdownCheckboxOpen = (filterName) => {
    setOpenDropDownCheckbox((prev) => prev === filterName ? null : filterName);
  };
  return /* @__PURE__ */ jsxs4(
    Stack3,
    {
      sx: {
        flexDirection: "column"
      },
      children: [
        /* @__PURE__ */ jsxs4(
          Stack3,
          {
            sx: {
              flexDirection: "row",
              justifyContent: "flex-start",
              gap: "10px"
            },
            children: [
              searchByValues && setSearchByValue ? /* @__PURE__ */ jsx5(
                SearchByValuesBox,
                {
                  searchValue,
                  handleSearch,
                  searchByValues,
                  setSearchByValue
                }
              ) : /* @__PURE__ */ jsx5(
                CustomSearchInput,
                {
                  value: searchValue,
                  onChange: handleSearch,
                  placeholder: "Search",
                  startAdornment: /* @__PURE__ */ jsx5(InputAdornment, { position: "start", children: /* @__PURE__ */ jsx5(SearchOutlinedIcon, { sx: { color: "text.disabled" } }) })
                }
              ),
              /* @__PURE__ */ jsx5(
                Button3,
                {
                  variant: "outlined",
                  sx: {
                    height: "40px",
                    gap: 0,
                    boxShadow: openFilter ? "0px 1px 2px 0px rgba(16, 24, 40, 0.05)" : "0px 1px 2px 0px #1018280D",
                    border: openFilter ? "1.5px solid #555C61" : "1.5px solid #D0D5DD"
                  },
                  startIcon: !openFilter ? /* @__PURE__ */ jsx5(FilterListOffRounded, {}) : /* @__PURE__ */ jsx5(FilterListRounded, {}),
                  onClick: handleFilterOpen,
                  children: /* @__PURE__ */ jsx5(
                    Typography3,
                    {
                      sx: {
                        textWrap: "nowrap"
                      },
                      children: openFilter ? "Hide Filters" : "Show Filters"
                    }
                  )
                }
              )
            ]
          }
        ),
        /* @__PURE__ */ jsxs4(
          Stack3,
          {
            sx: {
              display: openFilter ? "flex" : "none",
              flexDirection: "row",
              gap: "10px",
              mt: "10px"
            },
            children: [
              /* @__PURE__ */ jsx5(
                Button3,
                {
                  variant: "text",
                  sx: {
                    width: "6vw",
                    color: "#555C61",
                    textWrap: "nowrap",
                    height: "36px",
                    boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
                    borderRadius: "6px",
                    gap: 1,
                    fontSize: "15px",
                    fontWeight: 500
                  },
                  onClick: handleClearAll,
                  endIcon: /* @__PURE__ */ jsx5(CloseIcon, {}),
                  children: "Clear All"
                }
              ),
              filters.map((filter) => {
                return filter.type === "select" ? /* @__PURE__ */ jsxs4(
                  FormControl,
                  {
                    sx: {
                      width: "40%"
                    },
                    children: [
                      /* @__PURE__ */ jsx5(
                        InputLabel,
                        {
                          id: "demo-simple-select-outlined-label",
                          sx: {
                            background: "#fcfcfc",
                            padding: "0 4px",
                            marginLeft: "-4px"
                          },
                          children: filter.filterName
                        }
                      ),
                      /* @__PURE__ */ jsx5(
                        Select,
                        {
                          labelId: "demo-simple-select-outlined-label",
                          id: "demo-simple-select-outlined",
                          sx: {
                            border: "1px solid #AAADB0",
                            height: "40px"
                          },
                          value: selectedFilters[filter.filterName] || "",
                          onChange: (e) => handleFilterChange(
                            filter.filterName,
                            e.target.value
                          ),
                          children: filter.options.map((option) => /* @__PURE__ */ jsx5(MenuItem2, { value: option.label, children: option.label }, option.key))
                        },
                        filter.filterName
                      )
                    ]
                  },
                  filter.filterName
                ) : filter.type === "dropdown/checkbox" ? /* @__PURE__ */ jsx5(
                  DropdownMenuCheckBoxWithSearch,
                  {
                    label: filter.filterName,
                    onClick: (selectedFilters2) => {
                      if (selectedFilters2.length >= 0) {
                        handleFilterChange(
                          filter.filterName,
                          selectedFilters2.map((filt) => filt.key)
                        );
                        setCheckBoxValue(selectedFilters2);
                      }
                    },
                    filters: filter.options,
                    selectedFilter: checkBoxValue,
                    open: openDropDownCheckbox === filter.filterName,
                    handleOpenMenu: () => handleDropdownCheckboxOpen(filter.filterName),
                    setOpen: (open) => {
                      if (!open) handleDropdownCheckboxOpen("");
                    }
                  },
                  filter.filterName
                ) : filter.type === "dropdown/single" ? /* @__PURE__ */ jsx5(
                  DropdownMenu,
                  {
                    filter,
                    onSelected: (str) => {
                      handleFilterChange(filter.filterName, str);
                    },
                    selectedFilterValue: selectedFilters[filter.filterName]
                  }
                ) : /* @__PURE__ */ jsx5(Fragment2, {});
              })
            ]
          }
        )
      ]
    }
  );
};
var DropdownMenu = ({
  filter,
  onSelected,
  selectedFilterValue
}) => {
  const disabledValue = "none";
  return /* @__PURE__ */ jsxs4(
    FormControl,
    {
      sx: {
        width: "20%"
      },
      size: "small",
      children: [
        /* @__PURE__ */ jsx5(
          InputLabel,
          {
            id: filter.filterName,
            sx: {
              background: "#FFFFFF"
            },
            children: filter.filterName
          }
        ),
        /* @__PURE__ */ jsxs4(
          Select,
          {
            sx: {
              px: "5%",
              justifyContent: "center"
            },
            labelId: filter.filterName,
            value: selectedFilterValue != null ? selectedFilterValue : disabledValue,
            onChange: (e) => onSelected(e.target.value),
            IconComponent: () => /* @__PURE__ */ jsx5(KeyboardArrowDownRounded3, {}),
            children: [
              /* @__PURE__ */ jsx5(MenuItem2, { disabled: true, value: disabledValue, children: "All" }),
              filter.options.map((opt, index) => /* @__PURE__ */ jsx5(MenuItem2, { value: opt.value, children: opt.label }, index))
            ]
          }
        )
      ]
    }
  );
};
var SearchByValuesBox = ({
  searchValue,
  handleSearch,
  searchByValues,
  setSearchByValue
}) => {
  var _a, _b;
  const [selectedSearchBy, setSelectedSearchBy] = useState3(
    (_b = (_a = searchByValues[0]) == null ? void 0 : _a.value) != null ? _b : ""
  );
  const handleSelect = (e) => {
    setSelectedSearchBy(e.target.value);
    setSearchByValue(e.target.value);
  };
  return /* @__PURE__ */ jsxs4(Stack3, { direction: "row", children: [
    /* @__PURE__ */ jsx5(
      Select,
      {
        fullWidth: true,
        size: "small",
        onChange: handleSelect,
        SelectDisplayProps: {
          style: { display: "flex", justifyContent: "space-between" }
        },
        IconComponent: () => /* @__PURE__ */ jsx5(KeyboardArrowDownRounded3, {}),
        value: selectedSearchBy,
        sx: {
          width: "10vw",
          ".MuiInputBase-input.MuiOutlinedInput-input ": {
            py: "2px !important"
          },
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
          borderRight: "none"
        },
        children: searchByValues.map((item) => /* @__PURE__ */ jsx5(MenuItem2, { value: item.value, children: `Search By ${item.label}` }, item.value))
      }
    ),
    /* @__PURE__ */ jsx5(
      CustomSearchInput,
      {
        value: searchValue,
        onChange: handleSearch,
        placeholder: "Search",
        sx: {
          "& fieldset": {
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
            borderLeft: "none !important"
          }
        },
        endAdornment: /* @__PURE__ */ jsx5(InputAdornment, { position: "start", children: /* @__PURE__ */ jsx5(SearchOutlinedIcon, { sx: { color: "#555C61" } }) })
      }
    )
  ] });
};

// src/components/Input/CustomFormControlLabel.tsx
import { FormControlLabel as FormControlLabel2, styled as styled3 } from "@mui/material";
import { jsx as jsx6 } from "react/jsx-runtime";
var CustomFormControlLabel = styled3((props) => /* @__PURE__ */ jsx6(FormControlLabel2, __spreadValues({}, props)))(
  ({ theme }) => ({
    "&.MuiFormControlLabel-root": {
      justifyContent: "space-between",
      borderRadius: "4px",
      border: "1px solid #E3E4E4",
      margin: 0,
      paddingRight: "0.8rem"
    },
    ".MuiFormControlLabel-label": {
      color: theme.palette.primary.main,
      fontWeight: 400,
      marginLeft: "10px",
      fontSize: "15px"
    }
  })
);

// src/components/Input/CustomSearchByInput.tsx
import { KeyboardArrowDownRounded as KeyboardArrowDownRounded4 } from "@mui/icons-material";
import {
  Box as Box4,
  Button as Button4,
  Chip as Chip2,
  ClickAwayListener as ClickAwayListener3,
  Grow as Grow2,
  InputBase as InputBase2,
  MenuItem as MenuItem3,
  MenuList as MenuList2,
  Paper as Paper3,
  Popper as Popper3,
  Typography as Typography4
} from "@mui/material";
import React4 from "react";
import { jsx as jsx7, jsxs as jsxs5 } from "react/jsx-runtime";
var CustomSearchByInput = ({
  searchByDropDownItems,
  width,
  onChange,
  value,
  onKeyDown,
  onSearchBySelect,
  placeholder
}) => {
  var _a, _b;
  const [openSub, setOpenSub] = React4.useState(false);
  const anchorSubRef = React4.useRef(null);
  const [searchBy, setSearchBy] = React4.useState({
    label: (_a = searchByDropDownItems[0]) == null ? void 0 : _a.label,
    value: (_b = searchByDropDownItems[0]) == null ? void 0 : _b.value
  });
  const [buttonWidth, setButtonWidth] = React4.useState(0);
  const handleToggleSub = (e) => {
    setOpenSub((prevOpenSub2) => !prevOpenSub2);
    setButtonWidth(e.currentTarget.offsetWidth);
  };
  const handleStatusClose = (event) => {
    if (anchorSubRef.current && anchorSubRef.current.contains(event.target)) {
      return;
    }
    setOpenSub(false);
  };
  function handleListKeyDown(event) {
    if (event.key === "Tab") {
      event.preventDefault();
      setOpenSub(false);
    } else if (event.key === "Escape") {
      setOpenSub(false);
    }
  }
  const prevOpenSub = React4.useRef(openSub);
  React4.useEffect(() => {
    if (prevOpenSub.current === true && openSub === false) {
      anchorSubRef.current.focus();
    }
    prevOpenSub.current = openSub;
  }, [openSub]);
  React4.useEffect(() => {
    onSearchBySelect && onSearchBySelect(searchBy);
  }, [searchBy]);
  return /* @__PURE__ */ jsxs5(
    Paper3,
    {
      component: "form",
      elevation: 0,
      sx: {
        p: "0px",
        display: "flex",
        alignItems: "center",
        width,
        gap: "19px",
        height: "42px",
        justifyContent: "space-between",
        border: "1px solid #D0D5DD !important",
        borderRadius: "4px",
        boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
        backgroundColor: "#FFFFFF"
      },
      children: [
        /* @__PURE__ */ jsxs5(Box4, { children: [
          /* @__PURE__ */ jsx7(
            Button4,
            {
              sx: {
                height: "42px",
                minWidth: "156px",
                textWrap: "nowrap",
                padding: "9px 28px",
                borderRadius: "4px 0px  0px 4px",
                border: "1px solid  #AAADB0",
                background: " #FFF",
                boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)",
                "&.active": {
                  backgroundColor: "#EDEEEE"
                }
              },
              variant: "outlined",
              ref: anchorSubRef,
              id: "composition-button",
              "aria-controls": openSub ? "composition-menu" : void 0,
              "aria-expanded": openSub ? "true" : void 0,
              "aria-haspopup": "true",
              onClick: handleToggleSub,
              endIcon: /* @__PURE__ */ jsx7(KeyboardArrowDownRounded4, {}),
              children: /* @__PURE__ */ jsx7(Typography4, { variant: "label1", children: `Search by ${searchBy.label.split(" ")[0]}` })
            }
          ),
          /* @__PURE__ */ jsx7(
            Popper3,
            {
              open: openSub,
              anchorEl: anchorSubRef.current,
              role: void 0,
              placement: "bottom-start",
              transition: true,
              disablePortal: true,
              sx: {
                zIndex: "2000"
              },
              children: ({ TransitionProps, placement }) => /* @__PURE__ */ jsx7(
                Grow2,
                __spreadProps(__spreadValues({}, TransitionProps), {
                  style: {
                    transformOrigin: placement === "bottom-start" ? "left top" : "left bottom"
                  },
                  children: /* @__PURE__ */ jsx7(
                    Paper3,
                    {
                      sx: {
                        width: buttonWidth,
                        marginTop: "22px",
                        padding: "0px 0px 12px 0px",
                        borderRadius: "8px",
                        border: "1px solid #ECECEC",
                        background: " #FFF",
                        boxShadow: "0px 12.514px 15.017px -2.503px rgba(16, 24, 40, 0.08), 0px 5.006px 5.006px -2.503px rgba(16, 24, 40, 0.03)"
                      },
                      children: /* @__PURE__ */ jsx7(ClickAwayListener3, { onClickAway: handleStatusClose, children: /* @__PURE__ */ jsx7(
                        MenuList2,
                        {
                          autoFocusItem: openSub,
                          id: "composition-menu",
                          "aria-labelledby": "composition-button",
                          onKeyDown: handleListKeyDown,
                          children: searchByDropDownItems.map((item, index) => /* @__PURE__ */ jsx7(
                            MenuItem3,
                            {
                              onClick: (e) => {
                                handleStatusClose(e);
                                const value2 = {
                                  label: item.label,
                                  value: item.value
                                };
                                setSearchBy(value2);
                              },
                              children: /* @__PURE__ */ jsx7(
                                Chip2,
                                {
                                  label: item.label,
                                  sx: {
                                    padding: "2px 8px",
                                    background: "#E3E4E4",
                                    height: "20px"
                                  }
                                }
                              )
                            },
                            index
                          ))
                        }
                      ) })
                    }
                  )
                })
              )
            }
          )
        ] }),
        /* @__PURE__ */ jsx7(
          InputBase2,
          {
            id: "search-box",
            sx: { border: "none", width: "100%", boxShadow: "none" },
            placeholder,
            inputProps: { "aria-label": "search component" },
            value,
            onChange: (e) => {
              onChange && onChange(e.target.value);
            },
            onKeyDown: (e) => {
              if (e.key === "Enter") {
                e.preventDefault();
                onKeyDown && onKeyDown(e);
              }
            }
          }
        )
      ]
    }
  );
};

// src/components/Input/InputField.tsx
import { InputBase as InputBase3, styled as styled4 } from "@mui/material";
var InputField = styled4(InputBase3)(
  () => ({
    padding: "12px 8px",
    height: "40px",
    width: "381px",
    display: "flex",
    alignItems: "center",
    border: "1px solid var(--Color-Stroke-Stroke-2, #E3E4E4)",
    borderRadius: "4px",
    background: "#FFF",
    boxShadow: "0px 1px 2px 0px rgba(16, 24, 40, 0.05)"
  })
);

// src/components/Input/UploadDocument.tsx
import { DeleteOutlineOutlined, VisibilityOutlined } from "@mui/icons-material";
import CloseIcon2 from "@mui/icons-material/Close";
import {
  Button as Button5,
  DialogContent,
  DialogTitle,
  IconButton as IconButton3,
  Paper as Paper4,
  Stack as Stack4,
  Typography as Typography5
} from "@mui/material";
import { useCallback as useCallback2, useState as useState4 } from "react";
import { Fragment as Fragment3, jsx as jsx8, jsxs as jsxs6 } from "react/jsx-runtime";
var UploadDocumentForm = ({
  name,
  initialFileName,
  initialFile,
  onFileUpload
}) => {
  const [fileName, setFileName] = useState4(initialFileName || "");
  const [file, setFile] = useState4(
    initialFile || null || ""
  );
  const [dragOver, setDragOver] = useState4(false);
  const handleDragOver = useCallback2((event) => {
    event.preventDefault();
    setDragOver(true);
  }, []);
  const handleDragLeave = useCallback2((event) => {
    event.preventDefault();
    setDragOver(false);
  }, []);
  const handleDrop = useCallback2(
    (event) => {
      event.preventDefault();
      setDragOver(false);
      if (event.dataTransfer.files && event.dataTransfer.files[0]) {
        setFileName(event.dataTransfer.files[0].name);
        setFile(event.dataTransfer.files[0]);
        onFileUpload(event.dataTransfer.files[0]);
      }
    },
    [onFileUpload]
  );
  const handleChange = useCallback2(
    (event) => {
      if (event.target.files && event.target.files[0]) {
        setFileName(event.target.files[0].name);
        setFile(event.target.files[0]);
        onFileUpload(event.target.files[0]);
      }
    },
    [onFileUpload]
  );
  const handleRemoveFile = () => {
    setFileName("");
    setFile(null);
    onFileUpload(null);
  };
  return /* @__PURE__ */ jsxs6(
    Paper4,
    {
      onDragOver: handleDragOver,
      elevation: dragOver ? 4 : 0,
      onDragLeave: handleDragLeave,
      onDrop: handleDrop,
      sx: {
        borderRadius: "12px",
        display: "flex",
        border: "1px solid #EAECF0",
        background: "#FFF",
        cursor: "pointer",
        px: "4%",
        py: "2%",
        alignItems: "center",
        flexDirection: "row",
        alignContent: "center",
        justifyContent: fileName || file ? "space-between" : "center"
      },
      children: [
        /* @__PURE__ */ jsx8(
          "input",
          {
            accept: "application/pdf",
            style: { display: "none", cursor: "pointer" },
            id: `file-upload-${name}`,
            type: "file",
            name,
            onChange: handleChange
          }
        ),
        /* @__PURE__ */ jsx8("label", { htmlFor: `file-upload-${name}`, children: fileName || file ? /* @__PURE__ */ jsxs6(
          Stack4,
          {
            flexDirection: "row",
            sx: {
              justifyContent: "space-between",
              alignItems: "center"
            },
            children: [
              /* @__PURE__ */ jsx8(PdfIcon, {}),
              /* @__PURE__ */ jsx8(Typography5, { children: name })
            ]
          }
        ) : /* @__PURE__ */ jsxs6(
          Stack4,
          {
            sx: {
              justifyContent: "center",
              alignItems: "center"
            },
            children: [
              /* @__PURE__ */ jsx8(UploadIcon, {}),
              /* @__PURE__ */ jsxs6(Typography5, { variant: "subtitle2", color: "text.primary", children: [
                /* @__PURE__ */ jsx8(
                  "span",
                  {
                    style: {
                      fontWeight: 600
                    },
                    children: "Click to Upload"
                  }
                ),
                " ",
                "or Drag and drop"
              ] }),
              /* @__PURE__ */ jsx8(Typography5, { variant: "subtitle2", children: "PDF (max size 10MB)" })
            ]
          }
        ) }),
        (fileName || file) && /* @__PURE__ */ jsxs6(Stack4, { direction: "row", children: [
          /* @__PURE__ */ jsx8(DeleteDocument, { handleDelete: handleRemoveFile }),
          /* @__PURE__ */ jsx8(
            PreviewDocument,
            {
              file,
              isExisting: !!initialFile
            }
          )
        ] })
      ]
    }
  );
};
var PreviewDocument = (props) => {
  const [isPreviewOpen, setIsPreviewOpen] = useState4(false);
  const [srcUrl, setSrcUrl] = useState4("");
  const handlePreview = () => {
    if (isPreviewOpen) {
      setSrcUrl("");
      setIsPreviewOpen(false);
    } else {
      setIsPreviewOpen(true);
      if (props.file) {
        if (props.isExisting) {
          setSrcUrl(props.file);
        } else {
          const reader = new FileReader();
          reader.onload = (e) => {
            e.target && setSrcUrl(e.target.result);
          };
          reader.readAsDataURL(props.file);
        }
      }
    }
  };
  return /* @__PURE__ */ jsxs6(Fragment3, { children: [
    /* @__PURE__ */ jsx8(IconButton3, { onClick: handlePreview, children: /* @__PURE__ */ jsx8(VisibilityOutlined, { color: "primary" }) }),
    /* @__PURE__ */ jsxs6(
      CustomDialog,
      {
        open: isPreviewOpen,
        onClose: handlePreview,
        maxWidth: "lg",
        fullWidth: true,
        children: [
          /* @__PURE__ */ jsxs6(
            Stack4,
            {
              sx: {
                background: "#F9FAFB",
                borderBottom: "2px solid  #F2F4F7"
              },
              children: [
                /* @__PURE__ */ jsx8(
                  DialogTitle,
                  {
                    sx: {
                      fontSize: "16px",
                      fontWeight: 600,
                      py: "1%"
                    },
                    children: "File"
                  }
                ),
                /* @__PURE__ */ jsx8(
                  IconButton3,
                  {
                    "aria-label": "close",
                    onClick: handlePreview,
                    sx: {
                      position: "absolute",
                      right: 8,
                      top: 4,
                      color: ""
                    },
                    children: /* @__PURE__ */ jsx8(CloseIcon2, {})
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ jsx8(DialogContent, { children: /* @__PURE__ */ jsx8(
            "iframe",
            {
              src: srcUrl,
              style: { width: "100%", height: "80vh", border: "none" }
            }
          ) })
        ]
      }
    )
  ] });
};
var DeleteDocument = (props) => {
  const [open, setOpen] = useState4(false);
  const handleClose = (reason) => {
    if (reason === "backdropClick") {
      return false;
    }
    setOpen(false);
  };
  return /* @__PURE__ */ jsxs6(Fragment3, { children: [
    /* @__PURE__ */ jsx8(IconButton3, { onClick: () => setOpen(!open), children: /* @__PURE__ */ jsx8(DeleteOutlineOutlined, { color: "primary" }) }),
    /* @__PURE__ */ jsxs6(CustomDialog, { open, onClose: handleClose, maxWidth: "xs", fullWidth: true, children: [
      /* @__PURE__ */ jsxs6(
        Stack4,
        {
          sx: {
            background: "#F9FAFB",
            borderBottom: "2px solid  #F2F4F7",
            flexDirection: "row"
          },
          children: [
            /* @__PURE__ */ jsx8(
              DialogTitle,
              {
                sx: {
                  fontWeight: 600,
                  py: "1%"
                },
                children: /* @__PURE__ */ jsx8(DeleteOutlineOutlined, { color: "error" })
              }
            ),
            /* @__PURE__ */ jsx8(
              IconButton3,
              {
                "aria-label": "close",
                onClick: () => handleClose("close"),
                sx: {
                  position: "absolute",
                  right: 8,
                  top: 4,
                  color: ""
                },
                children: /* @__PURE__ */ jsx8(CloseIcon2, {})
              }
            )
          ]
        }
      ),
      /* @__PURE__ */ jsxs6(DialogContent, { children: [
        /* @__PURE__ */ jsx8(
          Typography5,
          {
            variant: "subtitle1",
            sx: {
              color: "text.primary",
              fontSize: "18px",
              fontWeight: 600
            },
            children: "Delete Document"
          }
        ),
        /* @__PURE__ */ jsx8(Typography5, { variant: "subtitle2", children: "Are you sure you want to delete this uploaded form? This action cannot be undone." })
      ] }),
      /* @__PURE__ */ jsxs6(
        Stack4,
        {
          sx: {
            flexDirection: "row",
            justifyContent: "space-between",
            gap: "2%",
            py: "2%",
            px: "4%"
          },
          children: [
            /* @__PURE__ */ jsx8(
              Button5,
              {
                variant: "outlined",
                fullWidth: true,
                onClick: () => handleClose("close"),
                children: "Cancel"
              }
            ),
            /* @__PURE__ */ jsx8(Button5, { variant: "contained", fullWidth: true, onClick: props.handleDelete, children: "Confirm" })
          ]
        }
      )
    ] })
  ] });
};

// src/components/Input/MultiSelectAutoComplete.tsx
import {
  Autocomplete as Autocomplete2,
  FormControl as FormControl2,
  IconButton as IconButton4,
  InputAdornment as InputAdornment2,
  Stack as Stack5,
  TextField as TextField3,
  Typography as Typography6
} from "@mui/material";
import { SearchRounded as SearchRounded2 } from "@mui/icons-material";
import { jsx as jsx9, jsxs as jsxs7 } from "react/jsx-runtime";
var MultiSelectAutocomplete = ({
  label,
  options,
  selectedItems,
  getOptionLabel,
  onChange,
  onDelete,
  isOptionEqualToValue,
  renderOption
}) => {
  return /* @__PURE__ */ jsxs7(FormControl2, { fullWidth: true, margin: "normal", children: [
    /* @__PURE__ */ jsx9(Typography6, { variant: "label3", sx: { mt: 2 }, children: label }),
    "\xA0",
    /* @__PURE__ */ jsx9(
      Autocomplete2,
      {
        multiple: true,
        options,
        value: selectedItems,
        getOptionLabel,
        isOptionEqualToValue,
        onChange,
        renderTags: (value, getTagProps) => value.map((option, index) => /* @__PURE__ */ jsx9(Stack5, { direction: "row", children: /* @__PURE__ */ jsx9(
          Stack5,
          {
            direction: "row",
            sx: {
              display: "flex",
              alignItems: "center",
              gap: 1,
              height: "1.25rem",
              minWidth: "50%",
              marginRight: "4px",
              marginBottom: "4px"
            },
            children: /* @__PURE__ */ jsxs7(
              Stack5,
              {
                direction: "row",
                sx: {
                  display: "flex",
                  alignItems: "center",
                  gap: "2px",
                  borderRadius: "4px",
                  border: "1px solid #D0D5DD",
                  height: "1.25rem",
                  padding: "2px 11px 2px 7px"
                },
                children: [
                  /* @__PURE__ */ jsx9(
                    IconButton4,
                    __spreadProps(__spreadValues({
                      size: "small"
                    }, getTagProps({ index })), {
                      onClick: () => onDelete(option),
                      children: /* @__PURE__ */ jsx9(IconClose, {})
                    })
                  ),
                  /* @__PURE__ */ jsx9(Typography6, { children: getOptionLabel(option) })
                ]
              }
            )
          },
          index
        ) }, index)),
        renderOption,
        renderInput: (params) => /* @__PURE__ */ jsx9(
          TextField3,
          __spreadProps(__spreadValues({}, params), {
            inputProps: __spreadProps(__spreadValues({}, params.inputProps), {
              startAdornment: /* @__PURE__ */ jsx9(InputAdornment2, { position: "start", children: /* @__PURE__ */ jsx9(SearchRounded2, {}) })
            })
          })
        )
      }
    )
  ] });
};

export {
  CustomSearchInput,
  CustomSearchBox,
  emails,
  AutoComplete,
  CustomFormControlLabel,
  CustomSearchByInput,
  InputField,
  UploadDocumentForm,
  MultiSelectAutocomplete,
  TableDropDownMenu,
  DropDownMenuRadio,
  DropdownMenuCheckBoxWithSearch,
  DropDownMenu,
  DotsDropdown,
  DateRangePicker,
  CustomerModuleSearchFilterBox
};
