import {
  __async
} from "./chunk-BBZEL7EG.js";

// src/components/InActivity/InActivity.tsx
import { useEffect, useRef, useState } from "react";
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Typography
} from "@mui/material";
import { CloseRounded, ErrorOutlineOutlined } from "@mui/icons-material";
import { Fragment, jsx, jsxs } from "react/jsx-runtime";
var InActivity = ({
  children,
  isLoggedIn,
  timeout = 20 * 60 * 1e3,
  // 20 minutes
  onLogout
  // Optional callback to override default logout behavior,
}) => {
  const authenticated = isLoggedIn();
  const timer = useRef(null);
  const [openPopup, setOpenPopup] = useState(false);
  const resetTimer = () => {
    if (timer.current) clearTimeout(timer.current);
    timer.current = setTimeout(showPopup, timeout);
  };
  const showPopup = () => {
    authenticated && setOpenPopup(true);
    timer.current = setTimeout(handleLogout, 1e4);
  };
  const handleStayLoggedIn = () => {
    setOpenPopup(false);
    resetTimer();
  };
  const handleLogout = () => __async(void 0, null, function* () {
    setOpenPopup(false);
    if (timer.current) clearTimeout(timer.current);
    if (typeof onLogout === "function") {
      onLogout();
      return;
    }
    if (formRef.current) {
      formRef.current.submit();
    }
    localStorage.clear();
    sessionStorage.clear();
  });
  useEffect(() => {
    const events = [
      "load",
      "mousemove",
      "mousedown",
      "click",
      "scroll",
      "keypress",
      "touchstart",
      "touchmove",
      "touchend",
      "touchcancel"
    ];
    const resetTimerOnEvent = () => resetTimer();
    events.forEach((event) => window.addEventListener(event, resetTimerOnEvent));
    resetTimer();
    return () => {
      events.forEach(
        (event) => window.removeEventListener(event, resetTimerOnEvent)
      );
      if (timer.current) clearTimeout(timer.current);
    };
  }, []);
  const formRef = useRef(null);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      "form",
      {
        method: "POST",
        ref: formRef,
        style: { display: "none" },
        action: `${process.env.NEXT_PUBLIC_OPEN_API_BASE_URL}/users/logout`,
        children: /* @__PURE__ */ jsx(
          "input",
          {
            type: "hidden",
            name: "redirectUrl",
            value: `${process.env.NEXT_PUBLIC_POST_LOGOUT_REDIRECT_URL}`
          }
        )
      }
    ),
    /* @__PURE__ */ jsxs(Dialog, { open: openPopup, children: [
      /* @__PURE__ */ jsxs(
        DialogTitle,
        {
          id: "alert-dialog-title",
          sx: {
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between"
          },
          children: [
            /* @__PURE__ */ jsxs(
              Box,
              {
                sx: {
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "flex-start"
                },
                children: [
                  /* @__PURE__ */ jsx(
                    ErrorOutlineOutlined,
                    {
                      fontSize: "medium",
                      sx: {
                        color: "#D92D20",
                        background: "#FEE4E2",
                        borderRadius: "var(--radius-full, 9999px)",
                        display: "flex",
                        width: "48px",
                        height: "48px",
                        padding: "12px",
                        justifyContent: "center",
                        alignItems: "center",
                        marginRight: "16px"
                      }
                    }
                  ),
                  /* @__PURE__ */ jsx(Typography, { variant: "subtitle1", children: "Are you still there?" })
                ]
              }
            ),
            /* @__PURE__ */ jsx(
              IconButton,
              {
                "aria-label": "close",
                onClick: handleStayLoggedIn,
                sx: {
                  position: "absolute",
                  right: 8,
                  top: 8,
                  color: ""
                },
                children: /* @__PURE__ */ jsx(CloseRounded, {})
              }
            )
          ]
        }
      ),
      /* @__PURE__ */ jsx(
        DialogContent,
        {
          sx: {
            width: "400px"
          },
          children: /* @__PURE__ */ jsx(
            DialogContentText,
            {
              id: "alert-dialog-description",
              sx: {
                color: "#475467",
                fontWeight: "400",
                fontSize: "14px",
                textAlign: "center"
              },
              children: "It looks like you've been inactive for a while. For your security, you\u2019ll be logged out shortly. If you\u2019re still here, click \u2018Stay Logged In.'"
            }
          )
        }
      ),
      /* @__PURE__ */ jsxs(
        DialogActions,
        {
          sx: {
            justifyContent: "space-between"
          },
          children: [
            /* @__PURE__ */ jsx(Button, { variant: "outlined", onClick: handleLogout, children: "Logout" }),
            /* @__PURE__ */ jsx(Button, { variant: "contained", onClick: handleStayLoggedIn, autoFocus: true, children: "Stay Logged In" })
          ]
        }
      )
    ] }),
    children
  ] });
};

export {
  InActivity
};
