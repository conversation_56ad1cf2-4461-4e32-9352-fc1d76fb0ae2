import * as react_jsx_runtime from 'react/jsx-runtime';
import React__default from 'react';

type FilterType = 'select' | 'dropdown/checkbox' | 'dropdown/single' | 'date';
interface IFilterOptions {
    key: string;
    value: string;
    label: string;
}
interface IFilterOption {
    key: string;
    value: string;
    label: string;
}
interface IFilter {
    filterName: string;
    options: IFilterOptions[];
    type: FilterType;
}
interface CustomFilterBoxProps {
    openFilter: boolean;
    setOpenFilter: (value: boolean) => void;
    searchValue: string;
    handleSearch: (e: React__default.ChangeEvent<HTMLInputElement>) => void;
    filters: IFilter[];
    searchByValues?: Array<{
        label: string;
        value: string;
    }>;
    onFilterChange: (filters: Record<string, string | string[]>) => void;
    setSearchByValue?: (value: string) => void;
}
declare const CustomerModuleSearchFilterBox: (props: CustomFilterBoxProps) => react_jsx_runtime.JSX.Element;

export { type CustomFilterBoxProps as C, type IFilterOption as I, CustomerModuleSearchFilterBox as a };
