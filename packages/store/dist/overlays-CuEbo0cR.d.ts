import * as redux from 'redux';
import * as _reduxjs_toolkit from '@reduxjs/toolkit';
import { j as IDecodeToken, l as IChannelModule, t as ICustomer, i as IUser, f as IRole } from './customers-DTtxUxlp.js';

interface IAuth {
    isLoadingLogin: boolean;
    isLoginSuccess: boolean;
    isLoginError: boolean;
    loginErrorMessage: string;
    decodedToken: IDecodeToken;
    channelModules: IChannelModule[];
    userInfo: {
        username: string;
        email: string;
        phoneNumber: string;
    } | null;
}
declare const setIsLoadingLogin: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "auth/setIsLoadingLogin">;
declare const setDecodedToken: _reduxjs_toolkit.ActionCreatorWithPayload<IDecodeToken, "auth/setDecodedToken">;
declare const setIsLoginSuccess: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "auth/setIsLoginSuccess">;
declare const setIsLoginError: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "auth/setIsLoginError">;
declare const setChannelModules: _reduxjs_toolkit.ActionCreatorWithPayload<IChannelModule[], "auth/setChannelModules">;
declare const setLoginErrorMessage: _reduxjs_toolkit.ActionCreatorWithPayload<string, "auth/setLoginErrorMessage">;
declare const setCredentials: _reduxjs_toolkit.ActionCreatorWithPayload<any, "auth/setCredentials">;
declare const _default$2: redux.Reducer<IAuth>;

interface INavigationState {
    switchToCustomerDetails: {
        customer: ICustomer | null;
        open: boolean;
        isPendingCustomer: boolean;
    };
    switchToUserDetails: {
        user: IUser | null;
        open: boolean;
    };
    switchToRoleDetails: {
        role: IRole | null;
        open: boolean;
        type: string;
    };
    isSidebarCollapsed: boolean;
    documentToggle: {
        open: boolean;
        imageUrl: string;
    };
}
declare const setSwitchToUserDetails: _reduxjs_toolkit.ActionCreatorWithPayload<{
    open: boolean;
    user: IUser | null;
}, "navigation/setSwitchToUserDetails">;
declare const setSwitchToCustomerDetails: _reduxjs_toolkit.ActionCreatorWithPayload<any, "navigation/setSwitchToCustomerDetails">;
declare const setSwitchToRoleDetails: _reduxjs_toolkit.ActionCreatorWithPayload<any, "navigation/setSwitchToRoleDetails">;
declare const setSidebarCollapsed: _reduxjs_toolkit.ActionCreatorWithPayload<boolean, "navigation/setSidebarCollapsed">;
declare const setDocumentToggle: _reduxjs_toolkit.ActionCreatorWithPayload<{
    open: boolean;
    imageUrl: string;
}, "navigation/setDocumentToggle">;
declare const _default$1: redux.Reducer<INavigationState>;

type TComponent = 'create_user' | 'create_role' | 'view_right' | 'change_log' | 'edit_role' | 'edit_user' | 'view_roles' | 'event_history' | 'security_question_history' | 'pin_history' | 'customer_accounts_history' | 'pending_approval_requests';
interface DrawerChildren {
    open: boolean;
    drawerChildren: {
        childType: TComponent;
        data?: string | null | IRole | IUser | IRole[] | {
            event: string;
            event_source: string;
            event_date: string;
            id: string;
        }[];
    } | null;
    header: string;
}
interface OverlayState {
    openUserChangeLogDrawer: boolean;
    drawer: DrawerChildren;
}
declare const setOpenUserChangeLogs: _reduxjs_toolkit.ActionCreatorWithPayload<any, "overlays/setOpenUserChangeLogs">;
declare const setDrawer: _reduxjs_toolkit.ActionCreatorWithPayload<DrawerChildren, "overlays/setDrawer">;
declare const resetDrawer: _reduxjs_toolkit.ActionCreatorWithoutPayload<"overlays/resetDrawer">;
declare const _default: redux.Reducer<OverlayState>;

export { type INavigationState as I, type OverlayState as O, _default$2 as _, type IAuth as a, _default$1 as b, _default as c, setDecodedToken as d, setIsLoginSuccess as e, setIsLoginError as f, setChannelModules as g, setLoginErrorMessage as h, setCredentials as i, setSwitchToUserDetails as j, setSwitchToCustomerDetails as k, setSwitchToRoleDetails as l, setSidebarCollapsed as m, setDocumentToggle as n, setOpenUserChangeLogs as o, setDrawer as p, resetDrawer as r, setIsLoadingLogin as s };
