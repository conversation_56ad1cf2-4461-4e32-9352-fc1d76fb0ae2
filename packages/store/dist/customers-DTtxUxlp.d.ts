import React from 'react';
import { Dispatch } from 'redux';

interface IHeadCell {
    id?: string;
    label?: string;
    alignRight?: boolean;
    alignCenter?: boolean;
}
interface ISidebarConfigItem {
    path: string;
    title: string;
    id: string;
    icon: React.JSX.Element;
    app: string;
    isProductionReady?: boolean;
    requiredRights: string[];
}
interface IFilterOption {
    key: string;
    value: string;
    label: string;
}
type FilterType = 'select' | 'dropdown/checkbox' | 'dropdown/single' | 'date';
interface IFilter {
    filterName: string;
    options: IFilterOption[];
    type: FilterType;
}
interface ILandingApps {
    name: string;
    key: string;
    channel: string;
    icon?: React.JSX.Element;
    url: string;
    isProductionReady: boolean;
    modules?: string[];
}
interface ITableData {
    id: string;
    event: string;
    eventSource?: string;
    eventDate: string;
    maker?: string;
    makerTimestamp?: string;
    checker?: string;
    checkerTimestamp?: string;
}
interface IRole {
    id: string;
    name: string;
    description: string;
    creationDate: string;
    custom: boolean;
    status?: string;
    createdBy?: string;
    permissions: IPermission[];
    permissionsGroup: IPermissionGroup[];
    [key: string]: string | boolean | IPermission[] | IPermissionGroup[] | undefined;
}
interface IPermission {
    id: string;
    name: string;
    description: string;
    groupName: string;
    visible: boolean;
    module: string | {
        id: string;
        description: string;
        moduleName: string;
        approvalRequest: string | null;
        dateCreated: string | null;
        dateModified: string | null;
        updatedBy: string | null;
    };
}
interface IPermissionGroup {
    id: string;
    name: string;
    description: string;
    permissions: IPermission[];
}
interface IUser {
    id: string;
    firstName: string;
    lastName: string;
    middleName: string;
    roles: IRole[];
    email: string;
    phoneNumber: string;
    dateCreated?: string;
    status: string;
    lastLoginDate?: string;
    [key: string]: string | number | boolean | IRole[] | undefined;
}
type ClientType = 'Buyer' | 'Broker' | 'Producer' | 'Partner' | 'Warehouse';
interface IDecodeToken {
    email?: string;
    username?: string;
    last_name: string;
    first_name: string;
    user_id: string;
    authorities: string[];
    sub: string;
    name?: string;
    clientName?: string;
    aud?: string;
    iat: number;
    exp: number;
    ext_sys_id?: string;
    resources?: IResource[];
    clientType?: ClientType;
}
interface IResource {
    resourceType: string;
    resourceIds: string[];
}
interface IChannelModule {
    channel: string;
    modules: string[];
}

interface ICustomersFilter {
    dateCreatedFrom?: string;
    dateCreatedTo?: string;
    size: number;
    page: number;
    firstName?: string;
    otherNames?: string;
    lastName?: string;
    phoneNumber?: string;
    email?: string;
    nationality?: string;
    idNumber?: string;
    accountNumber?: string;
}
interface IPendingCustomersFilter {
    size?: number;
    page?: number;
    module?: string;
    requestType?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
    maker?: string | string[];
    makerSearchType?: string;
}
interface ISetCustomerSearch {
    searchBy: string[];
    searchValue: string;
}
interface IGetCustomersRespons {
    data: ICustomer[];
    totalElements: number;
    totalPages: number;
    size: number;
    page: number;
}
interface IGetCustomerResponse {
    data: ICustomer;
}
interface CustomerErrorResponse {
    status: number;
    message: string;
    error: string;
}
interface ICustomersDataResponse {
    data: ICustomer[];
    pageNumber: number;
    pageSize: number;
    totalNumberOfPages: number;
    totalElements: number;
}
interface ICustomer {
    id?: string;
    blockReason: string;
    country: string;
    firstName: string;
    otherNames: string;
    lastName: string;
    phoneNumber: string;
    accountNumber?: string;
    email: string;
    nationality: string;
    idNumber: string;
    idType: string;
    onboardingType: string;
    isBlocked: boolean;
    dateCreated: string;
    dateModified: string;
    sex: string;
    storeOfValues: StoreOfValue[];
    profileAccountStoreIds: IprofileAccountStoreIds[];
}
interface ICustomerAccountLink {
    accounts: ICustomerAccount[];
}
interface ICustomerCreate {
    blockReason: string;
    country: string;
    firstName: string;
    branchCode: string;
    otherNames: string;
    lastName: string;
    phoneNumber: string;
    email: string;
    nationality: string;
    idNumber: string;
    idType: string;
    onboardingType: string;
    isBlocked: boolean;
    customerAccounts: ICustomerAccount[];
}
interface StoreOfValue {
    storeCode: string;
    customerId: string;
}
interface IprofileAccountStoreIds {
    profileId: string;
    storeCode: string;
    description: string;
    customerId: string;
}
interface IDevicesResponse {
    pageNumber: number;
    pageSize: number;
    totalNumberOfPages: number;
    totalElements: number;
    data: IDevice[];
}
interface IDevice {
    deviceId: string;
    deviceType: string;
    deviceName: string;
    deviceStatus: string;
    deviceModel: string;
    uuid: string;
    dateCreated: Date;
    devicePlatform: string;
    phoneNumber: string;
}
interface ICustomerAccount {
    accNumber: string;
    accOpenDate: string;
    customerType: string;
    customerCategory: string;
    accBranchCode: string;
    accClass: string;
    accClassDesc: string;
    accCurrency: string;
    accDormant: string;
    accStatus: string;
    accRecordStatus: string;
    accStatBlock: string;
    accFrozen: string;
    accNoDebit: string;
    accNoCredit: string;
    accStopPay: string;
    jointAccIndicator: string;
    customerRecordStatus: string;
    accountClass: string | null;
    isMobileLinkable: boolean;
    tariffName?: string;
}
interface ICustomerProfileAccount {
    profileId: string;
    id: {
        storeCode: string;
        description: string;
        profileId: string;
        accountType: string;
        accountNo: string;
    };
    profile: {
        id: string;
        firstName: string;
        otherNames: string;
        lastName: string;
        email: string;
        phoneNumber: string;
        dateCreated: string;
        dateModified: string;
    };
    storeCode: string;
    description: string;
    accountNo: string;
    accountType: string;
    mandate: string;
    isMobileLinked: boolean;
    fullName: string;
    shortName: string;
    branchCode: string;
    currency: string;
    isDormant: boolean;
    isBlocked: boolean;
    isFrozen: boolean;
    isNoDebit: boolean;
    isNoCredit: boolean;
    isStopPay: boolean;
    status: string;
    tariffName: string;
    dateCreated: string;
    dateModified: string;
}
interface IUpdateCustomerDetails {
    email: string;
    profileID: string;
    dispatch: Dispatch;
}
interface IApproveRejectCustomerUpdate {
    approvalID: string;
    comments: string;
    dispatch: Dispatch;
}
interface ICustomerAccountEventHistory {
    type: string;
    maker: string;
    makerTimestamp: string;
    checker: string;
    checkerTimestamp: string;
}
interface ICustomerAccountDetails {
    firstName: string;
    lastName: string;
    email: string;
    phoneNumber: string;
    postalAddress: string;
    gender: string;
    idType: string;
    idValue: string;
    cif: string;
    physicalAddress: string;
    country: string;
    customerPrefix: string;
    dateOfBirth: string;
    nationality: string;
    customerCategory: string;
    customerType: string;
    customerAccounts: ICustomerAccount[];
    comments: string | null;
}
interface ICustomerAccountHistoryLogs {
    id: string;
    event: string;
    eventSource: string;
    eventDate: string;
}
interface ILogs {
    id: string;
    event: string;
    eventSource: string;
    eventDate: string;
}
interface ICustomerPinReset {
    profileID?: string;
    comments?: string;
    dispatch: Dispatch;
    pinType?: string;
}
interface IApproveCustomerPinReset extends ICustomerPinReset {
    approvalID?: string;
    type: string;
}
interface ICustomerPinDetails {
    profileId: string;
    status: string;
    type: string;
    attempts: number;
    dateFirstCreated: string;
    dateLastChanged: string;
}
interface IRestrictAccountParams {
    profileId: string;
    accountNo: string;
    comments: string;
    dispatch: Dispatch;
}
interface IAcceptRejectRestrictAccountApprovals {
    approvalId: string;
    comments: string;
    dispatch: Dispatch;
}
interface IActivateCustomerProfile {
    profileId: string;
    comments: string;
    dispatch: Dispatch;
}
interface IApproveRejectCustomerProfileActivation {
    approvalId: string;
    comments: string;
    dispatch: Dispatch;
}
interface ICustomerPinLog {
    id: string;
    event: string;
    eventSource: string;
    eventDate: string;
}
interface ICustomerPinLogResponse {
    data: ICustomerPinLog[];
    totalElements: number;
    totalNumberOfPages: number;
    pageNumber: number;
    pageSize: number;
}
interface IGetCustomerDevicesParams {
    profileID: string;
    dateCreatedFrom?: string;
    dateCreatedTo?: string;
    status?: string;
    deviceType?: string;
    deviceId?: string;
    page: number;
    size: number;
}
interface IGetCustomerDevicesProps {
    params: IGetCustomerDevicesParams;
    dispatch: Dispatch;
}
interface IGetCustomerDeviceDetail {
    profileID: string;
    deviceID: string;
    dispatch: Dispatch;
}
interface IDeactivateCustomerDeviceParams extends IGetCustomerDeviceDetail {
    comments: string;
}
interface IRejectCustomerDeviceParams {
    approvalId: string;
    comments: string;
    dispatch: Dispatch;
}
interface ICreateCustomerAccount {
    account: ICustomerAccountDetails;
    dispatch: Dispatch;
    setOpen?: (val: boolean) => void;
    setStep?: (val: string) => void;
}
interface IDeactivateCustomer {
    profileID: string;
    reason: string;
    dispatch: Dispatch;
}
interface IDeactivateCustomer {
    profileID: string;
    reason: string;
    dispatch: Dispatch;
}
interface IDeactivateCustomerApprovals {
    approvalID: string;
    comments: string;
    dispatch: Dispatch;
}
interface IDeactivateCustomerApprovals {
    approvalID: string;
    comments: string;
    dispatch: Dispatch;
}
interface IFetchCustomerAccount {
    account: string;
    dispatch: Dispatch;
}
interface IFetchCustomerAccount {
    account: string;
    dispatch: Dispatch;
}
interface IFetchCustomerAccount {
    account: string;
    dispatch: Dispatch;
}
interface IFetchCustomerAccount {
    account: string;
    dispatch: Dispatch;
}
interface ICreateCustomerApprovals {
    approvalId: string;
    comments: string;
    dispatch: Dispatch;
}
interface ICreateCustomerApprovals {
    approvalId: string;
    comments: string;
    dispatch: Dispatch;
}
interface ICreateCustomerDeactivate {
    approvalId: string;
    comments: string;
    accountNo: string;
    profileId: string;
    dispatch: Dispatch;
}
interface IAccountLinkingCompletion {
    approvalId: string;
    comments: string;
    profileId: string;
    dispatch: Dispatch;
    action: 'approve' | 'reject';
}
interface IAccountActivation {
    accountNo: string;
    profileId: string;
    comments: string;
    setOpen: (open: boolean) => void;
    setIsLoading: (loading: boolean) => void;
    dispatch: Dispatch;
}
interface INotificationEventSettings {
    id: string;
    notificationType: string;
    deliveryMode: string;
    templateName: string | null;
}
interface INotificationEventSubscribers {
    id: string;
    recipient: string;
    deliveryMode: string;
}
interface INotificationEventTemplates {
    id: string;
    templateName: string;
    templateSubject: string;
    templateContent: string;
    templateDescription: string;
    htmlContent: boolean;
}
interface INotificationEvents {
    id: string;
    eventType: string;
    eventName: string;
    settings: INotificationEventSettings[];
    subscribers: INotificationEventSubscribers[];
    placeHolders: string[];
    templates: INotificationEventTemplates[];
}
interface INotificationFrequencies {
    id: string;
    name: string;
    interval: number;
    frequencyType: string;
    description: string;
}
interface INotificationEventsPayload {
    eventId: string;
    profileId: string;
    accountSource: string;
    branchCode: string;
    accountId: string;
    frequencyId: string;
    thresholdAmount: string;
    subscribers: INotificationEventSubscriberPayload[];
    comments: string;
}
interface INotificationEventSubscriberPayload {
    recipients: string[];
    deliveryMode: string;
    name: string;
    recipientType: string;
}
interface INotificationEventType {
    id: string;
    eventType: string;
    eventName: string;
    platform: string | null;
}
interface INotificationEventsPerAccount {
    accountSource: string;
    accountId: string;
    alertFrequency: INotificationFrequencies;
    thresholdAmount: number;
    optedInDate: string;
    status: string;
    numberOfSends: number;
    event: INotificationEventType;
    subscribers: INotificationEventSubscribers[];
}

export type { IFetchCustomerAccount as $, ICustomerProfileAccount as A, IUpdateCustomerDetails as B, ClientType as C, IApproveRejectCustomerUpdate as D, ICustomerAccountEventHistory as E, FilterType as F, ICustomerAccountDetails as G, ICustomerAccountHistoryLogs as H, IHeadCell as I, ILogs as J, ICustomerPinReset as K, IApproveCustomerPinReset as L, ICustomerPinDetails as M, IRestrictAccountParams as N, IAcceptRejectRestrictAccountApprovals as O, IActivateCustomerProfile as P, IApproveRejectCustomerProfileActivation as Q, ICustomerPinLog as R, StoreOfValue as S, ICustomerPinLogResponse as T, IGetCustomerDevicesParams as U, IGetCustomerDevicesProps as V, IGetCustomerDeviceDetail as W, IDeactivateCustomerDeviceParams as X, IRejectCustomerDeviceParams as Y, IDeactivateCustomer as Z, IDeactivateCustomerApprovals as _, ISidebarConfigItem as a, ICreateCustomerAccount as a0, ICreateCustomerApprovals as a1, ICreateCustomerDeactivate as a2, IAccountLinkingCompletion as a3, IAccountActivation as a4, INotificationEventSettings as a5, INotificationEventSubscribers as a6, INotificationEventTemplates as a7, INotificationEvents as a8, INotificationFrequencies as a9, INotificationEventsPayload as aa, INotificationEventSubscriberPayload as ab, INotificationEventType as ac, INotificationEventsPerAccount as ad, IFilterOption as b, IFilter as c, ILandingApps as d, ITableData as e, IRole as f, IPermission as g, IPermissionGroup as h, IUser as i, IDecodeToken as j, IResource as k, IChannelModule as l, ICustomersFilter as m, IPendingCustomersFilter as n, ISetCustomerSearch as o, IGetCustomersRespons as p, IGetCustomerResponse as q, CustomerErrorResponse as r, ICustomersDataResponse as s, ICustomer as t, ICustomerAccountLink as u, ICustomerCreate as v, IprofileAccountStoreIds as w, IDevicesResponse as x, IDevice as y, ICustomerAccount as z };
