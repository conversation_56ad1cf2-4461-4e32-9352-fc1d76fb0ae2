'use client'

import { configureStore } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import { PersistPartial } from 'redux-persist/es/persistReducer'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import rootReducer, { RootReducer } from './reducers/store'

import storage from 'redux-persist/lib/storage'

export const persistConfig = {
  key: 'backoffice',
  storage,
  blacklist: ['notification', 'overlays', 'chargeConfiguration'],
}

export const persistedReducer = persistReducer(persistConfig, rootReducer)

export const setupStore = (preloadedState?: Partial<RootReducer>) => {
  return configureStore({
    reducer: persistedReducer,
    preloadedState: preloadedState as RootReducer & PersistPartial,
    devTools: process.env.NODE_ENV === 'development',
  })
}
const store = setupStore()

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export type AppStore = typeof store
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export default store
