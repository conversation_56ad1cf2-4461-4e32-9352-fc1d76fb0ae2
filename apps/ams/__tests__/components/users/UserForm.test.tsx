import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { CreateUser } from '@/app/users/users/CreateUser'
import { useAppDispatch, useAppSelector } from '@/store'
import { getRoles } from '@/store/actions'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  createUser: vi.fn(),
  updateUser: vi.fn(),
  getRoles: vi.fn(),
  getUserADProfile: vi.fn(),
  makeCreateUser: vi.fn(),
}))

// Mock access control utilities
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    CREATE_USERS: 'CREATE_USERS',
    CREATE_ROLES: 'CREATE_ROLES',
  },
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  HasAccessToRights: vi.fn(() => true),
}))

// Mock UI components
vi.mock('@dtbx/ui/components', () => ({
  ReadOnlyTypography: ({ label, ...props }: any) => (
    <input aria-label={label} {...props} />
  ),
  LoadingButton: () => <button>Loading...</button>,
  CustomSearchInput: ({ ...props }: any) => <input {...props} />,
}))

vi.mock('@dtbx/ui/components/Input', () => ({
  CustomSearchInput: ({ ...props }: any) => <input {...props} />,
}))

vi.mock('@dtbx/ui/components/CheckBox', () => ({
  CustomCheckBox: ({ ...props }: any) => <input type="checkbox" {...props} />,
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  LoadingButton: () => <button>Loading...</button>,
}))

describe('CreateUser', () => {
  const mockDispatch = vi.fn()
  const mockRoles = [
    { id: 'role1', name: 'Admin' },
    { id: 'role2', name: 'User' },
    { id: 'role3', name: 'Manager' },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      // Mock different state slices based on the selector
      return {
        rolesList: mockRoles,
        isLoadingCreateUser: false,
        isLoadingADUserDetails: false,
        isLoadedADUserDetailsFailure: false,
        isLoadedADUserDetailsSuccess: false,
        loadedADUserDetails: [],
        isCreateRoleSuccess: false,
        isCreateRoleFailed: false,
      }
    })
  })

  it('renders the create user button', () => {
    render(<CreateUser />)

    expect(screen.getByText('Create new user')).toBeInTheDocument()
  })

  it('opens drawer when button is clicked', () => {
    render(<CreateUser />)

    const button = screen.getByText('Create new user')
    fireEvent.click(button)

    expect(screen.getByText('Create a new staff user')).toBeInTheDocument()
  })

  it('displays loading state when creating user', () => {
    // Mock loading state
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        rolesList: mockRoles,
        isLoadingCreateUser: true,
        isLoadingADUserDetails: false,
        isLoadedADUserDetailsFailure: false,
        isLoadedADUserDetailsSuccess: false,
        loadedADUserDetails: [],
        isCreateRoleSuccess: false,
        isCreateRoleFailed: false,
      }
    })

    render(<CreateUser />)

    const button = screen.getByText('Create new user')
    fireEvent.click(button)

    // The form should be in loading state
    expect(screen.getByText('Create a new staff user')).toBeInTheDocument()
  })

  it('fetches roles when drawer opens', () => {
    render(<CreateUser />)

    const button = screen.getByText('Create new user')
    fireEvent.click(button)

    expect(getRoles).toHaveBeenCalledWith(mockDispatch)
  })
})
