import { render, screen, fireEvent, waitFor } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { PageHeader } from '../../../src/app/roles/PageHeader'
import { useAppDispatch, useAppSelector } from '@/store'
import * as rolesActions from '@/store/actions'
import * as rolesReducers from '@/store/reducers'
import * as storeReducers from '@dtbx/store/reducers'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  generateRoleReports: vi.fn(),
}))

// Mock the reducers
vi.mock('@/store/reducers', () => ({
  setRoleFilterValue: vi.fn(),
  setRoleSearchValue: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setNotification: vi.fn(),
}))

// Mock the CustomFilterBox component
vi.mock('@/app/approval-requests/CustomFilterBox', () => ({
  CustomFilterBox: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
  }: any) => (
    <div data-testid="custom-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search roles"
      />
      <button
        data-testid="filter-toggle"
        onClick={() => setOpenFilter(!openFilter)}
      >
        {openFilter ? 'Close Filter' : 'Open Filter'}
      </button>
      <div data-testid="filters-container">
        {filters.map((filter: any, index: number) => (
          <div key={index} data-testid={`filter-${filter.filterName}`}>
            <span>{filter.filterName}</span>
            <select
              data-testid={`filter-select-${filter.filterName}`}
              onChange={(e) =>
                onFilterChange({ [filter.filterName]: e.target.value })
              }
            >
              <option value="">Select {filter.filterName}</option>
              {filter.options.map((option: any) => (
                <option key={option.key} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ))}
      </div>
    </div>
  ),
}))

// Mock the UI components
vi.mock('@dtbx/ui/components/ExportButton', () => ({
  ExportButton: ({ onClick, text, openFilter }: any) => (
    <button data-testid="export-button" onClick={onClick} disabled={openFilter}>
      {text}
    </button>
  ),
}))

vi.mock('@dtbx/ui/components/Dialogs', () => ({
  ExportPreferences: ({ open, onExport, onCancel, setOpen }: any) =>
    open ? (
      <div data-testid="export-preferences">
        <button data-testid="export-pdf" onClick={() => onExport('PDF')}>
          Export PDF
        </button>
        <button data-testid="export-excel" onClick={() => onExport('EXCEL')}>
          Export Excel
        </button>
        <button data-testid="cancel-export" onClick={onCancel}>
          Cancel
        </button>
      </div>
    ) : null,
}))

// Mock the CreateRole component
vi.mock('../../../src/app/roles/CreateRole', () => ({
  CreateRole: () => <div data-testid="create-role">Create Role</div>,
}))

vi.mock('tiny-case', () => ({
  sentenceCase: (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(),
}))

describe('PageHeader', () => {
  const mockDispatch = vi.fn()
  const mockSearch = vi.fn()
  const mockFilter = vi.fn()
  const mockGenerateRoleReports = vi.fn()
  const mockSetRoleFilterValue = vi.fn()
  const mockSetRoleSearchValue = vi.fn()
  const mockSetNotification = vi.fn()

  const mockPermissionGroups = [
    {
      name: 'users',
      permissions: [{ name: 'CREATE_USER' }, { name: 'UPDATE_USER' }],
    },
    {
      name: 'roles',
      permissions: [{ name: 'CREATE_ROLE' }, { name: 'UPDATE_ROLE' }],
    },
  ]

  const mockState = {
    rolesCount: 25,
    permissionGroup: {
      data: mockPermissionGroups,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({ roles: mockState } as any)
      return result
    })
    vi.mocked(rolesActions.generateRoleReports).mockImplementation(
      mockGenerateRoleReports
    )
    vi.mocked(rolesReducers.setRoleFilterValue).mockImplementation(
      mockSetRoleFilterValue
    )
    vi.mocked(rolesReducers.setRoleSearchValue).mockImplementation(
      mockSetRoleSearchValue
    )
    vi.mocked(storeReducers.setNotification).mockImplementation(
      mockSetNotification
    )
  })

  it('renders the page header with all components', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
    expect(screen.getByTestId('export-button')).toBeInTheDocument()
    expect(screen.getByTestId('create-role')).toBeInTheDocument()
  })

  it('handles search input changes', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'admin' } })

    expect(mockDispatch).toHaveBeenCalledWith(mockSetRoleSearchValue('admin'))
    expect(mockSearch).toHaveBeenCalledWith('admin')
  })

  it('handles filter changes', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')
    fireEvent.change(visibilitySelect, { target: { value: 'yes' } })

    expect(mockDispatch).toHaveBeenCalledWith(
      mockSetRoleFilterValue({ 'Is Visible': 'yes' })
    )
    expect(mockFilter).toHaveBeenCalledWith({ 'Is Visible': 'yes' })
  })

  it('displays correct export count', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByText('Export All 25')).toBeInTheDocument()
  })

  it('opens export preferences when export button is clicked', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const exportButton = screen.getByTestId('export-button')
    fireEvent.click(exportButton)

    expect(screen.getByTestId('export-preferences')).toBeInTheDocument()
  })

  it('handles PDF export', async () => {
    render(
      <PageHeader
        selectedIds={['1', '2']}
        search={mockSearch}
        filter={mockFilter}
      />
    )

    const exportButton = screen.getByTestId('export-button')
    fireEvent.click(exportButton)

    const pdfButton = screen.getByTestId('export-pdf')
    fireEvent.click(pdfButton)

    await waitFor(() => {
      expect(mockGenerateRoleReports).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          page: 1,
          size: 10,
          Rights: '',
          Modules: '',
          UsersAssigned: '',
        },
        format: 'PDF',
        filteredData: [{ id: '1' }, { id: '2' }],
      })
    })
  })

  it('handles Excel export', async () => {
    render(
      <PageHeader selectedIds={['1']} search={mockSearch} filter={mockFilter} />
    )

    const exportButton = screen.getByTestId('export-button')
    fireEvent.click(exportButton)

    const excelButton = screen.getByTestId('export-excel')
    fireEvent.click(excelButton)

    await waitFor(() => {
      expect(mockGenerateRoleReports).toHaveBeenCalledWith({
        dispatch: mockDispatch,
        params: {
          page: 1,
          size: 10,
          Rights: '',
          Modules: '',
          UsersAssigned: '',
        },
        format: 'EXCEL',
        filteredData: [{ id: '1' }],
      })
    })
  })

  it('shows loading overlay during export', async () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const exportButton = screen.getByTestId('export-button')
    fireEvent.click(exportButton)

    const pdfButton = screen.getByTestId('export-pdf')
    fireEvent.click(pdfButton)

    // Loading should be shown
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
  })

  it('displays success notification after export', async () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const exportButton = screen.getByTestId('export-button')
    fireEvent.click(exportButton)

    const pdfButton = screen.getByTestId('export-pdf')
    fireEvent.click(pdfButton)

    await waitFor(
      () => {
        expect(mockDispatch).toHaveBeenCalledWith(
          mockSetNotification({
            message: '25 items successfully exported.',
            type: 'success',
          })
        )
      },
      { timeout: 5000 }
    )
  })

  it('handles export cancellation', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const exportButton = screen.getByTestId('export-button')
    fireEvent.click(exportButton)

    const cancelButton = screen.getByTestId('cancel-export')
    fireEvent.click(cancelButton)

    expect(screen.queryByTestId('export-preferences')).not.toBeInTheDocument()
  })

  it('renders visibility filter options', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByTestId('filter-Is Visible')).toBeInTheDocument()
    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')
    expect(visibilitySelect).toBeInTheDocument()
  })

  it('renders module filter options', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByTestId('filter-Module')).toBeInTheDocument()
    const moduleSelect = screen.getByTestId('filter-select-Module')
    expect(moduleSelect).toBeInTheDocument()
  })

  it('renders rights filter options', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByTestId('filter-Rights')).toBeInTheDocument()
    const rightsSelect = screen.getByTestId('filter-select-Rights')
    expect(rightsSelect).toBeInTheDocument()
  })

  it('handles empty permission groups', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        roles: {
          ...mockState,
          permissionGroup: { data: null },
        },
      } as any)
      return result
    })

    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
  })

  it('updates export count when rolesCount changes', () => {
    const { rerender } = render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByText('Export All 25')).toBeInTheDocument()

    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        roles: {
          ...mockState,
          rolesCount: 50,
        },
      } as any)
      return result
    })

    rerender(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    expect(screen.getByText('Export All 50')).toBeInTheDocument()
  })

  it('handles filter toggle', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const filterToggle = screen.getByTestId('filter-toggle')
    expect(filterToggle).toHaveTextContent('Open Filter')

    fireEvent.click(filterToggle)
    expect(filterToggle).toHaveTextContent('Close Filter')
  })

  it('disables export button when filter is open', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const filterToggle = screen.getByTestId('filter-toggle')
    fireEvent.click(filterToggle)

    const exportButton = screen.getByTestId('export-button')
    expect(exportButton).toBeDisabled()
  })

  it('handles multiple filter changes', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const visibilitySelect = screen.getByTestId('filter-select-Is Visible')
    fireEvent.change(visibilitySelect, { target: { value: 'yes' } })

    const moduleSelect = screen.getByTestId('filter-select-Module')
    fireEvent.change(moduleSelect, { target: { value: 'users' } })

    expect(mockFilter).toHaveBeenCalledWith({ 'Is Visible': 'yes' })
    expect(mockFilter).toHaveBeenCalledWith({ Module: 'users' })
  })

  it('handles empty search input', () => {
    render(
      <PageHeader selectedIds={[]} search={mockSearch} filter={mockFilter} />
    )

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: '' } })

    // The search input should be updated
    expect(searchInput).toHaveValue('')
  })
})
