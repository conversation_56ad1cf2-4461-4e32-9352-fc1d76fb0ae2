import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { EmptyRoles } from '@/app/roles/EmptyRoles'
import { useAppDispatch } from '@/store'
import * as storeReducers from '@dtbx/store/reducers'

// Mock the store hooks
vi.mock('@dtbx/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock the reducers
vi.mock('@dtbx/store/reducers', () => ({
  setDrawer: vi.fn(),
}))

// Mock the access control utilities
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    CREATE_ROLES: 'CREATE_ROLES',
  },
  AccessControlWrapper: ({ children }: any) => <div>{children}</div>,
}))

describe('EmptyRoles', () => {
  const mockDispatch = vi.fn()
  const mockSetDrawer = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(storeReducers.setDrawer).mockImplementation(mockSetDrawer)
  })

  it('renders the empty state with correct styling', () => {
    render(<EmptyRoles />)

    const container = screen
      .getByText("Seems like you haven't created any roles yet")
      .closest('div')
    expect(container).toBeInTheDocument()
    // The styling is applied by MUI components, so we just verify the container exists
  })

  it('displays the correct title', () => {
    render(<EmptyRoles />)

    expect(
      screen.getByText("Seems like you haven't created any roles yet")
    ).toBeInTheDocument()
  })

  it('displays the correct description', () => {
    render(<EmptyRoles />)

    expect(
      screen.getByText(
        'You can do this by clicking on the button below to create a new role.'
      )
    ).toBeInTheDocument()
  })

  it('renders the create new role button', () => {
    render(<EmptyRoles />)

    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    expect(createButton).toBeInTheDocument()
  })

  it('displays the add icon in the button', () => {
    render(<EmptyRoles />)

    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    expect(createButton).toBeInTheDocument()
    // The AddRounded icon should be rendered as a startIcon
  })

  it('opens create role drawer when button is clicked', () => {
    render(<EmptyRoles />)

    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    fireEvent.click(createButton)

    expect(mockDispatch).toHaveBeenCalledWith(
      mockSetDrawer({
        open: true,
        drawerChildren: {
          childType: 'create_role',
        },
        header: 'Create new role',
      })
    )
  })

  it('wraps the button with access control', () => {
    render(<EmptyRoles />)

    // The button should be wrapped with AccessControlWrapper
    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    expect(createButton).toBeInTheDocument()
  })

  it('applies correct typography styling to title', () => {
    render(<EmptyRoles />)

    const title = screen.getByText(
      "Seems like you haven't created any roles yet"
    )
    expect(title).toBeInTheDocument()
    // The title should have specific styling applied
  })

  it('applies correct typography styling to description', () => {
    render(<EmptyRoles />)

    const description = screen.getByText(
      'You can do this by clicking on the button below to create a new role.'
    )
    expect(description).toBeInTheDocument()
    // The description should have specific styling applied
  })

  it('renders with correct stack layout', () => {
    render(<EmptyRoles />)

    const mainStack = screen
      .getByText("Seems like you haven't created any roles yet")
      .closest('div')
    expect(mainStack).toBeInTheDocument()

    const innerStack = screen
      .getByText(
        'You can do this by clicking on the button below to create a new role.'
      )
      .closest('div')
    expect(innerStack).toBeInTheDocument()
  })

  it('applies correct padding to inner stack', () => {
    render(<EmptyRoles />)

    const innerStack = screen
      .getByText(
        'You can do this by clicking on the button below to create a new role.'
      )
      .closest('div')
    expect(innerStack).toHaveStyle({
      paddingLeft: '5%',
      paddingRight: '5%',
      gap: '10px',
      justifyContent: 'center',
    })
  })

  it('renders button with contained variant', () => {
    render(<EmptyRoles />)

    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    expect(createButton).toBeInTheDocument()
    // The button should have variant="contained"
  })

  it('handles multiple clicks on create button', () => {
    render(<EmptyRoles />)

    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })

    fireEvent.click(createButton)
    fireEvent.click(createButton)
    fireEvent.click(createButton)

    expect(mockDispatch).toHaveBeenCalledTimes(3)
    expect(mockDispatch).toHaveBeenCalledWith(
      mockSetDrawer({
        open: true,
        drawerChildren: {
          childType: 'create_role',
        },
        header: 'Create new role',
      })
    )
  })

  it('renders with proper accessibility structure', () => {
    render(<EmptyRoles />)

    // Check that the title is accessible
    const title = screen.getByText(
      "Seems like you haven't created any roles yet"
    )
    expect(title).toBeInTheDocument()

    // Check that the button is accessible
    const button = screen.getByRole('button', { name: /create new role/i })
    expect(button).toBeInTheDocument()
  })

  it('renders all text content correctly', () => {
    render(<EmptyRoles />)

    // Check all text content is present
    expect(
      screen.getByText("Seems like you haven't created any roles yet")
    ).toBeInTheDocument()
    expect(
      screen.getByText(
        'You can do this by clicking on the button below to create a new role.'
      )
    ).toBeInTheDocument()
    expect(screen.getByText('Create new role')).toBeInTheDocument()
  })

  it('handles component unmounting gracefully', () => {
    const { unmount } = render(<EmptyRoles />)

    expect(() => unmount()).not.toThrow()
  })

  it('renders with correct background image path', () => {
    render(<EmptyRoles />)

    const container = screen
      .getByText("Seems like you haven't created any roles yet")
      .closest('div')
    expect(container).toBeInTheDocument()
    // Background image is applied by MUI Stack component
  })

  it('renders button with correct text content', () => {
    render(<EmptyRoles />)

    const button = screen.getByRole('button')
    expect(button).toHaveTextContent('Create new role')
  })

  it('dispatches correct action payload', () => {
    render(<EmptyRoles />)

    const createButton = screen.getByRole('button', {
      name: /create new role/i,
    })
    fireEvent.click(createButton)

    expect(mockSetDrawer).toHaveBeenCalledWith({
      open: true,
      drawerChildren: {
        childType: 'create_role',
      },
      header: 'Create new role',
    })
  })
})
