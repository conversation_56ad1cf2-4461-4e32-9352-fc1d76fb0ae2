import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { AllApprovalRequestsMoreMenu } from '../../../src/app/approval-requests/All/MoreMenu'
import { useAppDispatch } from '@/store'

import { ApprovalRequestRouting } from '../../../src/app/approval-requests/RequestRouting'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  }),
}))

vi.mock('../../../src/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

describe('AllApprovalRequestsMoreMenu', () => {
  const mockDispatch = vi.fn()

  const mockRequest = {
    id: '123',
    makerCheckerType: {
      module: 'Users',
      type: 'CREATE_USER',
      channel: '',
      checkerPermissions: [],
      makerPermissions: [],
      name: '',
      overridePermissions: [],
    },
    status: 'PENDING',
    createdBy: 'user1',
    createdAt: '2023-01-01',
    entityId: 'entity123',
    entity: '{"name":"Test Entity"}',
    diff: [],
    maker: 'maker1',
    checker: undefined,
    makerComments: '',
    checkerComments: undefined,
    dateCreated: '2023-01-01',
    dateModified: '2023-01-01',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the menu button', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const actionsButton = screen.getByText('Actions')
    expect(actionsButton).toBeInTheDocument()
  })

  it('opens the menu when the button is clicked', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    // Initially, the menu should be closed
    expect(screen.queryByText('Go To Module')).not.toBeInTheDocument()

    // Click the button to open the menu
    const actionsButton = screen.getByText('Actions')
    fireEvent.click(actionsButton)

    // Now the menu should be open
    expect(screen.getByText('Go To Module')).toBeInTheDocument()
  })

  it('calls ApprovalRequestRouting when Go To Module is clicked', async () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    // Open the menu
    const actionsButton = screen.getByText('Actions')
    fireEvent.click(actionsButton)

    // Click the Go To Module menu item
    const menuItem = screen.getByText('Go To Module')
    fireEvent.click(menuItem)

    // Verify ApprovalRequestRouting was called with correct params
    expect(ApprovalRequestRouting).toHaveBeenCalledWith(
      mockRequest,
      mockDispatch,
      expect.any(Object)
    )
  })

  it('keeps menu open when disabled item is clicked', () => {
    render(<AllApprovalRequestsMoreMenu request={mockRequest} />)

    const actionsButton = screen.getByText('Actions')
    fireEvent.click(actionsButton)

    const menuItem = screen.getByText('Go To Module')
    fireEvent.click(menuItem)

    expect(screen.getByText('Go To Module')).toBeInTheDocument()
  })
})
