import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { ApprovalRequestDetails } from '@/app/roles/details/RolesDetails/ApprovalRequestDetails'
import { useAppDispatch, useAppSelector } from '@/store'
import { CheckerRequestsApiHandler } from '@/app/approval-requests/CheckerRequestsApiHandler'

// Mock the dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the CheckerRequestsApiHandler
vi.mock('@/app/approval-requests/CheckerRequestsApiHandler', () => ({
  CheckerRequestsApiHandler: vi.fn(),
}))

// Mock the router hook
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock the access control wrapper
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    REJECT_APPROVALREQUEST_ROLES: 'REJECT_APPROVALREQUEST_ROLES',
    ACCEPT_APPROVALREQUEST_ROLES: 'ACCEPT_APPROVALREQUEST_ROLES',
  },
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  formatTimestamp: vi.fn(() => '2023-01-01 10:00:00'),
  handleDiff: vi.fn(() => 'Mock diff'),
}))

describe('ApprovalRequestDetails', () => {
  const mockDispatch = vi.fn()
  const mockApprovalRequest = {
    id: '123',
    requestType: 'CREATE_USER',
    status: 'PENDING',
    createdBy: 'user1',
    createdAt: '2023-01-01',
    entityId: 'entity123',
    makerCheckerType: {
      module: 'users',
      type: 'CREATE_USER',
    },
    payload: {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    },
    maker: 'maker123',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      // Return the specific values the component needs
      return {
        selectedApprovalRequest: mockApprovalRequest,
        isLoadingUpdateRole: false,
        isLoadingDeleteRole: false,
      }
    })
  })

  it('renders the view approval request details button', () => {
    render(<ApprovalRequestDetails />)

    expect(
      screen.getByText('View Approval Request Details')
    ).toBeInTheDocument()
  })

  it('opens drawer when button is clicked', () => {
    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    expect(screen.getByText('Approval Request Details')).toBeInTheDocument()
  })

  it('displays approval request information in drawer', () => {
    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    expect(screen.getByDisplayValue('CREATE_USER')).toBeInTheDocument()
  })

  it('renders approve and reject buttons in drawer', () => {
    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    expect(screen.getByText('Approve')).toBeInTheDocument()
    expect(screen.getByText('Reject')).toBeInTheDocument()
  })

  it('calls CheckerRequestsApiHandler when approve is clicked', async () => {
    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    const approveButton = screen.getByText('Approve')
    fireEvent.click(approveButton)

    expect(CheckerRequestsApiHandler).toHaveBeenCalledWith(
      mockApprovalRequest,
      mockDispatch,
      expect.any(Object),
      'ACCEPT_CREATE_USER',
      'Request Approved successfully'
    )
  })

  it('calls CheckerRequestsApiHandler when reject is clicked', async () => {
    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    const rejectButton = screen.getByText('Reject')
    fireEvent.click(rejectButton)

    expect(CheckerRequestsApiHandler).toHaveBeenCalledWith(
      mockApprovalRequest,
      mockDispatch,
      expect.any(Object),
      'REJECT_CREATE_USER',
      'Request rejected successfully'
    )
  })

  it('displays loading state when updating role', () => {
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        selectedApprovalRequest: mockApprovalRequest,
        isLoadingUpdateRole: true,
        isLoadingDeleteRole: false,
      }
    })

    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    // Should show loading buttons instead of approve/reject
    expect(screen.queryByText('Approve')).not.toBeInTheDocument()
    expect(screen.queryByText('Reject')).not.toBeInTheDocument()
  })

  it('displays loading state when deleting role', () => {
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        selectedApprovalRequest: mockApprovalRequest,
        isLoadingUpdateRole: false,
        isLoadingDeleteRole: true,
      }
    })

    render(<ApprovalRequestDetails />)

    const button = screen.getByText('View Approval Request Details')
    fireEvent.click(button)

    // Should show loading buttons instead of approve/reject
    expect(screen.queryByText('Approve')).not.toBeInTheDocument()
    expect(screen.queryByText('Reject')).not.toBeInTheDocument()
  })
})
