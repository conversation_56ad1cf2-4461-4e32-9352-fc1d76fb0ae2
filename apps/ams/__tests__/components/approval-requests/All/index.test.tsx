import { render, screen, fireEvent, waitFor } from '../../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import AllApprovalRequests from '../../../../src/app/approval-requests/All/index'
import { useAppDispatch, useAppSelector } from '@/store'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions', () => ({
  getApprovalRequestTypes: vi.fn(),
  getApprovals: vi.fn(),
}))

// Mock CustomFilterBox
vi.mock('../../../../src/app/approval-requests/CustomFilterBox', () => ({
  default: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
    setMakerName,
  }: any) => (
    <div data-testid="custom-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
      />
      <button
        data-testid="filter-toggle"
        onClick={() => setOpenFilter(!openFilter)}
      >
        Toggle Filter
      </button>
      <input
        data-testid="maker-name-input"
        onChange={(e) => setMakerName && setMakerName(e.target.value)}
        placeholder="Maker name"
      />
    </div>
  ),
  CustomDateRangePicker: ({ onDateChange }: any) => (
    <div data-testid="custom-date-range-picker">
      <button
        onClick={() =>
          onDateChange &&
          onDateChange({ start: '2023-01-01', end: '2023-01-31' })
        }
      >
        Select Date Range
      </button>
    </div>
  ),
}))

// Mock UI components
vi.mock('@dtbx/ui/components/Table', () => ({
  CustomPagination: ({ onPageChange, onPageSizeChange }: any) => (
    <div data-testid="custom-pagination">
      <button onClick={() => onPageChange && onPageChange(2)}>Page 2</button>
      <button onClick={() => onPageSizeChange && onPageSizeChange(20)}>
        Size 20
      </button>
    </div>
  ),
  CustomTableHeader: ({ headCells = [], onRequestSort }: any) => (
    <div data-testid="custom-table-header">
      {headCells.map((cell: any) => (
        <button
          key={cell.id}
          onClick={() => onRequestSort(cell.id)}
          data-testid={`sort-${cell.id}`}
        >
          {cell.label}
        </button>
      ))}
      {/* Add default sort buttons for common columns */}
      <button
        data-testid="sort-maker"
        onClick={() => onRequestSort && onRequestSort('maker')}
      >
        Sort Maker
      </button>
    </div>
  ),
  PaginationOptions: () => <div data-testid="pagination-options" />,
}))

// Mock MoreMenu component
vi.mock('../../../../src/app/approval-requests/All/MoreMenu', () => ({
  AllApprovalRequestsMoreMenu: ({ request }: { request: any }) => (
    <div data-testid="all-approval-requests-more-menu">
      More Menu for {request.id}
    </div>
  ),
}))

vi.mock('@dtbx/ui/components/EmptyPage', () => ({
  EmptySearchAndFilter: () => (
    <div data-testid="empty-search-filter">No results</div>
  ),
}))

vi.mock('@dtbx/ui/components/Loading', () => ({
  CustomSkeleton: () => <div data-testid="custom-skeleton">Loading...</div>,
}))

vi.mock('@dtbx/ui/components/DropDownMenus', () => ({
  DateRangePicker: ({ onDateChange }: any) => (
    <div data-testid="date-range-picker">
      <button
        onClick={() => onDateChange({ start: '2023-01-01', end: '2023-01-31' })}
      >
        Select Date Range
      </button>
    </div>
  ),
  DropDownMenu: ({ options = [], onSelectionChange }: any) => (
    <div data-testid="dropdown-menu">
      {options.map((option: any) => (
        <button
          key={option.value}
          onClick={() => onSelectionChange(option.value)}
          data-testid={`option-${option.value}`}
        >
          {option.label}
        </button>
      ))}
    </div>
  ),
  DropDownMenuRadio: ({ options = [], onSelectionChange }: any) => (
    <div data-testid="dropdown-menu-radio">
      {options.map((option: any) => (
        <button
          key={option.value}
          onClick={() => onSelectionChange(option.value)}
          data-testid={`radio-option-${option.value}`}
        >
          {option.label}
        </button>
      ))}
    </div>
  ),
}))

describe('AllApprovalRequests', () => {
  const mockDispatch = vi.fn()

  const mockApprovalRequests = [
    {
      id: '1',
      maker: 'John Doe',
      dateCreated: '2023-01-01T10:00:00Z',
      dateModified: '2023-01-01T10:00:00Z',
      makerCheckerType: {
        channel: 'WEB',
        checkerPermissions: [],
        description: 'Create user request',
        makerPermissions: [],
        module: 'users',
        name: 'Create User',
        overridePermissions: [],
        type: 'CREATE_USER',
      },
      entityId: 'user-123',
      entity: 'User',
      diff: [],
      makerComments: 'Creating new user',
      status: 'APPROVED',
      checker: 'Jane Smith',
      checkerComments: 'Approved',
    },
    {
      id: '2',
      maker: 'Alice Johnson',
      dateCreated: '2023-01-02T11:00:00Z',
      dateModified: '2023-01-02T11:00:00Z',
      makerCheckerType: {
        channel: 'WEB',
        checkerPermissions: [],
        description: 'Update role request',
        makerPermissions: [],
        module: 'groups',
        name: 'Update Role',
        overridePermissions: [],
        type: 'UPDATE_ROLE',
      },
      entityId: 'role-456',
      entity: 'Role',
      diff: [],
      makerComments: 'Updating role permissions',
      status: 'REJECTED',
      checker: 'Bob Wilson',
      checkerComments: 'Insufficient justification',
    },
  ]

  const mockState = {
    approvalRequests: {
      approvalRequests: mockApprovalRequests,
      approvalRequestResponse: {
        data: mockApprovalRequests,
        totalElements: 2,
        totalNumberOfPages: 1,
        pageNumber: 1,
        pageSize: 10,
      },
      isLoadingRequests: false,
      requestTypes: [
        { id: '1', name: 'CREATE_USER', description: 'Create User' },
        { id: '2', name: 'UPDATE_ROLE', description: 'Update Role' },
      ],
      isRequestTypesLoading: false,
      isRequestTypesSuccess: true,
    },
    customers: {
      search: {
        searchBy: ['firstName'],
        searchValue: '',
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector(mockState as any)

      // Handle different selectors
      if (typeof result === 'object' && result !== null) {
        // If it's the approvalRequestResponse selector
        if ('data' in result || 'pageNumber' in result) {
          return {
            data: mockApprovalRequests,
            totalElements: 2,
            totalNumberOfPages: 1,
            pageNumber: 1,
            pageSize: 10,
            ...result,
          }
        }

        // If it's an array (like approvalRequests or requestTypes)
        if (Array.isArray(result)) {
          return result
        }

        // If it's the full state object
        if ('approvalRequestResponse' in result) {
          return {
            ...result,
            approvalRequestResponse: result.approvalRequestResponse || {
              data: mockApprovalRequests,
              totalElements: 2,
              totalNumberOfPages: 1,
              pageNumber: 1,
              pageSize: 10,
            },
          }
        }
      }

      return result
    })
  })

  it('renders the component with approval requests', () => {
    render(<AllApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Alice Johnson')).toBeInTheDocument()
    expect(screen.getByText('Create user')).toBeInTheDocument()
    expect(screen.getByText('Update role')).toBeInTheDocument()
  })

  it('shows loading skeleton when data is loading', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          isLoadingRequests: true,
        },
      } as any)

      // Return the appropriate value based on what selector is being called
      if (typeof result === 'boolean') {
        return true // for isLoadingRequests
      }

      return result
    })

    render(<AllApprovalRequests />)

    expect(screen.getByTestId('custom-skeleton')).toBeInTheDocument()
  })

  it('shows empty state when no data is available', () => {
    render(<AllApprovalRequests />)

    expect(screen.getByRole('button', { name: /filter/i })).toBeInTheDocument()
    expect(
      screen.getByPlaceholderText('Search by Maker first name')
    ).toBeInTheDocument()
  })

  it('handles filter toggle', () => {
    render(<AllApprovalRequests />)

    const filterButton = screen.getByRole('button', { name: /filter/i })
    fireEvent.click(filterButton)

    expect(filterButton).toBeInTheDocument()

    const dateRangePicker = screen.queryByTestId('custom-date-range-picker')
    const dropdownMenus = screen.queryAllByTestId('dropdown-menu')
    expect(dateRangePicker || dropdownMenus.length > 0).toBeTruthy()
  })

  it('handles search input changes', () => {
    render(<AllApprovalRequests />)

    const searchInput = screen.getByPlaceholderText(
      'Search by Maker first name'
    )
    fireEvent.change(searchInput, { target: { value: 'John' } })

    expect(searchInput).toHaveValue('John')
  })

  it('handles pagination changes', async () => {
    render(<AllApprovalRequests />)

    const pageButton = screen.getByText('Page 2')
    fireEvent.click(pageButton)

    expect(pageButton).toBeInTheDocument()
  })

  it('handles page size changes', async () => {
    render(<AllApprovalRequests />)

    const sizeButton = screen.getByText('Size 20')
    fireEvent.click(sizeButton)

    expect(sizeButton).toBeInTheDocument()
  })

  it('handles table sorting', () => {
    render(<AllApprovalRequests />)

    const sortButton = screen.getByTestId('sort-maker')
    fireEvent.click(sortButton)

    expect(sortButton).toBeInTheDocument()
  })

  it('displays status chips correctly', () => {
    render(<AllApprovalRequests />)

    expect(screen.getByText('Approved')).toBeInTheDocument()
    expect(screen.getByText('Rejected')).toBeInTheDocument()
  })

  it('displays formatted dates correctly', () => {
    render(<AllApprovalRequests />)

    // Should display formatted dates
    expect(screen.getByText(/Jan/)).toBeInTheDocument()
  })

  it('renders more menu for each request', () => {
    render(<AllApprovalRequests />)

    expect(screen.getByText('More Menu for 1')).toBeInTheDocument()
    expect(screen.getByText('More Menu for 2')).toBeInTheDocument()
  })

  it('handles filter changes', () => {
    render(<AllApprovalRequests />)

    const filterButton = screen.getByRole('button', { name: /filter/i })
    fireEvent.click(filterButton)

    expect(filterButton).toBeInTheDocument()
  })

  it('handles maker name search', () => {
    render(<AllApprovalRequests />)

    const makerNameInput = screen.getByPlaceholderText(
      'Search by Maker first name'
    )
    fireEvent.change(makerNameInput, { target: { value: 'John' } })

    expect(makerNameInput).toBeInTheDocument()
  })

  it('handles date range selection', () => {
    render(<AllApprovalRequests />)

    const filterButton = screen.getByRole('button', { name: /filter/i })
    fireEvent.click(filterButton)

    const dateRangeButton = screen.getByText('Select Date Range')
    fireEvent.click(dateRangeButton)

    expect(dateRangeButton).toBeInTheDocument()
  })

  it('handles dropdown menu selections', () => {
    render(<AllApprovalRequests />)

    const dropdownMenus = screen.queryAllByTestId('dropdown-menu')
    if (dropdownMenus.length > 0) {
      expect(dropdownMenus[0]).toBeInTheDocument()
    }
  })

  it('displays request types correctly', () => {
    render(<AllApprovalRequests />)

    expect(screen.getByText('Create user')).toBeInTheDocument()
    expect(screen.getByText('Update role')).toBeInTheDocument()
  })

  it('handles empty request types', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          requestTypes: [],
        },
      } as any)

      // Handle different selectors
      if (Array.isArray(result)) {
        return result
      }

      if (typeof result === 'object' && result !== null) {
        // If it's the approvalRequestResponse selector
        if ('data' in result || 'pageNumber' in result) {
          return {
            data: mockApprovalRequests,
            totalElements: 2,
            totalNumberOfPages: 1,
            pageNumber: 1,
            pageSize: 10,
            ...result,
          }
        }
      }

      return result
    })

    render(<AllApprovalRequests />)

    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })
})
