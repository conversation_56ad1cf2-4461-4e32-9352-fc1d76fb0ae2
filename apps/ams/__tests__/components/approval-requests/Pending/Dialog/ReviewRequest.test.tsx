import { render, screen, fireEvent, waitFor } from '../../../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import ReviewRequest from '../../../../../src/app/approval-requests/Pending/Dialog/ReviewRequest'
import { useAppDispatch } from '@/store'
import { IApprovalRequest } from '@/store/interfaces'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
}))

// Mock useCustomRouter
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    prefetch: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    pushWithTrailingSlash: vi.fn(),
  }),
}))

// Mock ApprovalRequestRouting
vi.mock('../../../../../src/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

// Mock utility functions
vi.mock('@dtbx/store/utils', () => ({
  ACCESS_CONTROLS: {
    ACCEPT_APPROVALREQUEST_CUSTOMERS: ['ACCEPT_CUSTOMERS'],
    ACCEPT_APPROVALREQUEST_ROLES: ['ACCEPT_ROLES'],
    ACCEPT_APPROVALREQUEST_USERS: ['ACCEPT_USERS'],
    REJECT_APPROVALREQUEST_CUSTOMERS: ['REJECT_CUSTOMERS'],
    REJECT_APPROVALREQUEST_ROLES: ['REJECT_ROLES'],
    REJECT_APPROVALREQUEST_USERS: ['REJECT_USERS'],
  },
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="access-control-wrapper">{children}</div>
  ),
  formatTimestamp: vi.fn((timestamp) => `Formatted: ${timestamp}`),
  handleDiff: vi.fn((diff) => `Diff: ${JSON.stringify(diff)}`),
}))

// Mock sentenceCase
vi.mock('tiny-case', () => ({
  sentenceCase: vi.fn((str) => {
    if (!str) return ''
    return str
      .replace(/_/g, ' ')
      .toLowerCase()
      .replace(/\b\w/g, (l:string) => l.toUpperCase())
  }),
}))

describe('ReviewRequest', () => {
  const mockDispatch = vi.fn()

  const mockRequest: IApprovalRequest = {
    id: '123',
    maker: 'John Doe',
    dateCreated: '2023-01-01T10:00:00Z',
    dateModified: '2023-01-01T10:00:00Z',
    makerCheckerType: {
      channel: 'WEB',
      checkerPermissions: ['APPROVE_USERS'],
      description: 'Create user request',
      makerPermissions: ['CREATE_USERS'],
      module: 'users',
      name: 'Create User',
      overridePermissions: [],
      type: 'CREATE_USER',
    },
    entityId: 'user-123',
    entity: 'User',
    diff: [
      {
        field: 'firstName',
        oldValue: '',
        newValue: 'John',
      },
      {
        field: 'lastName',
        oldValue: '',
        newValue: 'Doe',
      },
    ],
    makerComments: 'Creating new user for the development team',
    status: 'PENDING',
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
  })

  it('renders the menu item trigger', () => {
    render(<ReviewRequest request={mockRequest} />)

    expect(screen.getByText('See request summary')).toBeInTheDocument()
  })

  it('opens dialog when menu item is clicked', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByText('Approval request details')).toBeInTheDocument()
  })

  it('displays request details in dialog', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('Create User')).toBeInTheDocument()
    expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument()
    expect(
      screen.getByDisplayValue('Formatted: 2023-01-01T10:00:00Z')
    ).toBeInTheDocument()
  })

  it('displays changes made using handleDiff', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const expectedDiff = `Diff: ${JSON.stringify(mockRequest.diff)}`
    expect(screen.getByDisplayValue(expectedDiff)).toBeInTheDocument()
  })

  it('displays maker comments', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(
      screen.getByDisplayValue('Creating new user for the development team')
    ).toBeInTheDocument()
  })

  it('handles request without maker comments', () => {
    const requestWithoutComments: IApprovalRequest = {
      ...mockRequest,
      makerComments: undefined,
    }

    render(<ReviewRequest request={requestWithoutComments} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('No comment')).toBeInTheDocument()
  })

  it('handles request without type', () => {
    const requestWithoutType: IApprovalRequest = {
      ...mockRequest,
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        type: '',
      },
    }

    render(<ReviewRequest request={requestWithoutType} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('')).toBeInTheDocument()
  })

  it('closes dialog when close button is clicked', async () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    // Find the close button by its icon
    const closeButton = screen.getByTestId('CloseRoundedIcon').closest('button')
    fireEvent.click(closeButton!)

    await waitFor(() => {
      expect(
        screen.queryByText('Approval request details')
      ).not.toBeInTheDocument()
    })
  })

  it('closes dialog when Back button is clicked', async () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    await waitFor(() => {
      expect(
        screen.queryByText('Approval request details')
      ).not.toBeInTheDocument()
    })
  })

  it('navigates to review when "Review Request" button is clicked', async () => {
    const { ApprovalRequestRouting } = await import(
      '../../../../../src/app/approval-requests/RequestRouting'
    )

    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const reviewButton = screen.getByText('Review Request')
    fireEvent.click(reviewButton)

    expect(ApprovalRequestRouting).toHaveBeenCalledWith(
      mockRequest,
      mockDispatch,
      expect.any(Object)
    )

    await waitFor(() => {
      expect(
        screen.queryByText('Approval request details')
      ).not.toBeInTheDocument()
    })
  })

  it('wraps Review Request button with AccessControlWrapper', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByTestId('access-control-wrapper')).toBeInTheDocument()
  })

  it('prevents dialog close on backdrop click', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const dialog = screen.getByRole('dialog')
    fireEvent.click(dialog)

    // Dialog should still be open
    expect(screen.getByText('Approval request details')).toBeInTheDocument()
  })

  it('displays all form fields correctly', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    // Check all expected fields are present
    expect(screen.getByLabelText('Approval request type')).toBeInTheDocument()
    expect(screen.getByLabelText('Changes made')).toBeInTheDocument()
    expect(screen.getByLabelText('Maker')).toBeInTheDocument()
    expect(screen.getByLabelText('Maker timestamp')).toBeInTheDocument()
    expect(screen.getByLabelText('Maker comment')).toBeInTheDocument()
  })

  it('renders RequestsApprovalIcon in dialog header', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    // Icon should be present in the dialog
    const dialog = screen.getByRole('dialog')
    expect(dialog).toBeInTheDocument()
  })

  it('handles different request types', () => {
    const roleRequest: IApprovalRequest = {
      ...mockRequest,
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'groups',
        type: 'CREATE_ROLE',
        name: 'Create Role',
      },
    }

    render(<ReviewRequest request={roleRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('Create Role')).toBeInTheDocument()
  })

  it('handles customer requests', () => {
    const customerRequest: IApprovalRequest = {
      ...mockRequest,
      makerCheckerType: {
        ...mockRequest.makerCheckerType,
        module: 'Customers',
        type: 'CREATE_CUSTOMER',
        name: 'Create Customer',
      },
    }

    render(<ReviewRequest request={customerRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    expect(screen.getByDisplayValue('Create Customer')).toBeInTheDocument()
  })

  it('handles empty diff array', () => {
    const requestWithEmptyDiff: IApprovalRequest = {
      ...mockRequest,
      diff: [],
    }

    render(<ReviewRequest request={requestWithEmptyDiff} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const expectedDiff = 'Diff: []'
    expect(screen.getByDisplayValue(expectedDiff)).toBeInTheDocument()
  })

  it('handles complex diff structures', () => {
    const requestWithComplexDiff: IApprovalRequest = {
      ...mockRequest,
      diff: [
        {
          field: 'permissions',
          oldValue: [{ field: 'read', oldValue: 'false', newValue: 'true' }],
          newValue: [
            { field: 'read', oldValue: 'false', newValue: 'true' },
            { field: 'write', oldValue: 'false', newValue: 'true' },
          ],
        },
      ],
    }

    render(<ReviewRequest request={requestWithComplexDiff} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const expectedDiff = `Diff: ${JSON.stringify(requestWithComplexDiff.diff)}`
    expect(screen.getByDisplayValue(expectedDiff)).toBeInTheDocument()
  })

  it('handles keyboard interactions', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')

    // Test clicking the menu item first
    fireEvent.click(menuItem)
    expect(screen.getByText('Approval request details')).toBeInTheDocument()

    // Test Escape key on dialog
    const dialog = screen.getByRole('dialog')
    fireEvent.keyDown(dialog, { key: 'Escape', code: 'Escape' })
  })

  it('maintains dialog state correctly', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')

    // Open dialog
    fireEvent.click(menuItem)
    expect(screen.getByText('Approval request details')).toBeInTheDocument()

    // Close dialog
    const backButton = screen.getByText('Back')
    fireEvent.click(backButton)

    // Reopen dialog
    fireEvent.click(menuItem)
    expect(screen.getByText('Approval request details')).toBeInTheDocument()
  })

  it('displays CallMadeRounded icon in Review Request button', () => {
    render(<ReviewRequest request={mockRequest} />)

    const menuItem = screen.getByText('See request summary')
    fireEvent.click(menuItem)

    const reviewButton = screen.getByText('Review Request')
    expect(reviewButton).toBeInTheDocument()

    // Check if the icon is rendered as an endIcon
    const icon = reviewButton.querySelector('svg')
    expect(icon).toBeInTheDocument()
  })
})
