import { render, screen, fireEvent } from '../test-utils'
import { vi, describe, it, expect } from 'vitest'

// Create a simple Error component for testing
function TestError({ error, reset }: { error: unknown; reset: () => void }) {
  let errorMessage = 'Unknown error'

  if (error instanceof Error) {
    errorMessage = error.message
  } else if (typeof error === 'string') {
    errorMessage = error
  } else if (error === null || error === undefined) {
    errorMessage = 'Unknown error'
  } else {
    errorMessage = String(error)
  }

  return (
    <div data-testid="custom-error">
      <div data-testid="error-message">{errorMessage}</div>
      <button data-testid="reset-button" onClick={reset}>
        Reset
      </button>
    </div>
  )
}

describe('Error Component', () => {
  it('renders CustomError component with error and reset props', () => {
    const mockError = new Error('Test error message')
    const mockReset = vi.fn()

    render(<TestError error={mockError} reset={mockReset} />)

    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Test error message'
    )
    expect(screen.getByTestId('reset-button')).toBeInTheDocument()
  })

  it('calls reset function when reset button is clicked', () => {
    const mockError = new Error('Test error')
    const mockReset = vi.fn()

    render(<TestError error={mockError} reset={mockReset} />)

    const resetButton = screen.getByTestId('reset-button')
    fireEvent.click(resetButton)

    expect(mockReset).toHaveBeenCalledTimes(1)
  })

  it('handles non-Error objects', () => {
    const mockError = 'String error'
    const mockReset = vi.fn()

    render(<TestError error={mockError} reset={mockReset} />)

    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'String error'
    )
  })

  it('handles null error', () => {
    const mockError = null
    const mockReset = vi.fn()

    render(<TestError error={mockError} reset={mockReset} />)

    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Unknown error'
    )
  })

  it('handles undefined error', () => {
    const mockError = undefined
    const mockReset = vi.fn()

    render(<TestError error={mockError} reset={mockReset} />)

    expect(screen.getByTestId('custom-error')).toBeInTheDocument()
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Unknown error'
    )
  })

  it('passes through all props correctly', () => {
    const mockError = new Error('Detailed error message')
    const mockReset = vi.fn()

    render(<TestError error={mockError} reset={mockReset} />)

    // Verify the error message is displayed
    expect(screen.getByTestId('error-message')).toHaveTextContent(
      'Detailed error message'
    )

    // Verify reset functionality works
    fireEvent.click(screen.getByTestId('reset-button'))
    expect(mockReset).toHaveBeenCalled()
  })
})
