import { render, screen, fireEvent } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import PageHeader from '../../../src/app/users/approval-requests/pageHeader'
import { useAppDispatch, useAppSelector } from '@/store'
import * as approvalRequestsActions from '@/store/actions/approvalRequests'
import * as approvalRequestsReducers from '@/store/reducers/ApprovalRequests'

// Mock the store hooks
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

// Mock the actions
vi.mock('@/store/actions/approvalRequests', () => ({
  getApprovals: vi.fn(),
}))

// Mock the reducers
vi.mock('@/store/reducers/ApprovalRequests', () => ({
  setUserApprovalRequestsFilters: vi.fn(),
}))

// Mock the CustomFilterBox component
vi.mock('@/app/approval-requests/CustomFilterBox', () => ({
  CustomFilterBox: ({
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    onFilterChange,
    setMakerName,
    searchByValues,
    setSearchByValue,
  }: any) => (
    <div data-testid="custom-filter-box">
      <input
        data-testid="search-input"
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
      />
      <button
        data-testid="filter-toggle"
        onClick={() => setOpenFilter(!openFilter)}
      >
        {openFilter ? 'Close Filter' : 'Open Filter'}
      </button>
      <button
        data-testid="apply-filters"
        onClick={() => onFilterChange({ module: 'users', status: 'PENDING' })}
      >
        Apply Filters
      </button>
      <button
        data-testid="set-maker-name"
        onClick={() => setMakerName('John Doe')}
      >
        Set Maker Name
      </button>
      <div data-testid="search-by-values">
        {searchByValues?.join(', ')}
      </div>
      <button
        data-testid="set-search-by"
        onClick={() => setSearchByValue('email')}
      >
        Set Search By
      </button>
      <div data-testid="filters-count">{filters?.length || 0}</div>
    </div>
  ),
}))

describe('PageHeader', () => {
  const mockDispatch = vi.fn()
  const mockGetApprovals = vi.fn()
  const mockSetUserApprovalRequestsFilters = vi.fn()

  const mockRequestTypes = [
    {
      id: '1',
      name: 'CREATE_USER',
      description: 'Create user request',
    },
    {
      id: '2',
      name: 'UPDATE_USER',
      description: 'Update user request',
    },
    {
      id: '3',
      name: 'CREATE_GROUP',
      description: 'Create group request',
    },
  ]

  const mockState = {
    approvalRequests: {
      requestTypes: mockRequestTypes,
      userApprovalRequestFilters: { module: 'users' },
    },
    customers: {
      search: {
        searchBy: ['firstName'],
        searchValue: '',
      },
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector(mockState as any)
      return result
    })
    vi.mocked(approvalRequestsActions.getApprovals).mockImplementation(mockGetApprovals)
    vi.mocked(approvalRequestsReducers.setUserApprovalRequestsFilters).mockImplementation(mockSetUserApprovalRequestsFilters)
  })

  it('renders the page header with filter box', () => {
    render(<PageHeader />)

    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
    expect(screen.getByTestId('search-input')).toBeInTheDocument()
    expect(screen.getByTestId('filter-toggle')).toBeInTheDocument()
  })

  it('handles search input changes', () => {
    render(<PageHeader />)

    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'test search' } })

    // The search value should be updated in the component state
    expect(searchInput).toHaveValue('test search')
  })

  it('handles filter toggle', () => {
    render(<PageHeader />)

    const filterToggle = screen.getByTestId('filter-toggle')
    expect(filterToggle).toHaveTextContent('Open Filter')

    fireEvent.click(filterToggle)
    expect(filterToggle).toHaveTextContent('Close Filter')
  })

  it('handles filter changes and calls getApprovals', async () => {
    render(<PageHeader />)

    const applyFiltersButton = screen.getByTestId('apply-filters')
    fireEvent.click(applyFiltersButton)

    expect(mockDispatch).toHaveBeenCalledWith(
      mockSetUserApprovalRequestsFilters({ module: 'users', status: 'PENDING' })
    )
    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      '?channel=DBP&module=users&status=PENDING&page=1&size=10'
    )
  })

  it('handles empty filter changes', async () => {
    render(<PageHeader />)

    // Mock empty filters
    const customFilterBox = screen.getByTestId('custom-filter-box')
    const applyButton = customFilterBox.querySelector('[data-testid="apply-filters"]')
    
    // Simulate applying empty filters
    fireEvent.click(applyButton!)

    expect(mockDispatch).toHaveBeenCalled()
  })

  it('handles maker name search', async () => {
    render(<PageHeader />)

    const setMakerNameButton = screen.getByTestId('set-maker-name')
    fireEvent.click(setMakerNameButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      expect.stringContaining('makerFirstName=John Doe')
    )
  })

  it('renders correct search by values', () => {
    render(<PageHeader />)

    const searchByValues = screen.getByTestId('search-by-values')
    expect(searchByValues).toHaveTextContent('Maker, Checker, Request Type, module')
  })

  it('handles search by value changes', () => {
    render(<PageHeader />)

    const setSearchByButton = screen.getByTestId('set-search-by')
    fireEvent.click(setSearchByButton)

    // The setSearchByValue function should be called
    expect(setSearchByButton).toBeInTheDocument()
  })

  it('renders correct number of filters', () => {
    render(<PageHeader />)

    const filtersCount = screen.getByTestId('filters-count')
    expect(filtersCount).toHaveTextContent('3') // module, status, requestType
  })

  it('filters request types correctly', () => {
    render(<PageHeader />)

    // The component should filter request types to only include users and groups
    const filtersCount = screen.getByTestId('filters-count')
    expect(filtersCount).toHaveTextContent('3')
  })

  it('handles search with lastName', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        customers: {
          search: {
            searchBy: ['lastName'],
            searchValue: '',
          },
        },
      } as any)
      return result
    })

    render(<PageHeader />)

    const setMakerNameButton = screen.getByTestId('set-maker-name')
    fireEvent.click(setMakerNameButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      expect.stringContaining('makerLastName=John Doe')
    )
  })

  it('handles search with other search types', () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        customers: {
          search: {
            searchBy: ['email'],
            searchValue: '',
          },
        },
      } as any)
      return result
    })

    render(<PageHeader />)

    const setMakerNameButton = screen.getByTestId('set-maker-name')
    fireEvent.click(setMakerNameButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      expect.stringContaining('page=1&size=10')
    )
  })

  it('handles filter changes with existing search value', async () => {
    render(<PageHeader />)

    // First set a search value
    const searchInput = screen.getByTestId('search-input')
    fireEvent.change(searchInput, { target: { value: 'test maker' } })

    // Then apply filters
    const applyFiltersButton = screen.getByTestId('apply-filters')
    fireEvent.click(applyFiltersButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      expect.stringContaining('makerFirstName=test maker')
    )
  })

  it('handles maker name search with existing filters', async () => {
    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          userApprovalRequestFilters: {
            module: 'users',
            status: 'PENDING',
          },
        },
      } as any)
      return result
    })

    render(<PageHeader />)

    const setMakerNameButton = screen.getByTestId('set-maker-name')
    fireEvent.click(setMakerNameButton)

    expect(mockGetApprovals).toHaveBeenCalledWith(
      mockDispatch,
      expect.stringContaining('module=users&status=PENDING')
    )
  })

  it('handles empty maker name search', async () => {
    render(<PageHeader />)

    // Mock setting empty maker name
    const customFilterBox = screen.getByTestId('custom-filter-box')
    
    // Simulate the setMakerName being called with empty string
    const setMakerNameButton = screen.getByTestId('set-maker-name')
    
    // We need to test the actual function behavior, but since it's mocked,
    // we just verify the component renders correctly
    expect(setMakerNameButton).toBeInTheDocument()
  })

  it('renders with correct stack layout', () => {
    render(<PageHeader />)

    // Check that the main container exists
    const customFilterBox = screen.getByTestId('custom-filter-box')
    expect(customFilterBox).toBeInTheDocument()
  })

  it('handles request types filtering for users and groups', () => {
    const requestTypesWithMixed = [
      { id: '1', name: 'CREATE_USER', description: 'Create user' },
      { id: '2', name: 'CREATE_CUSTOMER', description: 'Create customer' },
      { id: '3', name: 'UPDATE_GROUP', description: 'Update group' },
      { id: '4', name: 'DELETE_PRODUCT', description: 'Delete product' },
    ]

    vi.mocked(useAppSelector).mockImplementation((selector) => {
      const result = selector({
        ...mockState,
        approvalRequests: {
          ...mockState.approvalRequests,
          requestTypes: requestTypesWithMixed,
        },
      } as any)
      return result
    })

    render(<PageHeader />)

    // Should still render the filter box
    expect(screen.getByTestId('custom-filter-box')).toBeInTheDocument()
  })
})
