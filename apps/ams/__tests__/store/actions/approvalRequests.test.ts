import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { getApprovals, getApprovalRequestTypes } from '@/store/actions/approvalRequests'
import {
  setApprovalRequestResponse,
  setApprovals,
  setLoadingApprovals,
  setLoadingRequestTypes,
  setRequestTypes,
  setRequestTypesSuccess,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi } from '@dtbx/store/utils'

// Mock the dependencies
vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
  },
}))

vi.mock('@/store/reducers', () => ({
  setApprovalRequestResponse: vi.fn(),
  setApprovals: vi.fn(),
  setLoadingApprovals: vi.fn(),
  setLoadingRequestTypes: vi.fn(),
  setRequestTypes: vi.fn(),
  setRequestTypesSuccess: vi.fn(),
}))

vi.mock('@dtbx/store/reducers', () => ({
  setNotification: vi.fn(),
}))

describe('Approval Requests Actions', () => {
  const mockDispatch = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getApprovals', () => {
    it('should dispatch success actions when API call succeeds', async () => {
      const mockResponse = {
        data: {
          data: [
            {
              id: '1',
              requestType: 'CREATE_USER',
              status: 'PENDING',
              createdBy: 'user1',
              createdAt: '2023-01-01',
            },
          ],
          totalElements: 1,
          totalPages: 1,
        },
      }

      vi.mocked(secureapi.get).mockResolvedValue(mockResponse)

      await getApprovals(mockDispatch)

      expect(setLoadingApprovals).toHaveBeenCalledWith(true)
      expect(secureapi.get).toHaveBeenCalledWith('/backoffice-auth/maker-checker/approvals')
      expect(setApprovals).toHaveBeenCalledWith(mockResponse.data.data)
      expect(setApprovalRequestResponse).toHaveBeenCalledWith(mockResponse.data)
      expect(setLoadingApprovals).toHaveBeenCalledWith(false)
    })

    it('should dispatch success actions with params when provided', async () => {
      const mockResponse = {
        data: {
          data: [],
          totalElements: 0,
          totalPages: 0,
        },
      }
      const params = '?page=1&size=10'

      vi.mocked(secureapi.get).mockResolvedValue(mockResponse)

      await getApprovals(mockDispatch, params)

      expect(secureapi.get).toHaveBeenCalledWith(`/backoffice-auth/maker-checker/approvals${params}`)
    })

    it('should dispatch error actions when API call fails', async () => {
      const errorMessage = 'Network Error'
      vi.mocked(secureapi.get).mockRejectedValue(new Error(errorMessage))

      await getApprovals(mockDispatch)

      expect(setLoadingApprovals).toHaveBeenCalledWith(true)
      expect(setNotification).toHaveBeenCalledWith({
        message: errorMessage,
        type: 'error',
      })
      expect(setLoadingApprovals).toHaveBeenCalledWith(false)
    })
  })

  describe('getApprovalRequestTypes', () => {
    it('should dispatch success actions when API call succeeds', async () => {
      const mockResponse = {
        data: [
          { id: '1', name: 'CREATE_USER', description: 'Create user request' },
          { id: '2', name: 'UPDATE_ROLE', description: 'Update role request' },
        ],
      }

      vi.mocked(secureapi.get).mockResolvedValue(mockResponse)

      await getApprovalRequestTypes(mockDispatch)

      expect(setLoadingRequestTypes).toHaveBeenCalledWith(true)
      expect(setRequestTypesSuccess).toHaveBeenCalledWith(false)
      expect(secureapi.get).toHaveBeenCalledWith('/backoffice-auth/maker-checker/types')
      expect(setRequestTypes).toHaveBeenCalledWith(mockResponse.data)
      expect(setRequestTypesSuccess).toHaveBeenCalledWith(true)
      expect(setLoadingRequestTypes).toHaveBeenCalledWith(false)
    })

    it('should dispatch success actions with channel when provided', async () => {
      const mockResponse = { data: [] }
      const channel = 'DBP'

      vi.mocked(secureapi.get).mockResolvedValue(mockResponse)

      await getApprovalRequestTypes(mockDispatch, channel)

      expect(secureapi.get).toHaveBeenCalledWith(`/backoffice-auth/maker-checker/types?channel=${channel}`)
    })

    it('should dispatch error actions when API call fails', async () => {
      const errorMessage = 'API Error'
      vi.mocked(secureapi.get).mockRejectedValue(new Error(errorMessage))

      await getApprovalRequestTypes(mockDispatch)

      expect(setLoadingRequestTypes).toHaveBeenCalledWith(true)
      expect(setRequestTypesSuccess).toHaveBeenCalledWith(false)
      expect(setNotification).toHaveBeenCalledWith({
        message: errorMessage,
        type: 'error',
      })
      expect(setLoadingRequestTypes).toHaveBeenCalledWith(false)
      expect(setRequestTypesSuccess).toHaveBeenCalledWith(false)
    })
  })
})
