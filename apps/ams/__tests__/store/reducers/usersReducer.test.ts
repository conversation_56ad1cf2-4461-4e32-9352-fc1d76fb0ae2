import { describe, it, expect } from 'vitest'
import usersReducer, {
  setUsersResponse,
  setOpenCreateUser,
  setOpenEditUser,
  setIsLoadingCreateUser,
  setIsLoadingEditUser,
  setIsLoadingUsers,
  resetUsersStore,
  setUserSearchValue,
  setUserFilterValue,
  setSingleUserData,
  setRoleUsers,
  setSelectedUser,
  setLoadedADUserDetails,
  setLoadingADSuccess,
  setLoadingADFailure,
  setLoadingADUserDetails,
  setGeneratedUserReportLoading,
  setGeneratedUserReportSuccess,
  setGeneratedUserReportFailure,
  type UsersReducersProps,
} from '@/store/reducers/usersReducer'
import { IUsersResponse, IUser, IADUserProfile } from '@/store/interfaces'

describe('usersReducer', () => {
  const initialState: UsersReducersProps = {
    usersResponse: {
      pageNumber: 0,
      pageSize: 0,
      totalNumberOfPages: 0,
      totalElements: 0,
      data: [],
    },
    isLoadingUsers: false,
    isLoadingCreateUser: false,
    isLoadingEditUser: false,
    openCreateUSer: false,
    openEditUser: false,
    selectedUser: {} as IUser,
    searchUserValue: '',
    userFilterValue: {},
    singleUserData: {} as IUser,
    roleUsers: {} as IUsersResponse,
    isLoadingADUserDetails: false,
    isLoadedADUserDetailsSuccess: false,
    isLoadedADUserDetailsFailure: false,
    loadedADUserDetails: [],
    isLoadingRoleUsers: false,
    isSuccessRolelUsers: false,
    isErrorRoleUsers: false,
    isGeneratedUserReportLoading: false,
    isGeneratedUserReportSuccess: false,
    isGeneratedUserReportFailure: false,
    isGeneratedUserReport: false,
  }

  it('should return the initial state', () => {
    expect(usersReducer(undefined, { type: 'unknown' })).toEqual(initialState)
  })

  it('should handle setUsersResponse', () => {
    const usersResponse: IUsersResponse = {
      pageNumber: 1,
      pageSize: 10,
      totalNumberOfPages: 5,
      totalElements: 50,
      data: [
        {
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          username: 'johndoe',
          roles: [],
          isActive: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
        },
      ],
    }
    const actual = usersReducer(initialState, setUsersResponse(usersResponse))
    expect(actual.usersResponse).toEqual(usersResponse)
  })

  it('should handle setOpenCreateUser', () => {
    const actual = usersReducer(initialState, setOpenCreateUser(true))
    expect(actual.openCreateUSer).toBe(true)
  })

  it('should handle setOpenEditUser', () => {
    const actual = usersReducer(initialState, setOpenEditUser(true))
    expect(actual.openEditUser).toBe(true)
  })

  it('should handle loading states', () => {
    expect(usersReducer(initialState, setIsLoadingCreateUser(true)).isLoadingCreateUser).toBe(true)
    expect(usersReducer(initialState, setIsLoadingEditUser(true)).isLoadingEditUser).toBe(true)
    expect(usersReducer(initialState, setIsLoadingUsers(true)).isLoadingUsers).toBe(true)
  })

  it('should handle setUserSearchValue', () => {
    const searchValue = 'john'
    const actual = usersReducer(initialState, setUserSearchValue(searchValue))
    expect(actual.searchUserValue).toBe(searchValue)
  })

  it('should handle setUserFilterValue', () => {
    const filterValue = { role: 'admin', status: 'active' }
    const actual = usersReducer(initialState, setUserFilterValue(filterValue))
    expect(actual.userFilterValue).toEqual(filterValue)
  })

  it('should handle setSingleUserData', () => {
    const user: IUser = {
      id: '1',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      username: 'janesmith',
      roles: [],
      isActive: true,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    }
    const actual = usersReducer(initialState, setSingleUserData(user))
    expect(actual.singleUserData).toEqual(user)
  })

  it('should handle setRoleUsers', () => {
    const roleUsers: IUsersResponse = {
      pageNumber: 1,
      pageSize: 5,
      totalNumberOfPages: 2,
      totalElements: 10,
      data: [
        {
          id: '2',
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          username: 'admin',
          roles: [],
          isActive: true,
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
        },
      ],
    }
    const actual = usersReducer(initialState, setRoleUsers(roleUsers))
    expect(actual.roleUsers).toEqual(roleUsers)
  })

  it('should handle setSelectedUser', () => {
    const user: IUser = {
      id: '3',
      firstName: 'Selected',
      lastName: 'User',
      email: '<EMAIL>',
      username: 'selected',
      roles: [],
      isActive: false,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01',
    }
    const actual = usersReducer(initialState, setSelectedUser(user))
    expect(actual.selectedUser).toEqual(user)
  })

  it('should handle setLoadedADUserDetails', () => {
    const adUsers: IADUserProfile[] = [
      {
        id: '1',
        displayName: 'John Doe',
        userPrincipalName: '<EMAIL>',
        mail: '<EMAIL>',
        givenName: 'John',
        surname: 'Doe',
      },
    ]
    const actual = usersReducer(initialState, setLoadedADUserDetails(adUsers))
    expect(actual.loadedADUserDetails).toEqual(adUsers)
  })

  it('should handle AD user details loading states', () => {
    expect(usersReducer(initialState, setLoadingADUserDetails(true)).isLoadingADUserDetails).toBe(true)
    expect(usersReducer(initialState, setLoadingADSuccess(true)).isLoadedADUserDetailsSuccess).toBe(true)
    expect(usersReducer(initialState, setLoadingADFailure(true)).isLoadedADUserDetailsFailure).toBe(true)
  })

  it('should handle user report states', () => {
    expect(usersReducer(initialState, setGeneratedUserReportLoading(true)).isGeneratedUserReportLoading).toBe(true)
    expect(usersReducer(initialState, setGeneratedUserReportSuccess(true)).isGeneratedUserReportSuccess).toBe(true)
    expect(usersReducer(initialState, setGeneratedUserReportFailure(true)).isGeneratedUserReportFailure).toBe(true)
  })

  it('should handle resetUsersStore', () => {
    const modifiedState = {
      ...initialState,
      usersResponse: {
        pageNumber: 1,
        pageSize: 10,
        totalNumberOfPages: 5,
        totalElements: 50,
        data: [{ id: '1', firstName: 'Test' } as IUser],
      },
      isLoadingUsers: true,
      searchUserValue: 'test',
      openCreateUSer: true,
    }
    const actual = usersReducer(modifiedState, resetUsersStore())
    expect(actual).toEqual(initialState)
  })
})
