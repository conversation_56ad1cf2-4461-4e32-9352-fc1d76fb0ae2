import { render, screen } from '../../test-utils'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import RolesPage from '../../../src/app/roles/page'
import { useAppDispatch, useAppSelector } from '@/store'
import { getRolesFilter } from '@/store/actions'

// Mock dependencies
vi.mock('@/store', () => ({
  useAppDispatch: vi.fn(),
  useAppSelector: vi.fn(),
}))

vi.mock('@/store/actions', () => ({
  getRolesFilter: vi.fn(),
  getPermissionsGroup: vi.fn(),
}))

// Mock components
vi.mock('@dtbx/ui/components', () => ({
  PageHeader: ({
    title,
    children,
  }: {
    title: string
    children: React.ReactNode
  }) => (
    <div data-testid="page-header">
      <h1>{title}</h1>
      <div>{children}</div>
    </div>
  ),
  DataTable: ({
    columns,
    data,
    onRowClick,
  }: {
    columns: Array<{ field: string; headerName: string }>
    data: Array<Record<string, any>>
    onRowClick?: (row: any) => void
  }) => (
    <div data-testid="data-table">
      <table>
        <thead>
          <tr>
            {columns.map((col: { field: string; headerName: string }) => (
              <th key={col.field}>{col.headerName}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row: any, index: number) => (
            <tr key={index} onClick={() => onRowClick && onRowClick(row)}>
              {columns.map((col: { field: string; headerName: string }) => (
                <td key={col.field}>{row[col.field]}</td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
  Button: ({
    children,
    onClick,
  }: {
    children: React.ReactNode
    onClick: () => void
  }) => (
    <button data-testid="button" onClick={onClick}>
      {children}
    </button>
  ),
  SearchInput: ({ onChange }: { onChange: (value: string) => void }) => (
    <input
      data-testid="search-input"
      onChange={(e) => onChange(e.target.value)}
    />
  ),
  FilterDrawer: ({
    children,
    open,
    onClose,
  }: {
    children: React.ReactNode
    open: boolean
    onClose: () => void
  }) => (
    <div
      data-testid="filter-drawer"
      style={{ display: open ? 'block' : 'none' }}
    >
      {children}
      <button onClick={onClose}>Close</button>
    </div>
  ),
  LoadingListsSkeleton: () => (
    <div data-testid="loading-skeleton">Loading...</div>
  ),
  AccessControlWrapper: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}))

vi.mock('../../../src/app/roles/EmptyRoles', () => ({
  EmptyRoles: () => <div data-testid="empty-roles">No roles found</div>,
}))

vi.mock('../../../src/app/roles/ListRoles', () => ({
  ListRoles: ({
    page,
    setPage,
    onExport,
  }: {
    page: number
    setPage: (page: number) => void
    onExport: (ids: string[]) => void
  }) => (
    <div data-testid="list-roles">
      <div>Roles list</div>
      <div>Page: {page}</div>
      <button onClick={() => setPage(page + 1)}>Next Page</button>
      <button onClick={() => onExport(['role1', 'role2'])}>Export</button>
    </div>
  ),
}))

vi.mock('../../../src/app/roles/PageHeader', () => ({
  PageHeader: ({
    search,
    filter,
    selectedIds,
  }: {
    search: (value: string) => void
    filter: (value: Record<string, string | string[]>) => void
    selectedIds: string[]
  }) => (
    <div data-testid="roles-page-header">
      <div>Roles Header</div>
      <button onClick={() => search('test')}>Search</button>
      <button onClick={() => filter({ test: 'value' })}>Filter</button>
      <div>Selected: {selectedIds?.length || 0}</div>
    </div>
  ),
}))

describe('RolesPage', () => {
  const mockDispatch = vi.fn()
  const mockRoles = [
    {
      id: '1',
      name: 'Admin',
      description: 'Administrator role',
      creationDate: '2023-01-01',
      custom: true,
    },
    {
      id: '2',
      name: 'User',
      description: 'Regular user role',
      creationDate: '2023-01-02',
      custom: false,
    },
  ]

  beforeEach(() => {
    vi.clearAllMocks()
    vi.mocked(useAppDispatch).mockReturnValue(mockDispatch)
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        tableListRoles: mockRoles,
        isLoadingTableListRoles: false,
        totalElements: 20,
        totalNumberOfPages: 2,
        pageNumber: 1,
        pageSize: 10,
        permissionsGroup: [],
      }
    })
  })

  it('renders the Roles page with header', () => {
    render(<RolesPage />)

    expect(screen.getByTestId('roles-page-header')).toBeInTheDocument()
  })

  it('dispatches getRolesFilter on initial render', () => {
    render(<RolesPage />)

    expect(getRolesFilter).toHaveBeenCalledWith(
      mockDispatch,
      expect.objectContaining({
        page: 1,
        size: 10,
      })
    )
  })

  it('renders the list roles component when roles are available', () => {
    render(<RolesPage />)

    expect(screen.getByTestId('list-roles')).toBeInTheDocument()
  })

  it('renders the empty roles component when no roles are available', () => {
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        tableListRoles: [],
        isLoadingTableListRoles: false,
        totalElements: 0,
        totalNumberOfPages: 0,
        pageNumber: 1,
        pageSize: 10,
        permissionsGroup: [],
      }
    })

    render(<RolesPage />)

    expect(screen.getByTestId('empty-roles')).toBeInTheDocument()
  })

  it('renders loading skeleton when isLoading is true', () => {
    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        tableListRoles: [],
        isLoadingTableListRoles: true,
        totalElements: 0,
        totalNumberOfPages: 0,
        pageNumber: 1,
        pageSize: 10,
        permissionsGroup: [],
      }
    })

    render(<RolesPage />)

    const skeletons = document.querySelectorAll('.MuiSkeleton-root')
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('handles search functionality through PageHeader', () => {
    render(<RolesPage />)

    const pageHeader = screen.getByTestId('roles-page-header')
    expect(pageHeader).toBeInTheDocument()

    const searchButton = screen.getByText('Search')
    expect(searchButton).toBeInTheDocument()
  })

  it('handles filter functionality through PageHeader', () => {
    render(<RolesPage />)

    const pageHeader = screen.getByTestId('roles-page-header')
    expect(pageHeader).toBeInTheDocument()

    const filterButton = screen.getByText('Filter')
    expect(filterButton).toBeInTheDocument()
  })

  it('displays selected roles count in PageHeader', () => {
    render(<RolesPage />)

    const pageHeader = screen.getByTestId('roles-page-header')
    expect(pageHeader).toBeInTheDocument()

    expect(screen.getByText('Selected: 0')).toBeInTheDocument()
  })

  it('renders with different role data structures', () => {
    const customMockRoles = [
      {
        id: '3',
        name: 'Custom Role',
        description: 'A custom role for testing',
        permissions: [],
        custom: true,
      },
    ]

    vi.mocked(useAppSelector).mockImplementation((_selector) => {
      return {
        tableListRoles: customMockRoles,
        isLoadingTableListRoles: false,
        totalElements: 1,
        totalNumberOfPages: 1,
        pageNumber: 1,
        pageSize: 10,
        permissionsGroup: [],
      }
    })

    render(<RolesPage />)

    expect(screen.getByTestId('list-roles')).toBeInTheDocument()
  })
})
