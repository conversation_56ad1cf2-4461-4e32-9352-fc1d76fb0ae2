'use client'
import { CircularProgress, Stack } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { setRoleFilterValue, setRoleSearchValue } from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { FileFormat } from '@/store/interfaces'
import { IFilter, IFilterOption } from '@dtbx/store/interfaces'
import { generateRoleReports } from '@/store/actions'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'
import { ExportButton } from '@dtbx/ui/components/ExportButton'
import { ExportPreferences } from '@dtbx/ui/components/Dialogs'
import { CreateRole } from './CreateRole'

interface PageHeaderProps {
  selectedIds: string[]
  search: (value: string) => void
  filter: (value: Record<string, string | string[]>) => void
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  selectedIds,
  search,
  filter,
}) => {
  const dispatch = useAppDispatch()
  const { rolesCount } = useAppSelector((state) => state.roles)
  const [open, setOpen] = useState(false)
  const [loading, setLoading] = React.useState(false)
  const modules = useAppSelector((state) => state.roles.permissionGroup)
  const [searchValue, setSearchValue] = useState<string>('')
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
    dispatch(setRoleSearchValue(e.target.value))
    search(e.target.value)
  }
  const handleFilterChange = (filters: Record<string, string | string[]>) => {
    dispatch(setRoleFilterValue(filters))
    filter(filters)
  }
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const [exportCount, setExportCount] = useState<number>(0)
  const filters: IFilter[] = [
    {
      filterName: 'Is Visible',
      options: [
        { key: 'yes', value: 'yes', label: 'Yes' },
        { key: 'no', value: 'no', label: 'No' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'Module',
      options: modules.data
        ? [
            ...modules.data.map((module) => {
              return {
                key: module.name,
                value: module.name,
                label: sentenceCase(module.name),
              }
            }),
          ]
        : [],
      type: 'dropdown/single',
    },
    {
      filterName: 'Rights',
      options: modules.data
        ? ([
            ...modules.data.map((module) => {
              return module.permissions.map((permission) => {
                return {
                  key: permission.name,
                  value: permission.name,
                  label: sentenceCase(permission.name),
                }
              })
            }),
          ].flat(Infinity) as IFilterOption[])
        : [],
      type: 'dropdown/single',
    },
  ]
  useEffect(() => {
    setExportCount(rolesCount || 0)
  }, [rolesCount])

  const handleExportClick = () => {
    setOpen(true)
  }
  const handleExport = async (format: FileFormat) => {
    setOpen(false)
    setLoading(true)

    const filteredData = selectedIds.map((id) => ({ id }))

    // Export logic based on selected format
    await generateRoleReports({
      dispatch,
      params: {
        page: 1,
        size: 10,
        Rights: '',
        Modules: '',
        UsersAssigned: '',
      },
      format: format,
      filteredData: filteredData,
    })

    // Simulate export process
    await new Promise((resolve) => setTimeout(resolve, 3000))
    setLoading(false)
    dispatch(
      setNotification({
        message: `${exportCount} items successfully exported.`,
        type: 'success',
      })
    )
  }

  const handleCancel = () => {
    setOpen(false)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'row',
        justifyContent: 'space-between',
        my: '17px',
      }}
    >
      <Stack
        sx={{
          flexGrow: 1,
        }}
      >
        <CustomFilterBox
          openFilter={openFilter}
          setOpenFilter={setOpenFilter}
          searchValue={searchValue}
          handleSearch={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
        />
      </Stack>
      <Stack
        sx={{
          px: '10px',
        }}
      >
        <ExportButton
          onClick={handleExportClick}
          text={`Export All ${exportCount}`}
          openFilter={openFilter}
        />
      </Stack>
      <ExportPreferences
        open={open}
        onExport={handleExport}
        onCancel={handleCancel}
        setOpen={setOpen}
        selectedIds={[]}
      />

      <Stack>
        <CreateRole />
      </Stack>
      {loading && (
        <Stack
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.5)',
            zIndex: 10,
          }}
        >
          <CircularProgress size={40} sx={{ color: '#1976d2' }} />
        </Stack>
      )}
    </Stack>
  )
}
