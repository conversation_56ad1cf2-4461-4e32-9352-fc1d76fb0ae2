import { Button, Stack, Typography } from '@mui/material'
import CancelIcon from '@mui/icons-material/Cancel'
import CheckCircleRoundedIcon from '@mui/icons-material/CheckCircleRounded'
import { useAppDispatch } from '@/store'
import { setCreateRoleFailed, setCreateRoleSuccess } from '@/store/reducers'

export const UserRoleCreateSuccess = ({
  handleCloseDrawer,
}: {
  handleCloseDrawer: () => void
}) => {
  const dispatch = useAppDispatch()
  return (
    <Stack
      sx={{
        width: '100%',
        flexDirection: 'column',
        alignItems: 'center',
        alignContent: 'center',
        gap: '3%',
        py: '20%',
      }}
    >
      <CheckCircleRoundedIcon
        sx={{
          color: 'error.main',
          fontSize: '10vw',
        }}
      />
      <Typography
        variant="subtitle2"
        sx={{
          color: 'primary.main',
          fontWeight: 700,
        }}
      >
        New role has been created
      </Typography>
      <Typography
        sx={{
          textWrap: 'wrap',
          color: 'primary.primary3',
        }}
      >
        The role has been created and can be assigned to other users.
      </Typography>
      <Button
        variant="outlined"
        onClick={() => {
          handleCloseDrawer()
          dispatch(setCreateRoleSuccess(false))
        }}
      >
        Okay, got it!
      </Button>
    </Stack>
  )
}

export const UserRoleCreateFailure = () => {
  const dispatch = useAppDispatch()
  return (
    <Stack
      sx={{
        width: '100%',
        flexDirection: 'column',
        alignItems: 'center',
        alignContent: 'center',
        gap: '3%',
        py: '20%',
      }}
    >
      <CancelIcon
        sx={{
          color: 'error.main',
          fontSize: '10vw',
        }}
      />
      <Typography
        variant="subtitle2"
        sx={{
          color: 'primary.main',
          fontWeight: 700,
        }}
      >
        New role creation failed
      </Typography>
      <Typography
        sx={{
          textWrap: 'wrap',
          color: 'primary.primary3',
        }}
      >
        Role was not created.
      </Typography>
      <Button
        variant="outlined"
        onClick={() => {
          dispatch(setCreateRoleFailed(false))
        }}
      >
        Okay, got it!
      </Button>
    </Stack>
  )
}
