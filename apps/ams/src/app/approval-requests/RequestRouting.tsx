'use client'

import { Dispatch } from '@reduxjs/toolkit'
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime'
import {
  IApprovalRequest,
  ICustomer,
  ICustomerAccount,
} from '@/store/interfaces'
import {
  getCustomerAccountByAccountNo,
  getCustomerDeviceDetail,
  getCustomerPinDetails,
  getCustomerProfile,
  getRoleById,
  getUserById,
} from '@/store/actions'
import {
  setChangeTab,
  setCustomer,
  setCustomerAccountsList,
  setCustomerApprovalBarOpen,
  setIsViewAccountOpen,
  setOpenDevice,
  setSelectedApprovalRequest,
} from '@/store/reducers'
import { setNotification, setSwitchToRoleDetails } from '@dtbx/store/reducers'
import {
  extractFields,
  isAccountLinkingApprovalRequest,
} from '@dtbx/store/utils'

export const ApprovalRequestRouting = async (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => {
  const handler = handlers[request.makerCheckerType.module]
  if (handler) {
    if (typeof handler === 'function') {
      await handler(request, dispatch, router)
    } else {
      const subHandler = handler[request.makerCheckerType.type]
      if (subHandler) {
        await subHandler(request, dispatch, router)
      }
    }
  }
}
type Handler = (
  request: IApprovalRequest,
  dispatch: Dispatch,
  router: AppRouterInstance
) => Promise<void>
type Handlers = {
  [key: string]: Handler | { [key: string]: Handler }
}

const handlers: Handlers = {
  users: async (request, dispatch, router) => {
    if (request.entityId) await getUserById(dispatch, request.entityId)
    dispatch(setSelectedApprovalRequest(request))
    router.push('/users/details')
  },
  groups: async (request, dispatch, router) => {
    if (request.entityId) await getRoleById(dispatch, request.entityId)
    dispatch(setSwitchToRoleDetails({ open: true, role: {}, type: 'approve' }))
    dispatch(setSelectedApprovalRequest(request))
    router.push('/roles/details')
  },
}
