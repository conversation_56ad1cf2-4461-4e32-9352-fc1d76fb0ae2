'use client'
import {
  <PERSON><PERSON>,
  Chip,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Popper,
  Select,
  SelectChangeEvent,
  Stack,
  TextField,
  Typography,
  ClickAwayListener,
  Paper,
  Box,
} from '@mui/material'
import React, { FC, useEffect, useRef, useState } from 'react'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import CloseIcon from '@mui/icons-material/Close'
import {
  FilterListOffRounded,
  FilterListRounded,
  KeyboardArrowDownRounded,
} from '@mui/icons-material'
import dayjs, { Dayjs } from 'dayjs'
import { IFilterOption } from '@dtbx/store/interfaces'

import { CustomSearchInput } from '@dtbx/ui/components/Input'
import RequestSearch from './RequestSearch'
import { DropdownMenuCheckBoxWithSearch } from '@dtbx/ui/components/DropDownMenus'
import {
  DateCalendar,
  LocalizationProvider,
  PickersDay,
} from '@mui/x-date-pickers'
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'
type FilterType = 'select' | 'dropdown/checkbox' | 'dropdown/single' | 'date'

interface IFilterOptions {
  key: string
  value: string
  label: string
}

interface IFilter {
  filterName: string
  options: IFilterOptions[]
  type: FilterType
}

interface CustomFilterBoxProps {
  openFilter: boolean
  setOpenFilter: (value: boolean) => void
  searchValue: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  filters: IFilter[]
  searchByValues?: string[]
  onFilterChange: (filters: Record<string, string | string[]>) => void
  setSearchByValue?: (value: string) => void
  setDate?: (value: { start: dayjs.Dayjs; end: dayjs.Dayjs } | null) => void
  setMakerName?: (value: string) => void
  searchPlaceHolder?: string
}

export const CustomFilterBox = (props: CustomFilterBoxProps) => {
  const {
    openFilter,
    setOpenFilter,
    searchValue,
    handleSearch,
    filters,
    searchByValues,
    onFilterChange,
    setSearchByValue,
    setDate,
    setMakerName,
    searchPlaceHolder,
  } = props
  const defaultModule = () => {
    let defaultModule = {}
    filters.forEach((filter) => {
      if (filter.filterName === 'Module') {
        defaultModule =
          filter.options.filter((option) => option.key === 'customers').length >
          0
            ? { Module: 'Customers' }
            : {}
      }
    })
    return defaultModule
  }
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string | string[]>
  >(defaultModule() ? defaultModule() : {})
  const [checkBoxValue, setCheckBoxValue] = useState<IFilterOption[]>([])
  const [openDropDownCheckbox, setOpenDropDownCheckbox] = useState<
    string | null
  >(null)
  const handleFilterChange = (
    filterName: string,
    selectedOption: string | string[]
  ) => {
    const newFilters = {
      ...selectedFilters,
      [filterName]: selectedOption,
    }
    setSelectedFilters(newFilters)
    onFilterChange(newFilters)
  }
  const handleFilterOpen = () => {
    setOpenFilter(!openFilter)
  }
  const handleClearAll = () => {
    setOpenFilter(false)
    setSelectedFilters({})
    onFilterChange({})
    setCheckBoxValue([])
    setOpenDropDownCheckbox(null)
    setDate ? setDate(null) : null
  }
  const handleDropdownCheckboxOpen = (filterName: string) => {
    setOpenDropDownCheckbox((prev) => (prev === filterName ? null : filterName))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          gap: '10px',
        }}
      >
        {setMakerName ? (
          <RequestSearch
            searchByItems={[
              {
                label: 'Maker First Name',
                value: 'firstName',
              },
              {
                label: 'Maker Last Name',
                value: 'lastName',
              },
            ]}
            onSetSearch={(makerName: string) => {
              setMakerName(makerName)
            }}
          />
        ) : searchByValues && setSearchByValue ? (
          <SearchByValuesBox
            searchValue={searchValue}
            handleSearch={handleSearch}
            searchByValues={searchByValues}
            setSearchByValue={setSearchByValue}
          />
        ) : (
          <CustomSearchInput
            value={searchValue}
            onChange={handleSearch}
            placeholder={searchPlaceHolder || 'Search'}
            startAdornment={
              <InputAdornment position="start">
                <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            }
          />
        )}

        <Button
          variant="outlined"
          sx={{
            height: '40px',
            gap: 0,
            boxShadow: openFilter
              ? '0px 1px 2px 0px rgba(16, 24, 40, 0.05)'
              : '0px 1px 2px 0px #1018280D',
            border: openFilter ? '1.5px solid #555C61' : '1.5px solid #D0D5DD',
          }}
          startIcon={
            !openFilter ? <FilterListOffRounded /> : <FilterListRounded />
          }
          onClick={handleFilterOpen}
        >
          <Typography
            sx={{
              textWrap: 'nowrap',
            }}
          >
            {openFilter ? 'Hide Filters' : 'Show Filters'}
          </Typography>
        </Button>
      </Stack>
      <Stack
        sx={{
          display: openFilter ? 'flex' : 'none',
          flexDirection: 'row',
          gap: '10px',
          mt: '10px',
        }}
      >
        <Button
          variant="text"
          sx={{
            width: '6vw',
            color: '#555C61',
            textWrap: 'nowrap',
            height: '40px',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
            borderRadius: '6px',
            gap: 0,
          }}
          onClick={handleClearAll}
          endIcon={<CloseIcon />}
        >
          <Typography variant="body2" color="text.primary">
            Clear All
          </Typography>
        </Button>
        {filters.map((filter) => {
          return filter.type === 'select' ? (
            <FormControl
              key={filter.filterName}
              sx={{
                width: '40%',
              }}
            >
              <InputLabel
                id="demo-simple-select-outlined-label"
                sx={{
                  background: '#fcfcfc',
                  padding: '0 4px',
                  marginLeft: '-4px',
                }}
              >
                {filter.filterName}
              </InputLabel>
              <Select
                labelId="demo-simple-select-outlined-label"
                id="demo-simple-select-outlined"
                key={filter.filterName}
                sx={{
                  border: '1px solid #AAADB0',
                  height: '40px',
                }}
                value={selectedFilters[filter.filterName] || ''}
                onChange={(e) =>
                  handleFilterChange(filter.filterName, e.target.value)
                }
              >
                {filter.options.map((option) => (
                  <MenuItem key={option.key} value={option.label}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          ) : filter.type === 'dropdown/checkbox' ? (
            <DropdownMenuCheckBoxWithSearch
              label={filter.filterName}
              key={filter.filterName}
              onClick={(selectedFilters: IFilterOption[]) => {
                if (selectedFilters.length >= 0) {
                  handleFilterChange(
                    filter.filterName,
                    selectedFilters.map((filt) => filt.key)
                  )
                  setCheckBoxValue(selectedFilters)
                }
              }}
              filters={filter.options}
              selectedFilter={checkBoxValue}
              open={openDropDownCheckbox === filter.filterName}
              handleOpenMenu={() =>
                handleDropdownCheckboxOpen(filter.filterName)
              }
              setOpen={(open) => {
                if (!open) handleDropdownCheckboxOpen('')
              }}
            />
          ) : filter.type === 'dropdown/single' ? (
            <DropdownMenu
              filter={filter}
              onSelected={(str: string) => {
                handleFilterChange(filter.filterName, str)
              }}
              selectedFilterValue={selectedFilters[filter.filterName] as string}
            />
          ) : filter.type === 'date' ? (
            <CustomDateRangePicker
              filter={filter}
              handleFilterChangeAction={handleFilterChange}
              selectedDates={selectedFilters[filter.filterName] as string[]}
            />
          ) : (
            <></>
          )
        })}
      </Stack>
    </Stack>
  )
}

interface IDropdownMenu {
  filter: IFilter
  onSelected: (str: string) => void
  selectedFilterValue?: string
}

const DropdownMenu: FC<IDropdownMenu> = ({
  filter,
  onSelected,
  selectedFilterValue,
}) => {
  const disabledValue = 'none'
  return (
    <FormControl
      sx={{
        minWidth: '25%',
      }}
      size="small"
    >
      <InputLabel
        id={filter.filterName}
        sx={{
          background: '#FFFFFF',
          px: '5px',
        }}
      >
        {filter.filterName}
      </InputLabel>
      <Select
        sx={{
          px: '5%',
          justifyContent: 'center',
        }}
        labelId={filter.filterName}
        MenuProps={{
          sx: {
            maxHeight: '50vh',
            borderRadius: '6px',
            '.MuiMenu-paper': { borderRadius: '8px' },
          },
        }}
        value={selectedFilterValue ?? disabledValue}
        onChange={(e) => onSelected(e.target.value)}
        IconComponent={() => <KeyboardArrowDownRounded />}
      >
        <MenuItem disabled value={disabledValue}>
          All
        </MenuItem>
        {filter.options.map((opt, index) => (
          <MenuItem key={index} value={opt.value}>
            {opt.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  )
}

interface SearchByValuesBoxProps {
  searchValue: string
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void
  searchByValues: string[]
  setSearchByValue: (value: string) => void
}

const SearchByValuesBox = ({
  searchValue,
  handleSearch,
  searchByValues,
  setSearchByValue,
}: SearchByValuesBoxProps) => {
  const [selectedSearchBy, setSelectedSearchBy] = useState<string>(
    searchByValues[0] ?? ''
  )
  const handleSelect = (e: SelectChangeEvent) => {
    setSelectedSearchBy(e.target.value)
    setSearchByValue(e.target.value)
  }
  return (
    <Stack direction="row">
      <Select
        fullWidth
        size="small"
        onChange={handleSelect}
        IconComponent={() => <KeyboardArrowDownRounded />}
        value={selectedSearchBy}
        sx={{
          width: '13vw',
          '.MuiInputBase-input.MuiOutlinedInput-input ': {
            py: '2px !important',
          },
          borderTopRightRadius: 0,
          borderBottomRightRadius: 0,
          borderRight: 'none',
        }}
      >
        {searchByValues.map((value) => (
          <MenuItem key={value} value={value}>
            <Chip label={`Search By ${value}`} />
          </MenuItem>
        ))}
      </Select>
      <CustomSearchInput
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
        width={'300px'}
        sx={{
          '& fieldset': {
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
            borderLeft: 'none !important',
          },
        }}
        endAdornment={
          <InputAdornment position="start">
            <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        }
      />
    </Stack>
  )
}

export const CustomDateRangePicker = ({
  filter,
  handleFilterChangeAction,
  selectedDates,
}: {
  filter: IFilter
  handleFilterChangeAction: (
    filterName: string,
    selectedOption: string[]
  ) => void
  selectedDates: string[]
}) => {
  const [dateRange, setDateRange] = useState<[Dayjs | null, Dayjs | null]>([
    selectedDates ? dayjs(selectedDates[0], 'DD/MM/YYYY HH:mm') : null,
    selectedDates ? dayjs(selectedDates[1], 'DD/MM/YYYY HH:mm') : null,
  ])
  const [tempDateRange, setTempDateRange] = useState<
    [Dayjs | null, Dayjs | null]
  >([null, null])
  const [isOpen, setIsOpen] = useState(false)
  const anchorRef = useRef<HTMLDivElement>(null)

  const handleDateChange = (date: Dayjs | null) => {
    if (!tempDateRange[0] || (tempDateRange[0] && tempDateRange[1])) {
      setTempDateRange([date, null])
    } else {
      setTempDateRange([tempDateRange[0], date])
    }
  }

  const handleApply = () => {
    if (tempDateRange[0] && tempDateRange[1]) {
      setDateRange(
        tempDateRange[0].isBefore(tempDateRange[1])
          ? tempDateRange
          : [tempDateRange[1], tempDateRange[0]]
      )
      const dateRange = tempDateRange[0].isBefore(tempDateRange[1])
        ? [
            tempDateRange[0].format('YYYY-MM-DD'),
            tempDateRange[1].format('YYYY-MM-DD'),
          ]
        : [
            tempDateRange[1].format('YYYY-MM-DD'),
            tempDateRange[0].format('YYYY-MM-DD'),
          ]
      handleFilterChangeAction(filter.filterName, dateRange)
    }
    setIsOpen(false)
  }

  const handleCancel = () => {
    setTempDateRange(dateRange)
    setIsOpen(false)
  }

  const formatDateRange = (range: [Dayjs | null, Dayjs | null]) => {
    return `${range[0] ? range[0].format('MM/DD/YYYY') : 'From'} - ${range[1] ? range[1].format('MM/DD/YYYY') : 'To'}`
  }

  const isDateInRange = (date: Dayjs) => {
    if (tempDateRange[0] && tempDateRange[1]) {
      return (
        date.isSame(tempDateRange[0]) ||
        date.isSame(tempDateRange[1]) ||
        (date.isAfter(tempDateRange[0]) && date.isBefore(tempDateRange[1]))
      )
    }
    return false
  }
  const isInBetweenRange = (date: Dayjs) => {
    return date.isAfter(tempDateRange[0]) && date.isBefore(tempDateRange[1])
  }
  useEffect(() => {
    !selectedDates && setDateRange([null, null])
  }, [selectedDates])
  return (
    <Stack>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <div ref={anchorRef}>
          <TextField
            label={filter.filterName}
            size="small"
            value={`${filter.filterName} : ${formatDateRange(dateRange)}`}
            sx={{ minWidth: '17vw' }}
            onClick={() => setIsOpen(true)}
            slotProps={{
              input: {
                readOnly: true,
              },
            }}
          />
        </div>
        <Popper
          open={isOpen}
          anchorEl={anchorRef.current}
          placement="bottom-start"
        >
          <ClickAwayListener onClickAway={handleCancel}>
            <Paper elevation={8} sx={{ p: 2 }}>
              <Stack direction="row">
                <Typography>
                  {formatDateRange(tempDateRange) || 'N/A'}
                </Typography>
              </Stack>
              <DateCalendar
                value={tempDateRange[1] || tempDateRange[0]}
                showDaysOutsideCurrentMonth
                onChange={handleDateChange}
                slots={{
                  day: (props) => {
                    const { day } = props
                    const isSelected = isDateInRange(day)
                    return (
                      <PickersDay
                        disableMargin
                        sx={{
                          px:
                            tempDateRange[0] === day || tempDateRange[1] === day
                              ? 'inherit'
                              : 2.5,
                          color:
                            tempDateRange[0] === day
                              ? '#FFFFFF'
                              : tempDateRange[1] === day
                                ? '#FFFFFF'
                                : isSelected
                                  ? 'primary.main'
                                  : 'text.main',
                          backgroundColor:
                            tempDateRange[0] === day
                              ? 'text.primary'
                              : tempDateRange[1] === day
                                ? 'text.primary'
                                : isSelected
                                  ? '#F5F5F5'
                                  : '#FFFFFF',
                          borderTopLeftRadius:
                            isInBetweenRange(day) || tempDateRange[1] === day
                              ? 'inherit'
                              : '50%',
                          borderBottomLeftRadius:
                            isInBetweenRange(day) || tempDateRange[1] === day
                              ? 'inherit'
                              : '50%',
                          borderTopRightRadius:
                            isInBetweenRange(day) || tempDateRange[0] === day
                              ? 'inherit'
                              : '50%',
                          borderBottomRightRadius:
                            isInBetweenRange(day) || tempDateRange[0] === day
                              ? 'inherit'
                              : '50%',
                        }}
                        day={day}
                        onDaySelect={function (day: dayjs.Dayjs): void {
                          handleDateChange(day)
                        }}
                        outsideCurrentMonth={false}
                        isFirstVisibleCell={false}
                        isLastVisibleCell={false}
                      />
                    )
                  },
                }}
              />
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                <Button onClick={handleCancel} variant="outlined">
                  Cancel
                </Button>
                <Button
                  onClick={handleApply}
                  variant="contained"
                  disabled={!tempDateRange[0] || !tempDateRange[1]}
                >
                  Apply
                </Button>
              </Box>
            </Paper>
          </ClickAwayListener>
        </Popper>
      </LocalizationProvider>
    </Stack>
  )
}
