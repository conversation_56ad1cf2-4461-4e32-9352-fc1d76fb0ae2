import { Stack } from '@mui/material'
import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { setUserApprovalRequestsFilters } from '@/store/reducers'
import { getApprovals } from '@/store/actions'
import { IFilter } from '@dtbx/store/interfaces'
import { CustomFilterBox } from '@/app/approval-requests/CustomFilterBox'

const PageHeader = () => {
  const dispatch = useAppDispatch()
  const [openFilter, setOpenFilter] = useState<boolean>(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [, setSearchByValue] = useState<string>('email')
  const { requestTypes, userApprovalRequestFilters } = useAppSelector(
    (state) => state.approvalRequests
  )
  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value
    setSearchValue(search)
  }

  const { search } = useAppSelector((state) => state.customers)

  const makerNameFilter = () => {
    return search?.searchBy[0] === 'firstName'
      ? 'makerFirstName'
      : search?.searchBy[0] === 'lastName'
        ? 'makerLastName'
        : ''
  }

  const handleFilterChange = async (
    filters: Record<string, string | string[]>
  ) => {
    Object.keys(filters).length > 0
      ? dispatch(setUserApprovalRequestsFilters(filters))
      : dispatch(setUserApprovalRequestsFilters({ module: 'users' }))
    const filterParams = Object.keys(filters)
      .map((key) => {
        return `${key}=${filters[key]}`
      })
      .join('&')
    const nameParam = searchValue ? `&${makerNameFilter()}=${searchValue}` : ''
    const params = `?channel=DBP&${filterParams || 'module=users'}&page=1&size=10${nameParam}`
    await getApprovals(dispatch, params)
  }
  const filters: IFilter[] = [
    {
      filterName: 'module',
      options: [
        { key: 'users', value: 'users', label: 'Users' },
        { key: 'groups', value: 'groups', label: 'Roles' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'status',
      options: [
        { key: 'PENDING', value: 'PENDING', label: 'Pending' },
        { key: 'APPROVED', value: 'APPROVED', label: 'Approved' },
        { key: 'REJECTED', value: 'REJECTED', label: 'Rejected' },
      ],
      type: 'dropdown/single',
    },
    {
      filterName: 'requestType',
      options: [
        ...requestTypes
          .filter(
            (req) =>
              req.name.toLowerCase().includes('users') ||
              req.name.toLowerCase().includes('groups')
          )
          .map((req) => {
            return { key: req.id, value: req.id, label: req.name }
          }),
      ],
      type: 'dropdown/single',
    },
  ]
  const searchByMaker = async (makerName: string) => {
    setSearchValue(makerName)
    const filterParams = Object.keys(userApprovalRequestFilters)
      .map((key) => {
        return `${key}=${userApprovalRequestFilters[key]}`
      })
      .join('&')
    const nameParam = makerName ? `&${makerNameFilter()}=${makerName}` : ''
    const params = `?channel=DBP&${filterParams || 'module=users'}&page=1&size=10${nameParam}`
    await getApprovals(dispatch, params)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        justifyContent: 'space-between',
        gap: '16px',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
          gap: '18px',
          flex: 1,
        }}
      >
        <CustomFilterBox
          openFilter={openFilter}
          setMakerName={searchByMaker}
          setOpenFilter={setOpenFilter}
          searchValue={searchValue}
          searchByValues={['Maker', 'Checker', 'Request Type', 'module']}
          handleSearch={handleSearch}
          filters={filters}
          onFilterChange={handleFilterChange}
          setSearchByValue={setSearchByValue}
        />
      </Stack>
    </Stack>
  )
}

export default PageHeader
