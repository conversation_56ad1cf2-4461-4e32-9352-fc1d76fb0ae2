'use client'
// @ts-nocheck
import {
  Avatar,
  Box,
  Breadcrumbs,
  Button,
  Divider,
  Stack,
  TextField,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import ArrowBackIcon from '@mui/icons-material/ArrowBack'
import { sentenceCase } from 'tiny-case'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { IDiffValues } from '@/store/interfaces'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { CheckIcon, LoggedInIcon, UserProfileIcon } from '@dtbx/ui/icons'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { CustomChip, CustomerStatusChip } from '@dtbx/ui/components/Chip'

import { CheckerRequestsApiHandler } from '@/app/approval-requests/CheckerRequestsApiHandler'

import DeactivateUser from './DeactivateUser'
import { ApprovalRequestDetails } from './ApprovalRequestDetails'
import { ViewRoles } from '@/app/rights/RightsDialogs'

export const UserDetails = () => {
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { singleUserData, isLoadingEditUser } = useAppSelector(
    (state) => state.users
  )
  const [name, setName] = useState<string>('')
  const [lastLoggedInData, setloggedInDate] = useState<string | number | null>(
    ''
  )

  const getNames = () => {
    let firstName = ''
    let lastName = ''

    Object.values(selectedApprovalRequest.diff).forEach(
      (value: IDiffValues) => {
        if (typeof value.newValue === 'string') {
          if (value.field === 'firstName') {
            firstName = value.newValue
          }
          if (value.field === 'lastName') {
            lastName = value.newValue
          }
        }
      }
    )

    return `${firstName} ${lastName}`.trim()
  }
  //handle button clicks
  const handleApprove = async () => {
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `ACCEPT_${selectedApprovalRequest.makerCheckerType.type}`,
      'Request Approved successfully'
    )
    return router.push('/users')
  }
  const handleReject = async () => {
    await CheckerRequestsApiHandler(
      selectedApprovalRequest,
      dispatch,
      router,
      `REJECT_${selectedApprovalRequest.makerCheckerType.type}`,
      'Request rejected successfully'
    )
    return router.push('/users')
  }

  useEffect(() => {
    const newName =
      selectedApprovalRequest.makerCheckerType.type !== 'CREATE_USERS'
        ? `${singleUserData.firstName} ${singleUserData.lastName}`
        : getNames()
    setName(newName)
  }, [selectedApprovalRequest, singleUserData])
  return (
    <Stack
      sx={{
        flexDirection: 'column',
      }}
    >
      <Stack
        sx={{
          justifyContent: 'space-between',
          flexDirection: 'row',
          px: '2%',
          paddingBottom: '1%',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            alignContent: 'center',
            gap: '10px',
          }}
        >
          <Breadcrumbs>
            <Typography
              variant="body2"
              sx={{ color: 'primary.main', cursor: 'pointer' }}
              onClick={() => router.push('/users')}
            >
              Users
            </Typography>
            <Typography variant="body2" sx={{ color: 'primary.main' }}>
              {name}
            </Typography>
          </Breadcrumbs>
          <CustomerStatusChip
            label={
              selectedApprovalRequest.status === 'APPROVED' &&
              singleUserData.status === 'ACTIVE'
                ? 'ACTIVE'
                : singleUserData.status
                  ? singleUserData.status
                  : 'ACTIVE'
            }
          />
        </Stack>
        {selectedApprovalRequest.status === 'PENDING' && (
          <Stack
            sx={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              gap: '10px',
            }}
          >
            <ApprovalRequestDetails />
            {isLoadingEditUser ? (
              <LoadingButton width={'30%'} />
            ) : (
              <AccessControlWrapper
                rights={[
                  'REJECT_DEACTIVATE_USERS',
                  'REJECT_CREATE_USERS',
                  'REJECT_DELETE_USERS',
                  'REJECT_ACTIVATE_USERS',
                ]}
                makerId={selectedApprovalRequest?.maker}
              >
                <Button
                  variant="outlined"
                  sx={{
                    background: '#E3E4E4',
                    border: '1px solid #AAADB0',
                    display: 'none',
                  }}
                  onClick={handleReject}
                >
                  Reject
                </Button>
              </AccessControlWrapper>
            )}
            {isLoadingEditUser ? (
              <LoadingButton width={'30%'} />
            ) : (
              <AccessControlWrapper
                rights={[
                  'ACCEPT_ACTIVATE_USERS',
                  'ACCEPT_DEACTIVATE_USERS',
                  'ACCEPT_UPDATE_USERS',
                  'ACCEPT_DELETE_USERS',
                  'ACCEPT_CREATE_USERS',
                ]}
                makerId={selectedApprovalRequest?.maker}
              >
                <Button
                  variant="contained"
                  endIcon={<CheckIcon />}
                  onClick={handleApprove}
                  sx={{
                    display: 'none',
                  }}
                >
                  Approve
                </Button>
              </AccessControlWrapper>
            )}
          </Stack>
        )}
        {(selectedApprovalRequest.status !== 'PENDING' ||
          selectedApprovalRequest.status.length < 0) &&
          ['ACTIVE', 'INACTIVE'].includes(singleUserData.status) && (
            <Stack
              sx={{
                flexDirection: 'row',
                gap: '20px',
                justifyContent: 'flex-end',
                alignItems: 'center',
              }}
            >
              <DeactivateUser user={singleUserData} />
              <Button
                sx={{
                  border: '1px solid #D0D5DD',
                  borderRadius: '6px',
                  padding: '9px 28px',
                }}
                variant="contained"
              >
                <Typography
                  sx={{
                    color: '#FFFFFF',
                    fontSize: '13px',
                    lineHeight: '16px',
                    fontWeight: 500,
                  }}
                >
                  Changes log
                </Typography>
              </Button>
            </Stack>
          )}
      </Stack>
      <Divider />
      <Stack
        sx={{
          px: '2%',
          py: '2%',
          border: '1px solid #D0D5DD',
          background: '#FFFFFF',
          flexDirection: 'column',
          marginTop: '5vh',
          mx: '2%',
          gap: '8vh',
        }}
      >
        <Button
          variant="outlined"
          sx={{
            width: '12%',
            px: 0,
            border: '1px solid #AAADB0',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          startIcon={<ArrowBackIcon />}
          onClick={() => router.push('/users')}
        >
          All Users
        </Button>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '20px',
          }}
        >
          <Avatar
            sx={{
              backgroundColor: '#E7E8E9',
              padding: '10px',
              width: '50px',
              height: '50px',
            }}
          >
            <UserProfileIcon />
          </Avatar>
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 500,
              color: '#2A3339',
            }}
          >
            {name}

            {selectedApprovalRequest.makerCheckerType.type ===
            'CREATE_USERS' ? (
              <Typography
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  alignContent: 'center',
                  gap: '5px',
                }}
                variant="body2"
              >
                {' '}
                <LoggedInIcon /> Not yet logged in
              </Typography>
            ) : (
              <Typography>
                Last logged in{' '}
                {(singleUserData?.lastLoginDate as string) || 'N/A'}
              </Typography>
            )}
          </Typography>
        </Stack>
        <Stack
          sx={{
            flexDirection: 'row',
            width: '100%',
            flexWrap: 'wrap',
            gap: '5%',
          }}
        >
          {selectedApprovalRequest.makerCheckerType.type === 'CREATE_USERS'
            ? selectedApprovalRequest.diff.map((value: IDiffValues) => (
                <TextField
                  key={value.field}
                  label={sentenceCase(value.field)}
                  value={
                    typeof value.newValue === 'string'
                      ? value.newValue
                      : Array.isArray(value.newValue)
                        ? value.newValue.find(
                            (val: IDiffValues) => val.field === 'name'
                          )?.newValue
                        : ''
                  }
                  sx={{
                    width: '30%',
                    marginBottom: '2%',
                  }}
                />
              ))
            : singleUserData &&
              Object.keys(singleUserData).map((key) => {
                if (key === 'id') {
                  return null
                } else {
                  if (key === 'roles' && singleUserData[key].length > 0) {
                    //TODO: Fix this,  add a limit to number of roles shown by component and add a show more button
                    return (
                      <Box
                        key={key}
                        sx={{
                          position: 'relative',
                          width: '30%',
                          marginBottom: '2%',
                        }}
                      >
                        <Typography
                          variant="caption"
                          sx={{
                            position: 'absolute',
                            top: '-10px',
                            left: '10px',
                            backgroundColor: 'background.paper',
                            padding: '0 4px',
                            color: 'text.secondary',
                          }}
                        >
                          {sentenceCase(key)}
                        </Typography>

                        <Box
                          sx={{
                            display: 'flex',
                            flexWrap: 'wrap',
                            gap: 0.5,
                            padding: '8px 12px',
                            border: '1px solid',
                            borderColor: 'grey.400',
                            borderRadius: '4px',
                            backgroundColor: 'background.paper',
                            minHeight: '56px',
                            alignItems: 'center',
                          }}
                        >
                          {singleUserData[key]
                            .slice(0, 2)
                            .map((item, index) =>
                              item !== null ? (
                                <CustomChip
                                  key={index}
                                  label={item?.name}
                                  size="small"
                                />
                              ) : null
                            )}
                          {singleUserData[key].length > 2 && (
                            <ViewRoles
                              roles={[...singleUserData[key]]}
                              permission={singleUserData['firstName']}
                            />
                          )}
                        </Box>
                      </Box>
                    )
                  }

                  if (key === 'country') {
                    const country = singleUserData[key] as
                      | { name: string }
                      | string
                      | undefined

                    if (
                      typeof country === 'object' &&
                      country !== null &&
                      'name' in country
                    ) {
                      return (
                        <TextField
                          key={key}
                          label={sentenceCase(key)}
                          value={country.name || ''}
                        />
                      )
                    } else {
                      return (
                        <TextField
                          key={key}
                          label={sentenceCase(key)}
                          value={country || ''}
                        />
                      )
                    }
                  }
                  return (
                    <TextField
                      key={key}
                      label={sentenceCase(key)}
                      value={
                        key === 'status'
                          ? sentenceCase(singleUserData[key])
                          : key === 'roles' && singleUserData[key].length > 0
                            ? singleUserData[key][0].name
                            : singleUserData[key]
                      }
                      sx={{
                        width: '30%',
                        marginBottom: '2%',
                      }}
                    />
                  )
                }
              })}
        </Stack>
      </Stack>
    </Stack>
  )
}
