'use client'

import { configureStore } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'
import { rootReducer } from '@dtbx/store/reducers'
import storage from 'redux-persist/lib/storage'

export const persistConfig = {
  key: 'backoffice',
  storage,
  blacklist: ['notification', 'overlays', 'chargeConfiguration'],
}
export const persistedReducer = persistReducer(persistConfig, rootReducer)

const index = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
})

export type RootState = ReturnType<typeof index.getState>
export type AppDispatch = typeof index.dispatch
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export default index
