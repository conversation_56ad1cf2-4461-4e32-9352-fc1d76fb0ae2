'use client'
import { ReactNode } from 'react'
import { NextAppDirEmotionCacheProvider, ThemeConfig } from '@dtbx/ui/theme'
import { CustomScrollbar, InActivity } from '@dtbx/ui/components'
import { isLoggedIn } from '@dtbx/store/utils'
import '@dtbx/ui/theme/index.css'
import AppProvider from '@/store/AppProvider'

export default function RootLayout({
  children,
}: Readonly<{
  children: ReactNode
}>) {
  return (
    <html lang="en">
      <body>
        <AppProvider>
          <NextAppDirEmotionCacheProvider options={{ key: 'mui' }}>
            <ThemeConfig themeType="main">
              <CustomScrollbar>
                <InActivity isLoggedIn={isLoggedIn}>{children}</InActivity>
              </CustomScrollbar>
            </ThemeConfig>
          </NextAppDirEmotionCacheProvider>
        </AppProvider>
      </body>
    </html>
  )
}
