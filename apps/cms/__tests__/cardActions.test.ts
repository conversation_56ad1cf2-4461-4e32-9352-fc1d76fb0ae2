import { expect, it, describe, vi, beforeEach, afterEach } from 'vitest'

import * as apiFunctions from '@/store/actions'
import { secureapi } from '@dtbx/store/utils'
import {
  setBranchCardsList,
  setBranchCardsListPagination,
  setCardResponse,
  setCreditCardsList,
  setIsLoadingActivateCard,
  setIsLoadingBranchCards,
  setIsLoadingCards,
  setIsLoadingSetPinCard,
  setIsLoadingSingleCard,
  setSelectedCardToView,
  setIsLoadingResetPinRetries,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { ICreditCard } from '@/store/interfaces'
import {
  createMockCreditCard,
  createMockApiResponse,
  createMockApiError,
  createMockCardResponse,
} from './mocks/data'

vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
  },
}))

describe('Card Actions', () => {
  let mockDispatch: vi.MockedFunction<any>

  beforeEach(() => {
    mockDispatch = vi.fn()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('activateCards', () => {
    it('should dispatch setIsLoadingActivateCard(true) at the beginning', async () => {
      const mockDispatch = vi.fn()

      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123456',
        cardIdentifierType: 'PAN',
        countryCode: 'US',
      }

      vi.spyOn(secureapi, 'post').mockResolvedValue({})

      await apiFunctions.activateCards(mockDispatch, data, 'normal')

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingActivateCard(true)
      )
    }),
      it('should make a POST request to the correct endpoint when requestType is "super"', async () => {
        const dispatch = vi.fn()
        const data = {
          comments: 'Test comment',
          cardIdentifierId: '123',
          cardIdentifierType: 'typeA',
          countryCode: 'US',
        }
        const requestType = 'super'
        const postMock = vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

        await apiFunctions.activateCards(dispatch, data, requestType)

        expect(postMock).toHaveBeenCalledWith(
          'backoffice-bff/cards/activate',
          data
        )
        postMock.mockRestore()
      })
    it('should dispatch success notification with correct message when requestType is "make"', async () => {
      const dispatch = vi.fn()
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const requestType = 'make'
      vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.activateCards(dispatch, data, requestType)

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card activation request sent successfully',
          type: 'success',
        })
      )
    })
    it('should handle API errors by dispatching error notification', async () => {
      const mockDispatch = vi.fn()

      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123456',
        cardIdentifierType: 'PAN',
        countryCode: 'US',
      }

      const errorMessage = 'API Error'
      const error = new Error(errorMessage)

      vi.spyOn(secureapi, 'post').mockRejectedValue(error)
      vi.spyOn(console, 'error').mockImplementation(() => {})

      await apiFunctions.activateCards(mockDispatch, data, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(console.error).toHaveBeenCalledWith(
        'card activation request error:',
        error
      )
    })
  })
  describe('completeCardActivation', () => {
    it('should call secureapi.put with correct URL and data when approvalType is approve', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Approved by test' }

      vi.spyOn(secureapi, 'put').mockResolvedValue({})

      await apiFunctions.completeCardActivation(
        mockDispatch,
        approvalId,
        approvalType,
        data
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        `backoffice-bff/cards/activate/approve/12345`,
        data
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingActivateCard(true)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setIsLoadingActivateCard(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message:
            'Activate credit card request has been approved. Customer will be notified via an sms that will include their PIN.',
          type: 'success',
        })
      )
    })
    it('should call secureapi.put with correct URL and data when approvalType is "reject"', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'reject'
      const data = { comments: 'Test comment' }
      const putMock = vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardActivation(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(putMock).toHaveBeenCalledWith(
        `backoffice-bff/cards/activate/reject/${approvalId}`,
        data
      )
    })
    it('should dispatch setIsLoadingActivateCard(true) at the beginning', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Test comment' }
      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardActivation(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(true))
    })

    it('should dispatch setIsLoadingActivateCard(false) after successful API call', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Test comment' }
      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardActivation(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingActivateCard(false))
    })

    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'reject'
      const data = { comments: 'Rejected by test' }
      const errorMessage = 'API connection error'

      vi.spyOn(secureapi, 'put').mockRejectedValue(new Error(errorMessage))
      vi.spyOn(console, 'error').mockImplementation(() => {})

      await apiFunctions.completeCardActivation(
        mockDispatch,
        approvalId,
        approvalType,
        data
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingActivateCard(true)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setIsLoadingActivateCard(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(console.error).toHaveBeenCalledWith(
        'card activation approval completion request error:',
        expect.any(Error)
      )
    })
  })

  describe('getAllCards', () => {
    it('should fetch cards data and update store with response', async () => {
      const dispatch = vi.fn()
      const params = { size: 10, page: 1, cardType: 'credit' }
      const mockResponse = {
        data: {
          data: {
            data: [
              {
                cardId: '123',
                customerName: 'John Doe',
                active: true,
                pan: '****************',
                phoneNumber: '************',
                cif: '**********',
                domicileBranch: '2345',
                cardName: 'Jane Doe',
                account: '***********',
                cardType: 'Credit',
                productName: 'Credit Card',
                isPrimary: false,
                isSupplementary: false,
              },
            ],
            pageNumber: 1,
            pageSize: 10,
            totalElements: 20,
            totalNumberOfPages: 2,
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCards(dispatch, params)

      expect(secureapi.get).toHaveBeenCalledWith('backoffice-bff/cards', {
        params,
      })
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCards(true))
      expect(dispatch).toHaveBeenCalledWith(
        setCreditCardsList(mockResponse.data.data.data)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setCardResponse(mockResponse.data.data)
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCards(false))
    })
    it('should set loading state to true before API call and false after successful response', async () => {
      const dispatch = vi.fn()
      const params = { size: 10, page: 1, cardType: 'debit' }
      const mockResponse = {
        data: {
          data: {
            data: [],
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCards(dispatch, params)

      const dispatchCalls = dispatch.mock.calls
      expect(dispatchCalls[0][0]).toEqual(setIsLoadingCards(true))
      expect(dispatchCalls[dispatchCalls.length - 1][0]).toEqual(
        setIsLoadingCards(false)
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCards(false))
    })
    it('should dispatch setCreditCardsList with cards data from response', async () => {
      const dispatch = vi.fn()
      const params = {
        size: 20,
        page: 2,
        cardType: 'credit',
        customerName: 'John',
      }
      const cardsData: ICreditCard[] = [
        {
          cardId: '123',
          customerName: 'John Doe',
          active: true,
          pan: '****************',
          phoneNumber: '************',
          cif: '**********',
          domicileBranch: '2345',
          cardName: 'Jane Doe',
          account: '***********',
          cardType: 'Credit',
          productName: 'Credit Card',
          isPrimary: false,
          isSupplementary: false,
        },
      ]
      const mockResponse = {
        data: {
          data: {
            data: cardsData,
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCards(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setCreditCardsList(cardsData))
    })
  })
  describe('getCardsById', () => {
    it('should fetch card data and dispatch it to the store', async () => {
      const dispatch = vi.fn()
      const cardId = '123'
      const mockData = {
        cardId: '123',
        customerName: 'John Doe',
        active: true,
        pan: '****************',
        phoneNumber: '************',
        cif: '**********',
        domicileBranch: '2345',
        cardName: 'Jane Doe',
        account: '***********',
        cardType: 'Credit',
        productName: 'Credit Card',
        isPrimary: false,
        isSupplementary: false,
      }
      const mockResponse = { data: { data: mockData } }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getCardById(dispatch, cardId)

      expect(secureapi.get).toHaveBeenCalledWith('backoffice-bff/cards/123')
      expect(dispatch).toHaveBeenCalledWith(setSelectedCardToView(mockData))
    })
    it('should set loading state to true at start and false after successful fetch', async () => {
      const dispatch = vi.fn()
      const cardId = '123'
      const mockResponse = { data: { data: {} } }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getCardById(dispatch, cardId)

      expect(dispatch).toHaveBeenNthCalledWith(1, setIsLoadingSingleCard(true))
      expect(dispatch).toHaveBeenLastCalledWith(setIsLoadingSingleCard(false))
    })
    it('should pass the cardId parameter correctly to the API endpoint', async () => {
      const dispatch = vi.fn()
      const cardId = 'special-card-789'
      const mockResponse = { data: { data: {} } }
      const getSpy = vi
        .spyOn(secureapi, 'get')
        .mockResolvedValueOnce(mockResponse)

      await apiFunctions.getCardById(dispatch, cardId)

      expect(getSpy).toHaveBeenCalledTimes(1)
      expect(getSpy).toHaveBeenCalledWith(`backoffice-bff/cards/${cardId}`)
      expect(getSpy).toHaveBeenCalledWith(
        'backoffice-bff/cards/special-card-789'
      )
    })
    it('should handle API error when invalid cardId is provided', async () => {
      const dispatch = vi.fn()
      const invalidCardId = 'invalid-id'
      const errorMessage = 'Card not found'
      vi.spyOn(secureapi, 'get').mockRejectedValueOnce(new Error(errorMessage))

      await apiFunctions.getCardById(dispatch, invalidCardId)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingSingleCard(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
      expect(dispatch).not.toHaveBeenCalledWith(
        setSelectedCardToView(expect.anything())
      )
    })
  })

  describe('getBranchCardsByPhonePAN', () => {
    it('should fetch branch cards data and update the store when valid params are provided', async () => {
      const dispatch = vi.fn()
      const params = 'phone=**********&pan=1234'
      const mockResponse = {
        data: {
          data: {
            data: [
              {
                cardId: '123',
                customerName: 'John Doe',
                active: true,
                pan: '****************',
                phoneNumber: '************',
                cif: '**********',
                domicileBranch: '2345',
                cardName: 'Jane Doe',
                account: '***********',
                cardType: 'Credit',
                productName: 'Credit Card',
                isPrimary: false,
                isSupplementary: false,
              },
            ],
            pageNumber: 1,
            pageSize: 10,
            totalElements: 20,
            totalNumberOfPages: 2,
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValue(mockResponse)

      await apiFunctions.getBranchCardsByPhonePan(dispatch, params)

      expect(secureapi.get).toHaveBeenCalledWith(
        `backoffice-bff/cards/branch-query?${params}`
      )
      expect(dispatch).toHaveBeenCalledWith(
        setBranchCardsList(mockResponse.data.data.data)
      )
    })
    it('should set loading state to true before API call and false after successful response', async () => {
      const dispatch = vi.fn()
      const params = 'phone=**********'
      const mockResponse = {
        data: {
          data: {
            data: [],
            pageNumber: 1,
            pageSize: 10,
            totalElements: 0,
            totalNumberOfPages: 0,
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValue(mockResponse)

      await apiFunctions.getBranchCardsByPhonePan(dispatch, params)

      expect(dispatch).toHaveBeenNthCalledWith(1, setIsLoadingBranchCards(true))
      expect(dispatch).toHaveBeenLastCalledWith(setIsLoadingBranchCards(false))
    })
    it('should correctly extract and dispatch pagination data from the response', async () => {
      const dispatch = vi.fn()
      const params = 'pan=1234'
      const paginationData = {
        pageNumber: 2,
        pageSize: 15,
        totalElements: 30,
        totalNumberOfPages: 3,
      }
      const mockResponse = {
        data: {
          data: {
            data: [],
            ...paginationData,
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValue(mockResponse)

      await apiFunctions.getBranchCardsByPhonePan(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(
        setBranchCardsListPagination(paginationData)
      )
    })
    it('should dispatch error notification with the error message when API call fails', async () => {
      const dispatch = vi.fn()
      const params = 'phone=**********'
      const errorMessage = 'API request failed'
      const error = new Error(errorMessage)
      vi.spyOn(secureapi, 'get').mockRejectedValue(error)

      await apiFunctions.getBranchCardsByPhonePan(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
    })
    it('should handle empty response data gracefully', async () => {
      const dispatch = vi.fn()
      const params = 'phone=**********'
      const mockResponse = {
        data: {
          data: {
            data: [],
            pageNumber: 1,
            pageSize: 10,
            totalElements: 0,
            totalNumberOfPages: 0,
          },
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValue(mockResponse)

      await apiFunctions.getBranchCardsByPhonePan(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setBranchCardsList([]))
      expect(dispatch).toHaveBeenCalledWith(
        setBranchCardsListPagination({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalNumberOfPages: 0,
        })
      )
    })
  })

  describe('resetCardPin', () => {
    it('should dispatch setIsLoadingSetPinCard(true) at the beginning', async () => {
      const mockDispatch = vi.fn()

      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123456',
        cardIdentifierType: 'PAN',
        countryCode: 'US',
      }

      vi.spyOn(secureapi, 'post').mockResolvedValue({})

      await apiFunctions.resetCardPin(mockDispatch, data, 'normal')

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingSetPinCard(true)
      )
    })

    it('should make a POST request to the correct endpoint when requestType is "super"', async () => {
      const dispatch = vi.fn()
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const requestType = 'super'
      const postMock = vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPin(dispatch, data, requestType)

      expect(postMock).toHaveBeenCalledWith(
        'backoffice-bff/cards/set-pin',
        data
      )
      postMock.mockRestore()
    })

    it('should make a POST request to the correct endpoint when requestType is "make"', async () => {
      const dispatch = vi.fn()
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const requestType = 'make'
      const postMock = vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPin(dispatch, data, requestType)

      expect(postMock).toHaveBeenCalledWith(
        'backoffice-bff/cards/set-pin/make',
        data
      )
      postMock.mockRestore()
    })

    it('should dispatch success notification with correct message when requestType is "super"', async () => {
      const dispatch = vi.fn()
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const requestType = 'super'
      vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPin(dispatch, data, requestType)

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card Pin reset successfully',
          type: 'success',
        })
      )
    })

    it('should dispatch success notification with correct message when requestType is "make"', async () => {
      const dispatch = vi.fn()
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const requestType = 'make'
      vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPin(dispatch, data, requestType)

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Card Pin  request sent successfully',
          type: 'success',
        })
      )
    })

    it('should handle API errors by dispatching error notification', async () => {
      const mockDispatch = vi.fn()

      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123456',
        cardIdentifierType: 'PAN',
        countryCode: 'US',
      }

      const errorMessage = 'API Error'
      const error = new Error(errorMessage)

      vi.spyOn(secureapi, 'post').mockRejectedValue(error)
      vi.spyOn(console, 'error').mockImplementation(() => {})

      await apiFunctions.resetCardPin(mockDispatch, data, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(console.error).toHaveBeenCalledWith(
        'card reset pin request error:',
        error
      )
    })
  })

  describe('completeCardSetPin', () => {
    it('should call secureapi.put with correct URL and data when approvalType is approve', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Approved by test' }

      vi.spyOn(secureapi, 'put').mockResolvedValue({})

      await apiFunctions.completeCardSetPin(
        mockDispatch,
        approvalId,
        approvalType,
        data
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        `backoffice-bff/cards/set-pin/approve/12345`,
        data
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingSetPinCard(true)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setIsLoadingSetPinCard(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message:
            'Set pin credit card request has been approved. Customer will be notified via an sms that will include their PIN.',
          type: 'success',
        })
      )
    })

    it('should call secureapi.put with correct URL and data when approvalType is "reject"', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'reject'
      const data = { comments: 'Test comment' }
      const putMock = vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardSetPin(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(putMock).toHaveBeenCalledWith(
        `backoffice-bff/cards/set-pin/reject/${approvalId}`,
        data
      )
    })

    it('should dispatch setIsLoadingSetPinCard(true) at the beginning', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Test comment' }
      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardSetPin(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(true))
    })

    it('should dispatch setIsLoadingSetPinCard(false) after successful API call', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Test comment' }
      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardSetPin(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingSetPinCard(false))
    })

    it('should dispatch success notification with correct message for approval', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'approve'
      const data = { comments: 'Test comment' }
      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardSetPin(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message:
            'Set pin credit card request has been approved. Customer will be notified via an sms that will include their PIN.',
          type: 'success',
        })
      )
    })

    it('should dispatch success notification with correct message for rejection', async () => {
      const dispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'reject'
      const data = { comments: 'Test comment' }
      vi.spyOn(secureapi, 'put').mockResolvedValueOnce({})

      await apiFunctions.completeCardSetPin(
        dispatch,
        approvalId,
        approvalType,
        data
      )

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Set pin credit card request has been rejected',
          type: 'success',
        })
      )
    })

    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const approvalId = '12345'
      const approvalType = 'reject'
      const data = { comments: 'Rejected by test' }
      const errorMessage = 'API connection error'

      vi.spyOn(secureapi, 'put').mockRejectedValue(new Error(errorMessage))
      vi.spyOn(console, 'error').mockImplementation(() => {})

      await apiFunctions.completeCardSetPin(
        mockDispatch,
        approvalId,
        approvalType,
        data
      )

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingSetPinCard(true)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        2,
        setIsLoadingSetPinCard(false)
      )
      expect(mockDispatch).toHaveBeenNthCalledWith(
        3,
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(console.error).toHaveBeenCalledWith(
        'card set pin approval completion request error:',
        expect.any(Error)
      )
    })
  })

  describe('resetCardPinRetryCounter', () => {
    it('should dispatch setIsLoadingResetPinRetries(true) at the beginning', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123456',
        cardIdentifierType: 'PAN',
        countryCode: 'US',
      }

      vi.spyOn(secureapi, 'post').mockResolvedValue({})

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, 'super')

      expect(mockDispatch).toHaveBeenNthCalledWith(
        1,
        setIsLoadingResetPinRetries(true)
      )
    })

    it('should make a POST request to the correct endpoint when type is "super"', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const type = 'super'
      const postMock = vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, type)

      expect(postMock).toHaveBeenCalledWith(
        'backoffice-bff/cards/reset-pin-try-counter',
        data
      )
    })

    it('should make a POST request to the correct endpoint when type is "make"', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const type = 'make'
      const postMock = vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, type)

      expect(postMock).toHaveBeenCalledWith(
        'backoffice-bff/cards/reset-pin-try-counter/make',
        data
      )
    })

    it('should dispatch success notification with correct message when type is "super"', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const type = 'super'
      vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, type)

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Reset Card PIN Retries request was successful',
          type: 'success',
        })
      )
    })

    it('should dispatch success notification with correct message when type is "make"', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      const type = 'make'
      vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, type)

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message:
            'Reset Card PIN Retries request was submitted successfully for approval',
          type: 'success',
        })
      )
    })

    it('should handle API errors by dispatching error notification', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123456',
        cardIdentifierType: 'PAN',
        countryCode: 'US',
      }

      const errorMessage = 'API Error'
      const error = new Error(errorMessage)

      vi.spyOn(secureapi, 'post').mockRejectedValue(error)

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, 'super')

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingResetPinRetries(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })

    it('should dispatch setIsLoadingResetPinRetries(false) after successful API call', async () => {
      const data = {
        comments: 'Test comment',
        cardIdentifierId: '123',
        cardIdentifierType: 'typeA',
        countryCode: 'US',
      }
      vi.spyOn(secureapi, 'post').mockResolvedValueOnce({})

      await apiFunctions.resetCardPinRetryCounter(mockDispatch, data, 'make')

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingResetPinRetries(false)
      )
    })
  })

  describe('completeResetCardPinRetryCounter', () => {
    it('should call secureapi.put with correct URL and data when type is "approve"', async () => {
      const data = { comments: 'Approved by test' }
      const approvalId = '12345'
      const type = 'approve'
      const mockResponse = { data: { data: 'success' } }

      vi.spyOn(secureapi, 'put').mockResolvedValue(mockResponse)

      const result = await apiFunctions.completeResetCardPinRetryCounter(
        mockDispatch,
        data,
        approvalId,
        type
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        `backoffice-bff/cards/reset-pin-try-counter/approve/12345`,
        data
      )
      expect(result).toBe('success')
    })

    it('should call secureapi.put with correct URL and data when type is "reject"', async () => {
      const data = { comments: 'Rejected by test' }
      const approvalId = '12345'
      const type = 'reject'
      const mockResponse = { data: { data: 'rejected' } }

      vi.spyOn(secureapi, 'put').mockResolvedValue(mockResponse)

      const result = await apiFunctions.completeResetCardPinRetryCounter(
        mockDispatch,
        data,
        approvalId,
        type
      )

      expect(secureapi.put).toHaveBeenCalledWith(
        `backoffice-bff/cards/reset-pin-try-counter/reject/12345`,
        data
      )
      expect(result).toBe('rejected')
    })

    it('should dispatch error notification when API call fails', async () => {
      const data = { comments: 'Test comment' }
      const approvalId = '12345'
      const type = 'approve'
      const errorMessage = 'API connection error'

      vi.spyOn(secureapi, 'put').mockRejectedValue(new Error(errorMessage))

      const result = await apiFunctions.completeResetCardPinRetryCounter(
        mockDispatch,
        data,
        approvalId,
        type
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(result).toBeUndefined()
    })

    it('should return response data on successful API call', async () => {
      const data = { comments: 'Test comment' }
      const approvalId = '12345'
      const type = 'approve'
      const expectedData = { success: true, id: approvalId }
      const mockResponse = { data: { data: expectedData } }

      vi.spyOn(secureapi, 'put').mockResolvedValue(mockResponse)

      const result = await apiFunctions.completeResetCardPinRetryCounter(
        mockDispatch,
        data,
        approvalId,
        type
      )

      expect(result).toEqual(expectedData)
    })
  })
})
