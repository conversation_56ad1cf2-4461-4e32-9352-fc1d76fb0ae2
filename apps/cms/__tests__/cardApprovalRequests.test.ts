import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import * as apiFunctions from '@/store/actions'
import { secureapi } from '@dtbx/store/utils'
import {
  setIsLoadingApprovals,
  setCardApprovalRequests,
  setCardApprovalsPagination,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { ICardApprovalRequest } from '@/store/interfaces'
import { createMockCardApprovalRequest, createMockApiError } from './mocks/data'

vi.mock('@dtbx/store/utils', () => ({
  secureapi: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
  },
}))

const mockData: ICardApprovalRequest[] = [
  createMockCardApprovalRequest({
    id: '1',
    status: 'PENDING',
    maker: '<PERSON>',
  }),
]

describe('Card Approval Requests Actions', () => {
  let mockDispatch: vi.MockedFunction<any>

  beforeEach(() => {
    mockDispatch = vi.fn()
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('getAllCardsApprovalRequests', () => {
    it('should fetch card approval requests with no parameters', async () => {
      const mockResponse = {
        data: {
          data: mockData,
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValue(mockResponse)

      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, '')

      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-auth/maker-checker/approvals?channel=CARDS'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setCardApprovalRequests(mockResponse.data.data)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
    })
    it('should fetch card approval requests with additional parameters', async () => {
      const params = 'pageNumber=2&pageSize=20'
      const mockResponse = {
        data: {
          data: mockData,
          pageNumber: 2,
          pageSize: 20,
          totalElements: 40,
          totalNumberOfPages: 2,
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, params)

      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-auth/maker-checker/approvals?channel=CARDS&pageNumber=2&pageSize=20'
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setCardApprovalRequests(mockResponse.data.data)
      )
    })
    it('should dispatch card approval requests data correctly', async () => {
      const mockResponse = {
        data: {
          data: mockData,
          pageNumber: 1,
          pageSize: 10,
          totalElements: 2,
          totalNumberOfPages: 1,
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, '')

      expect(mockDispatch).toHaveBeenCalledWith(
        setCardApprovalRequests(mockData)
      )
      expect(mockDispatch).toHaveBeenCalledTimes(4) // Loading true, data, pagination, loading false
    })

    it('should dispatch pagination data correctly', async () => {
      const paginationData = {
        pageNumber: 3,
        pageSize: 15,
        totalElements: 45,
        totalNumberOfPages: 3,
      }
      const mockResponse = {
        data: {
          data: [createMockCardApprovalRequest({ id: '1' })],
          ...paginationData,
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCardsApprovalRequests(
        mockDispatch,
        'pageNumber=3&pageSize=10'
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setCardApprovalsPagination(paginationData)
      )
      const paginationCallIndex = mockDispatch.mock.calls.findIndex(
        (call) => call[0].type === setCardApprovalsPagination.type
      )
      expect(paginationCallIndex).toBeGreaterThan(-1)
    })
    it('should handle empty response data', async () => {
      const mockResponse = {
        data: {
          data: [],
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalNumberOfPages: 0,
        },
      }
      vi.spyOn(secureapi, 'get').mockResolvedValueOnce(mockResponse)

      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, '')

      expect(mockDispatch).toHaveBeenCalledWith(setCardApprovalRequests([]))
      expect(mockDispatch).toHaveBeenCalledWith(
        setCardApprovalsPagination({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalNumberOfPages: 0,
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
    })

    it('should handle API errors by dispatching error notification', async () => {
      const errorMessage = 'Failed to fetch approval requests'
      const error = createMockApiError(errorMessage)

      vi.spyOn(secureapi, 'get').mockRejectedValue(error)

      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, '')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })

    it('should handle network errors gracefully', async () => {
      const networkError = new Error('Network Error')
      vi.spyOn(secureapi, 'get').mockRejectedValue(networkError)

      await apiFunctions.getAllCardsApprovalRequests(
        mockDispatch,
        'status=PENDING'
      )

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Network Error',
          type: 'error',
        })
      )
      expect(secureapi.get).toHaveBeenCalledWith(
        '/backoffice-auth/maker-checker/approvals?channel=CARDS&status=PENDING'
      )
    })

    it('should set loading to false even when API call fails', async () => {
      const error = new Error('API Error')
      vi.spyOn(secureapi, 'get').mockRejectedValue(error)

      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, '')

      const loadingTrueCalls = mockDispatch.mock.calls.filter(
        (call) =>
          call[0].type === setIsLoadingApprovals.type &&
          call[0].payload === true
      )
      const loadingFalseCalls = mockDispatch.mock.calls.filter(
        (call) =>
          call[0].type === setIsLoadingApprovals.type &&
          call[0].payload === false
      )

      expect(loadingTrueCalls).toHaveLength(1)
      expect(loadingFalseCalls).toHaveLength(1)
    })

    it('should handle malformed response data gracefully', async () => {
      const malformedResponse = {
        data: null,
      }
      vi.spyOn(secureapi, 'get').mockResolvedValue(malformedResponse)

      // The function should handle malformed data and dispatch an error
      await apiFunctions.getAllCardsApprovalRequests(mockDispatch, '')

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingApprovals(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: expect.any(String),
          type: 'error',
        })
      )
    })
  })
})
