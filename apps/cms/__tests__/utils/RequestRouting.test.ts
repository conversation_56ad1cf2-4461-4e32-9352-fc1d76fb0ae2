import { describe, it, expect, vi, beforeEach } from 'vitest'
import { ApprovalRequestRouting } from '../../src/app/approval-requests/RequestRouting'
import * as cardActions from '@/store/actions/CardsActions'
import { setSelectedCardApprovalRequest } from '@/store/reducers'
import { createMockCardApprovalRequest } from '../mocks/data'

// Mock the card actions
vi.mock('@/store/actions/CardsActions', () => ({
  getCardById: vi.fn(),
}))

describe('ApprovalRequestRouting', () => {
  const mockDispatch = vi.fn()
  const mockRouter = {
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Cards module routing', () => {
    it('should handle Cards module requests with entityId', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: 'card-123',
        makerCheckerType: {
          module: 'Cards',
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      expect(cardActions.getCardById).toHaveBeenCalledWith(
        mockDispatch,
        'card-123'
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCardApprovalRequest(mockRequest)
      )
      expect(mockRouter.push).toHaveBeenCalledWith('/credit-cards/c-card')
    })

    it('should handle Cards module requests without entityId', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: undefined,
        makerCheckerType: {
          module: 'Cards',
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      expect(cardActions.getCardById).not.toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCardApprovalRequest(mockRequest)
      )
      expect(mockRouter.push).toHaveBeenCalledWith('/credit-cards/c-card')
    })

    it('should handle Cards module requests with null entityId', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: null,
        makerCheckerType: {
          module: 'Cards',
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      expect(cardActions.getCardById).not.toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCardApprovalRequest(mockRequest)
      )
      expect(mockRouter.push).toHaveBeenCalledWith('/credit-cards/c-card')
    })

    it('should handle Cards module requests with empty string entityId', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: '',
        makerCheckerType: {
          module: 'Cards',
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      expect(cardActions.getCardById).not.toHaveBeenCalled()
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCardApprovalRequest(mockRequest)
      )
      expect(mockRouter.push).toHaveBeenCalledWith('/credit-cards/c-card')
    })
  })

  describe('unknown module handling', () => {
    it('should handle unknown module gracefully', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: 'entity-123',
        makerCheckerType: {
          module: 'UnknownModule',
          type: 'UNKNOWN_ACTION',
          channel: 'UNKNOWN',
          name: 'Unknown Action',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      // Should not call any actions for unknown modules
      expect(cardActions.getCardById).not.toHaveBeenCalled()
      expect(mockDispatch).not.toHaveBeenCalled()
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should handle requests with missing makerCheckerType', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: 'entity-123',
        makerCheckerType: null as any,
      })

      // Should throw an error because the function tries to access makerCheckerType.module
      await expect(
        ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)
      ).rejects.toThrow()
    })

    it('should handle requests with missing module in makerCheckerType', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: 'entity-123',
        makerCheckerType: {
          module: undefined as any,
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      expect(cardActions.getCardById).not.toHaveBeenCalled()
      expect(mockDispatch).not.toHaveBeenCalled()
      expect(mockRouter.push).not.toHaveBeenCalled()
    })
  })

  describe('error handling', () => {
    it('should propagate getCardById API errors', async () => {
      vi.mocked(cardActions.getCardById).mockRejectedValue(
        new Error('API Error')
      )

      const mockRequest = createMockCardApprovalRequest({
        entityId: 'card-123',
        makerCheckerType: {
          module: 'Cards',
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      // Should throw an error when getCardById fails
      await expect(
        ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)
      ).rejects.toThrow('API Error')

      expect(cardActions.getCardById).toHaveBeenCalledWith(
        mockDispatch,
        'card-123'
      )
    })
  })

  describe('case sensitivity', () => {
    it('should handle case-sensitive module names', async () => {
      const mockRequest = createMockCardApprovalRequest({
        entityId: 'card-123',
        makerCheckerType: {
          module: 'cards', // lowercase
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      // Should not match because 'cards' !== 'Cards'
      expect(cardActions.getCardById).not.toHaveBeenCalled()
      expect(mockDispatch).not.toHaveBeenCalled()
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should handle exact case match for Cards module', async () => {
      // Reset mocks to avoid interference from previous tests
      vi.mocked(cardActions.getCardById).mockResolvedValue(undefined)

      const mockRequest = createMockCardApprovalRequest({
        entityId: 'card-123',
        makerCheckerType: {
          module: 'Cards', // exact case
          type: 'ACTIVATE',
          channel: 'CARDS',
          name: 'Card Activation',
          checkerPermissions: [],
          makerPermissions: [],
          overridePermissions: [],
        },
      })

      await ApprovalRequestRouting(mockRequest, mockDispatch, mockRouter)

      expect(cardActions.getCardById).toHaveBeenCalledWith(
        mockDispatch,
        'card-123'
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setSelectedCardApprovalRequest(mockRequest)
      )
      expect(mockRouter.push).toHaveBeenCalledWith('/credit-cards/c-card')
    })
  })
})
