import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { CardsChangesLogDrawer } from '../../../src/app/Drawers/CardsChangesLogDrawer'
import { render, createMockStore } from '../../test-utils'
import { useState } from 'react'
import { Button } from '@mui/material'

// Mock dependencies
vi.mock('@dtbx/store/utils', () => ({
  formatTimestamp: vi.fn((date) => `Formatted: ${date}`),
}))

vi.mock('tiny-case', () => ({
  sentenceCase: vi.fn((str) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()),
}))

const mockApprovalRequests = [
  {
    id: 'approval-1',
    status: 'PENDING',
    maker: '<PERSON>',
    checker: null,
    makerCheckerType: {
      name: 'Activate Card',
      type: 'ACTIVATE_CARDS',
      module: 'Cards',
      channel: 'CARDS',
      checkerPermissions: [],
      makerPermissions: [],
      overridePermissions: [],
    },
    diff: [],
    dateCreated: '2025-07-23 12:12:34',
    dateModified: '2025-07-23 12:12:34',
    entityId: 'entity-1',
    entity: '{}',
    makerComments: 'Activate card request',
    checkerComments: null,
  },
  {
    id: 'approval-2',
    status: 'APPROVED',
    maker: 'Jane Smith',
    checker: 'Admin User',
    makerCheckerType: {
      name: 'Reset PIN',
      type: 'RESET_PIN',
      module: 'Cards',
      channel: 'CARDS',
      checkerPermissions: [],
      makerPermissions: [],
      overridePermissions: [],
    },
    diff: [],
    dateCreated: '2025-07-22 10:30:00',
    dateModified: '2025-07-22 11:15:00',
    entityId: 'entity-2',
    entity: '{}',
    makerComments: 'Reset PIN request',
    checkerComments: 'Approved',
  },
  {
    id: 'approval-3',
    status: 'REJECTED',
    maker: 'Bob Wilson',
    checker: 'Admin User',
    makerCheckerType: {
      name: 'Block Card',
      type: 'BLOCK_CARD',
      module: 'Cards',
      channel: 'CARDS',
      checkerPermissions: [],
      makerPermissions: [],
      overridePermissions: [],
    },
    diff: [],
    dateCreated: '2025-07-21 14:20:00',
    dateModified: '2025-07-21 15:45:00',
    entityId: 'entity-3',
    entity: '{}',
    makerComments: 'Block card request',
    checkerComments: 'Rejected due to insufficient reason',
  },
]



describe('CardsChangesLogDrawer', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should not show Changes Log button when no branch approval requests exist', () => {
    render(<CardsChangesLogDrawer />)

    expect(screen.queryByText('Changes Log')).not.toBeInTheDocument()
  })

  it('should show Changes Log button when branch approval requests exist', () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })

    render(<CardsChangesLogDrawer />, { store })

    expect(screen.getByText('Changes Log')).toBeInTheDocument()
  })

  it('should open drawer when Changes Log button is clicked', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })

    render(<CardsChangesLogDrawer />, { store })

    const changesLogButton = screen.getByRole('button', { name: 'Changes Log' })
    fireEvent.click(changesLogButton)

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search by event type, maker, status...')).toBeInTheDocument()
    })
  })

  it('should display all approval requests in the table', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })
    
    fireEvent.click(screen.getByText('Changes Log'))
    
    await waitFor(() => {
      expect(screen.getByText('Activate Card')).toBeInTheDocument()
      expect(screen.getByText('Reset PIN')).toBeInTheDocument()
      expect(screen.getByText('Block Card')).toBeInTheDocument()
      
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('Bob Wilson')).toBeInTheDocument()
    })
  })

  it('should display correct status colors', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      const pendingStatus = screen.getByText('Pending')
      const approvedStatus = screen.getByText('Approved')
      const rejectedStatus = screen.getByText('Rejected')
      
      expect(pendingStatus).toHaveStyle({ color: '#F79009' })
      expect(approvedStatus).toHaveStyle({ color: '#12B76A' })
      expect(rejectedStatus).toHaveStyle({ color: '#F04438' })
    })
  })

  it('should filter approval requests based on search term', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search by event type, maker, status...')
      fireEvent.change(searchInput, { target: { value: 'Activate' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText('Activate Card')).toBeInTheDocument()
      expect(screen.queryByText('Reset PIN')).not.toBeInTheDocument()
      expect(screen.queryByText('Block Card')).not.toBeInTheDocument()
    })
  })

  it('should filter by maker name', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search by event type, maker, status...')
      fireEvent.change(searchInput, { target: { value: 'Jane' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
      expect(screen.queryByText('Bob Wilson')).not.toBeInTheDocument()
    })
  })

  it('should filter by status', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search by event type, maker, status...')
      fireEvent.change(searchInput, { target: { value: 'PENDING' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
      expect(screen.queryByText('Bob Wilson')).not.toBeInTheDocument()
    })
  })

  it('should show empty state when no approvals match search', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      const searchInput = screen.getByPlaceholderText('Search by event type, maker, status...')
      fireEvent.change(searchInput, { target: { value: 'nonexistent' } })
    })
    
    await waitFor(() => {
      expect(screen.getByText('No approval requests found')).toBeInTheDocument()
      expect(screen.getByText('Try adjusting your search criteria')).toBeInTheDocument()
    })
  })

  it('should not show Changes Log button when no approval history available', () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: [],
      },
    })

    render(<CardsChangesLogDrawer />, { store })

    // Button should not be visible when there are no approval requests
    expect(screen.queryByRole('button', { name: 'Changes Log' })).not.toBeInTheDocument()
  })

  it('should display formatted timestamps correctly', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      expect(screen.getByText('Formatted: 2025-07-23 12:12:34')).toBeInTheDocument()
      expect(screen.getByText('Formatted: 2025-07-22 10:30:00')).toBeInTheDocument()
    })
  })

  it('should show N/A for checker when not available', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: mockApprovalRequests,
      },
    })
    
    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))
    
    await waitFor(() => {
      const naCells = screen.getAllByText('N/A')
      expect(naCells.length).toBeGreaterThan(0)
    })
  })

  it('should handle view button click', async () => {
    const store = createMockStore({
      approvals: {
        branchApprovalRequests: [mockApprovalRequests[0]],
      },
    })

    render(<CardsChangesLogDrawer />, { store })

    fireEvent.click(screen.getByRole('button', { name: 'Changes Log' }))

    await waitFor(() => {
      const viewButton = screen.getByText('View')
      fireEvent.click(viewButton)

      // Check if the details section appears after clicking view
      expect(screen.getByText('Request Details')).toBeInTheDocument()
    })
  })
})
