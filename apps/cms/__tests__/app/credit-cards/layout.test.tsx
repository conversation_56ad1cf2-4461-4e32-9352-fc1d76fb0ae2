import { describe, it, expect, vi, beforeEach } from 'vitest'
import CreditCardsLayout from '../../../src/app/credit-cards/layout'
import { render, createMockStore } from '../../test-utils'

// Mock the HasAccessToRights function
vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(),
}))

// Mock the CardHeaderIcon
vi.mock('@dtbx/ui/icons', () => ({
  CardHeaderIcon: ({ width, height }: any) => (
    <div data-testid="card-header-icon" style={{ width, height }}>
      Card Icon
    </div>
  ),
}))

describe('CreditCardsLayout Role-Based View', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should set isBranchListView to true when user has BRANCH_VIEW_CARDS right', async () => {
    // Mock user having BRANCH_VIEW_CARDS right
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)

    const store = createMockStore()

    render(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>,
      { store }
    )

    // Check that HasAccessToRights was called with correct right
    expect(HasAccessToRights).toHaveBeenCalledWith(['BRANCH_VIEW_CARDS'])

    // Check that the store state was updated to show branch view
    const state = store.getState()
    expect(state.cards.isBranchListView).toBe(true)
  })

  it('should set isBranchListView to false when user does not have BRANCH_VIEW_CARDS right', async () => {
    // Mock user not having BRANCH_VIEW_CARDS right
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(false)

    const store = createMockStore()

    render(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>,
      { store }
    )

    // Check that HasAccessToRights was called with correct right
    expect(HasAccessToRights).toHaveBeenCalledWith(['BRANCH_VIEW_CARDS'])

    // Check that the store state was updated to show ops view
    const state = store.getState()
    expect(state.cards.isBranchListView).toBe(false)
  })

  it('should render the layout with correct title', async () => {
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)

    const { getByText, getByTestId } = render(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>
    )

    expect(getByText('Credit Cards')).toBeInTheDocument()
    expect(getByTestId('card-header-icon')).toBeInTheDocument()
    expect(getByText('Test Content')).toBeInTheDocument()
  })

  it('should not render view switch since view is now role-based', async () => {
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)

    const { container } = render(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>
    )

    // The switch should not exist since view is role-based
    const switchElement = container.querySelector('.MuiFormControlLabel-root')
    expect(switchElement).toBeNull()
  })
})
