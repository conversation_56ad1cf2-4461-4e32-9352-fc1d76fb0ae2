import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import PageHeader from '../../../../src/app/credit-cards/c-card/PageHeader'
import { render, createMockStore } from '../../../test-utils'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: vi.fn((param) => {
      if (param === 'origin') return 'cardsPage'
      return null
    }),
  }),
}))

// Mock custom router
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: vi.fn(),
  }),
}))

// Mock drawer components
vi.mock('../../../../src/app/Drawers', () => ({
  CardsChangesLogDrawer: () => <div data-testid="changes-log-drawer">Changes Log Drawer</div>,
  CardsApprovalRequestDrawer: ({ open, selectedCardApprovalRequest }: any) => (
    <div data-testid="approval-request-drawer">
      {open && (
        <div>
          <span>Approval Drawer Open</span>
          <span data-testid="approval-type">{selectedCardApprovalRequest?.makerCheckerType?.name}</span>
        </div>
      )}
    </div>
  ),
}))

const mockCard = {
  cardId: 'card-123',
  customerName: 'John Doe',
  active: true,
  pan: '1234',
  phoneNumber: '+254712345678',
}

const mockApprovalRequest = {
  id: 'approval-123',
  status: 'PENDING',
  maker: 'Leroy Ombiji',
  makerCheckerType: {
    name: 'Activate Card',
    type: 'ACTIVATE_CARDS',
    module: 'Cards',
    channel: 'CARDS',
    checkerPermissions: [],
    makerPermissions: [],
    overridePermissions: [],
  },
  diff: [],
  dateCreated: '2025-07-23 12:12:34',
  dateModified: '2025-07-23 12:12:34',
  entityId: 'entity-123',
  entity: '{}',
  makerComments: 'Test comment',
  checkerComments: null,
  checker: null,
}



describe('PageHeader', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render customer name and active status', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
    })

    render(<PageHeader />, { store })

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('Active')).toBeInTheDocument()
  })

  it('should show inactive status for inactive card', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: { ...mockCard, active: false },
      },
    })

    render(<PageHeader />, { store })
    expect(screen.getByText('Inactive')).toBeInTheDocument()
  })

  it('should not show pending approval chip when no approvals exist', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
    })

    render(<PageHeader />, { store })

    expect(screen.queryByText('Pending approval :')).not.toBeInTheDocument()
  })

  it('should show pending approval chip when selectedCardApprovalRequest exists', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
      approvals: {
        selectedCardApprovalRequest: mockApprovalRequest,
      },
    })

    render(<PageHeader />, { store })

    expect(screen.getByText('Pending approval :')).toBeInTheDocument()
    expect(screen.getByText('Activate Card')).toBeInTheDocument()
  })

  it('should show pending approval chip when branch approval exists', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
      approvals: {
        branchApprovalRequests: [mockApprovalRequest],
      },
    })

    render(<PageHeader />, { store })
    
    expect(screen.getByText('Pending approval :')).toBeInTheDocument()
    expect(screen.getByText('Activate Card')).toBeInTheDocument()
  })

  it('should prioritize selectedCardApprovalRequest over branch approvals', () => {
    const selectedApproval = {
      ...mockApprovalRequest,
      id: 'selected-approval',
      makerCheckerType: {
        ...mockApprovalRequest.makerCheckerType,
        name: 'Reset PIN',
      },
    }

    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
      approvals: {
        selectedCardApprovalRequest: selectedApproval,
        branchApprovalRequests: [mockApprovalRequest],
      },
    })

    render(<PageHeader />, { store })

    expect(screen.getByText('Reset PIN')).toBeInTheDocument()
    expect(screen.queryByText('Activate Card')).not.toBeInTheDocument()
  })

  it('should open approval drawer when pending approval chip is clicked', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
      approvals: {
        selectedCardApprovalRequest: mockApprovalRequest,
      },
    })

    render(<PageHeader />, { store })

    const approvalChip = screen.getByText('Pending approval :').closest('div')
    fireEvent.click(approvalChip!)

    expect(screen.getByText('Approval Drawer Open')).toBeInTheDocument()
    expect(screen.getByTestId('approval-type')).toHaveTextContent('Activate Card')
  })

  it('should handle non-pending branch approvals correctly', () => {
    const approvedRequest = {
      ...mockApprovalRequest,
      status: 'APPROVED',
    }

    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
      approvals: {
        branchApprovalRequests: [approvedRequest],
      },
    })

    render(<PageHeader />, { store })

    // Should not show pending approval chip for approved requests
    expect(screen.queryByText('Pending approval :')).not.toBeInTheDocument()
  })

  it('should find first pending approval from multiple branch approvals', () => {
    const approvedRequest = { ...mockApprovalRequest, status: 'APPROVED' }
    const pendingRequest = {
      ...mockApprovalRequest,
      id: 'pending-approval',
      status: 'PENDING',
      makerCheckerType: {
        ...mockApprovalRequest.makerCheckerType,
        name: 'Set PIN',
      },
    }

    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
      approvals: {
        branchApprovalRequests: [approvedRequest, pendingRequest],
      },
    })

    render(<PageHeader />, { store })

    expect(screen.getByText('Pending approval :')).toBeInTheDocument()
    expect(screen.getByText('Set PIN')).toBeInTheDocument()
  })

  it('should render breadcrumb navigation correctly', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
    })

    render(<PageHeader />, { store })

    expect(screen.getByText('Credit Cards')).toBeInTheDocument()
    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })

  it('should render changes log drawer', () => {
    const store = createMockStore({
      cards: {
        selectedCardToView: mockCard,
      },
    })

    render(<PageHeader />, { store })

    expect(screen.getByTestId('changes-log-drawer')).toBeInTheDocument()
  })
})
