import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../test-utils'
import PrePaidCardsPage from '../../../src/app/prepaid-cards/page'

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material')
  return {
    ...actual,
    Stack: ({ children, ...props }: any) => <div data-testid="stack" {...props}>{children}</div>,
  }
})

describe('PrePaidCardsPage', () => {
  it('should render without crashing', () => {
    expect(() => {
      render(<PrePaidCardsPage />)
    }).not.toThrow()
  })

  it('should render the page title', () => {
    render(<PrePaidCardsPage />)
    
    expect(screen.getByText('Prepaid Cards')).toBeInTheDocument()
  })

  it('should render within a Stack component', () => {
    render(<PrePaidCardsPage />)
    
    expect(screen.getByTestId('stack')).toBeInTheDocument()
    expect(screen.getByTestId('stack')).toHaveTextContent('Prepaid Cards')
  })

  it('should have correct text content', () => {
    render(<PrePaidCardsPage />)
    
    const stackElement = screen.getByTestId('stack')
    expect(stackElement).toHaveTextContent('Prepaid Cards')
    expect(stackElement.textContent).toBe('Prepaid Cards ')
  })

  it('should render consistently on multiple renders', () => {
    const { rerender } = render(<PrePaidCardsPage />)
    
    expect(screen.getByText('Prepaid Cards')).toBeInTheDocument()
    
    rerender(<PrePaidCardsPage />)
    
    expect(screen.getByText('Prepaid Cards')).toBeInTheDocument()
  })

  it('should have proper component structure', () => {
    const { container } = render(<PrePaidCardsPage />)
    
    expect(container.firstChild).toHaveAttribute('data-testid', 'stack')
    expect(container.firstChild).toHaveTextContent('Prepaid Cards')
  })

  it('should be accessible', () => {
    render(<PrePaidCardsPage />)
    
    const pageContent = screen.getByText('Prepaid Cards')
    expect(pageContent).toBeInTheDocument()
    expect(pageContent).toBeVisible()
  })

  it('should not have any interactive elements', () => {
    render(<PrePaidCardsPage />)
    
    const buttons = screen.queryAllByRole('button')
    const links = screen.queryAllByRole('link')
    const inputs = screen.queryAllByRole('textbox')
    
    expect(buttons).toHaveLength(0)
    expect(links).toHaveLength(0)
    expect(inputs).toHaveLength(0)
  })

  it('should render as a simple static component', () => {
    const { container } = render(<PrePaidCardsPage />)
    
    expect(container.children).toHaveLength(1)
    expect(container.firstChild).toHaveTextContent('Prepaid Cards')
  })

  it('should match expected DOM structure', () => {
    const { container } = render(<PrePaidCardsPage />)
    
    expect(container.innerHTML).toContain('Prepaid Cards ')
    expect(container.querySelector('[data-testid="stack"]')).toBeInTheDocument()
  })

  it('should handle text content with whitespace correctly', () => {
    render(<PrePaidCardsPage />)
    
    const stackElement = screen.getByTestId('stack')
    // The original component has "Prepaid Cards " with a trailing space
    expect(stackElement.textContent).toBe('Prepaid Cards ')
    expect(stackElement.textContent?.trim()).toBe('Prepaid Cards')
  })

  it('should be a functional component', () => {
    const component = PrePaidCardsPage
    expect(typeof component).toBe('function')
    expect(component.name).toBe('PrePaidCardsPage')
  })
})
