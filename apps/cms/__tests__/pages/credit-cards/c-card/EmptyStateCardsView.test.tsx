import { describe, it, expect } from 'vitest'
import { render, screen } from '../../../test-utils'
import { EmptyStateCardsView } from '../../../../src/app/credit-cards/c-card/EmptyStateCardsView'

describe('EmptyStateCardsView', () => {
  it('should render the empty state message', () => {
    render(<EmptyStateCardsView />)

    expect(screen.getByText('No Credit Cards found')).toBeInTheDocument()
  })

  it('should render the description text', () => {
    render(<EmptyStateCardsView />)

    expect(
      screen.getByText(
        'There are currently no credit cards found. Once available, they will be listed here.'
      )
    ).toBeInTheDocument()
  })

  it('should render the credit card icon', () => {
    render(<EmptyStateCardsView />)

    const icon = screen.getByAltText('search')
    expect(icon).toBeInTheDocument()
    expect(icon).toHaveAttribute('src', 'icons/credit-card-02.svg')
  })

  it('should be accessible', () => {
    render(<EmptyStateCardsView />)

    const emptyStateElement = screen.getByText('No Credit Cards found')
    expect(emptyStateElement).toBeInTheDocument()
  })

  it('should render consistently', () => {
    const { rerender } = render(<EmptyStateCardsView />)

    expect(screen.getByText('No Credit Cards found')).toBeInTheDocument()

    rerender(<EmptyStateCardsView />)

    expect(screen.getByText('No Credit Cards found')).toBeInTheDocument()
  })

  it('should have proper structure', () => {
    const { container } = render(<EmptyStateCardsView />)

    expect(container.firstChild).toBeInTheDocument()
    expect(screen.getByText('No Credit Cards found')).toBeInTheDocument()
  })

  it('should have proper heading hierarchy', () => {
    render(<EmptyStateCardsView />)

    const heading = screen.getByRole('heading', { level: 6 })
    expect(heading).toHaveTextContent('No Credit Cards found')
  })
})
