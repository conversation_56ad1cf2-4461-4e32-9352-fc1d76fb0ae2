import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '../../test-utils'
import Layout from '../../../src/app/credit-cards/layout'

// Mock the store hooks since the layout uses them
vi.mock('@/store', () => ({
  useAppDispatch: () => vi.fn(),
  useAppSelector: () => ({ isBranchListView: false }),
}))

describe('Credit Cards Layout', () => {
  it('should render children within layout', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>

    render(
      <Layout>
        <TestChild />
      </Layout>
    )

    // The layout renders children directly, not within AccessControlWrapper
    expect(screen.getByTestId('test-child')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()
    expect(screen.getByText('Credit Cards')).toBeInTheDocument() // Layout header
  })

  it('should render multiple children', () => {
    render(
      <Layout>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </Layout>
    )
    
    // The layout renders children directly
    expect(screen.getByTestId('child-1')).toBeInTheDocument()
    expect(screen.getByTestId('child-2')).toBeInTheDocument()
    expect(screen.getByText('Credit Cards')).toBeInTheDocument() // Layout header
  })

  it('should render without children', () => {
    render(<Layout />)

    // The layout should still render its header even without children
    expect(screen.getByText('Credit Cards')).toBeInTheDocument()
  })

  it('should pass through all children unchanged', () => {
    const ComplexChild = () => (
      <div>
        <h1>Title</h1>
        <p>Description</p>
        <button>Action</button>
      </div>
    )
    
    render(
      <Layout>
        <ComplexChild />
      </Layout>
    )
    
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Title')
    expect(screen.getByText('Description')).toBeInTheDocument()
    expect(screen.getByRole('button')).toHaveTextContent('Action')
  })
})
