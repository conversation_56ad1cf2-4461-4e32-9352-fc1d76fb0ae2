import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '../../test-utils'
import Rejected from '../../../src/app/approval-requests/Rejected'
import * as approvalActions from '@/store/actions/ApprovalRequests'
import * as cardActions from '@/store/actions/CardsActions'
import * as requestRouting from '../../../src/app/approval-requests/RequestRouting'
import { createMockCardApprovalRequest } from '../../mocks/data'

// Mock the approval actions
vi.mock('@/store/actions/ApprovalRequests', () => ({
  getAllCardsApprovalRequests: vi.fn(),
}))

vi.mock('@/store/actions/CardsActions', () => ({
  getCardById: vi.fn(),
}))

vi.mock('../../../src/app/approval-requests/RequestRouting', () => ({
  ApprovalRequestRouting: vi.fn(),
}))

// Mock the MoreMenu component
vi.mock('../../../src/app/approval-requests/MoreMenu', () => ({
  AllApprovalRequestsMoreMenu: ({ request }: any) => (
    <div data-testid={`more-menu-${request.id}`}>More Menu</div>
  ),
}))

// Mock debounce hook
vi.mock('@dtbx/ui/hooks', () => ({
  useCustomRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useDebounce: (value: string) => value, // Return immediately for testing
}))

describe('Rejected Component', () => {
  const mockDispatch = vi.fn()
  const mockRouter = {
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }

  const mockApprovalRequests = [
    createMockCardApprovalRequest({
      id: '1',
      status: 'APPROVED',
      maker: 'John Doe',
      checker: 'Jane Smith',
      makerFirstName: 'John',
      makerLastName: 'Doe',
      dateCreated: '2023-01-01T10:00:00Z',
      dateModified: '2023-01-01T12:00:00Z',
      entityId: 'card-123',
      makerCheckerType: {
        channel: 'CARDS',
        name: 'CARD_ACTIVATION',
        module: 'CARDS',
        checkerPermissions: [],
        makerPermissions: [],
        overridePermissions: [],
        type: 'ACTIVATE',
      },
    }),
    createMockCardApprovalRequest({
      id: '2',
      status: 'REJECTED',
      maker: 'Bob Wilson',
      checker: 'Alice Brown',
      makerFirstName: 'Bob',
      makerLastName: 'Wilson',
      dateCreated: '2023-01-02T11:00:00Z',
      dateModified: '2023-01-02T13:00:00Z',
      entityId: 'card-456',
      makerCheckerType: {
        channel: 'CARDS',
        name: 'PIN_RESET',
        module: 'CARDS',
        checkerPermissions: [],
        makerPermissions: [],
        overridePermissions: [],
        type: 'RESET_PIN',
      },
    }),
  ]

  const mockPagination = {
    pageNumber: 1,
    pageSize: 10,
    totalElements: 2,
    totalNumberOfPages: 1,
  }

  const defaultState = {
    approvals: {
      isLoadingApprovals: false,
      cardApprovalRequests: mockApprovalRequests,
      cardApprovalsPagination: mockPagination,
      selectedCardApprovalRequest: null,
    },
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render the all approval requests table', () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      expect(screen.getByText('All Approval Requests')).toBeInTheDocument()
      expect(screen.getByText('Showing 2 of 2 records')).toBeInTheDocument()
    })

    it('should render search input', () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      const searchInput = screen.getByRole('textbox')
      expect(searchInput).toBeInTheDocument()
    })

    it('should render table headers including checker columns', () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      expect(screen.getByText('Request Type')).toBeInTheDocument()
      expect(screen.getByText('Module')).toBeInTheDocument()
      expect(screen.getByText('Maker')).toBeInTheDocument()
      expect(screen.getByText('Maker Timestamp')).toBeInTheDocument()
      expect(screen.getByText('Checker')).toBeInTheDocument()
      expect(screen.getByText('Checker Timestamp')).toBeInTheDocument()
      expect(screen.getByText('Status')).toBeInTheDocument()
      expect(screen.getByText('Action')).toBeInTheDocument()
    })

    it('should render approval request data with checker information', () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      expect(screen.getByText('Card activation')).toBeInTheDocument()
      expect(screen.getByText('Pin reset')).toBeInTheDocument()
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Bob Wilson')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
      expect(screen.getByText('Alice Brown')).toBeInTheDocument()
    })

    it('should render status chips', () => {
      render(<Rejected />, { preloadedState: defaultState })

      // The CustomerStatusChip component renders the status, check for the actual rendered text
      expect(screen.getByText('Approved')).toBeInTheDocument()
      expect(screen.getByText('Rejected')).toBeInTheDocument()
    })

    it('should handle missing checker data', () => {
      const requestWithoutChecker = createMockCardApprovalRequest({
        id: '3',
        status: 'PENDING',
        checker: undefined,
        dateModified: undefined,
      })

      const stateWithMissingChecker = {
        ...defaultState,
        approvals: {
          ...defaultState.approvals,
          cardApprovalRequests: [requestWithoutChecker],
        },
      }

      render(<Rejected />, { preloadedState: stateWithMissingChecker })
      
      expect(screen.getByText('-')).toBeInTheDocument()
    })
  })

  describe('loading state', () => {
    it('should show loading skeleton when isLoadingApprovals is true', () => {
      const loadingState = {
        ...defaultState,
        approvals: {
          ...defaultState.approvals,
          isLoadingApprovals: true,
        },
      }

      render(<Rejected />, { preloadedState: loadingState })

      // Check for the skeleton components that are actually rendered
      const skeletons = document.querySelectorAll('.MuiSkeleton-root')
      expect(skeletons.length).toBeGreaterThan(0)
      expect(screen.queryByText('All Approval Requests')).not.toBeInTheDocument()
    })
  })

  describe('search functionality', () => {
    it('should call getAllCardsApprovalRequests when search term changes', async () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      const searchInput = screen.getByRole('textbox')
      fireEvent.change(searchInput, { target: { value: 'John' } })
      
      await waitFor(() => {
        expect(approvalActions.getAllCardsApprovalRequests).toHaveBeenCalledWith(
          expect.any(Function),
          'makerFirstName=John'
        )
      })
    })

    it('should call getAllCardsApprovalRequests with default params when search is cleared', async () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      const searchInput = screen.getByRole('textbox')
      fireEvent.change(searchInput, { target: { value: 'John' } })
      fireEvent.change(searchInput, { target: { value: '' } })
      
      await waitFor(() => {
        expect(approvalActions.getAllCardsApprovalRequests).toHaveBeenCalledWith(
          expect.any(Function),
          'page=1&size=10'
        )
      })
    })
  })

  describe('view functionality', () => {
    it('should call ApprovalRequestRouting for pending requests', async () => {
      const pendingRequest = createMockCardApprovalRequest({
        id: '3',
        status: 'PENDING',
      })

      const stateWithPending = {
        ...defaultState,
        approvals: {
          ...defaultState.approvals,
          cardApprovalRequests: [pendingRequest],
        },
      }

      render(<Rejected />, { preloadedState: stateWithPending })
      
      // This would require triggering the handleView function
      // In a real test, you'd need to interact with the component that calls handleView
    })

    it('should navigate to card details for non-pending requests', async () => {
      // This would require triggering the handleView function
      // The component should call getCardById and router.push for non-pending requests
    })
  })

  describe('pagination', () => {
    it('should render pagination component', () => {
      render(<Rejected />, { preloadedState: defaultState })

      // Check for pagination buttons instead of navigation role
      expect(screen.getByRole('button', { name: /previous/i })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /next/i })).toBeInTheDocument()
    })

    it('should handle pagination changes correctly', async () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      // This would require simulating pagination clicks
      // The component should call getAllCardsApprovalRequests with new page parameters
    })
  })

  describe('date formatting', () => {
    it('should format maker and checker timestamps correctly', () => {
      render(<Rejected />, { preloadedState: defaultState })

      // dayjs formats dates as 'MMMM D, YYYY hh:mm A' which produces these formats
      expect(screen.getByText('January 1, 2023 01:00 PM')).toBeInTheDocument()
      expect(screen.getByText('January 1, 2023 03:00 PM')).toBeInTheDocument()
      expect(screen.getByText('January 2, 2023 02:00 PM')).toBeInTheDocument()
      expect(screen.getByText('January 2, 2023 04:00 PM')).toBeInTheDocument()
    })
  })

  describe('accessibility', () => {
    it('should have proper table accessibility attributes', () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      const table = screen.getByRole('table')
      expect(table).toHaveAttribute('aria-label', 'rejected approvals table')
    })
  })

  describe('component lifecycle', () => {
    it('should call getAllCardsApprovalRequests on mount', () => {
      render(<Rejected />, { preloadedState: defaultState })
      
      expect(approvalActions.getAllCardsApprovalRequests).toHaveBeenCalledWith(
        expect.any(Function),
        'page=1&size=10'
      )
    })
  })

  describe('error handling', () => {
    it('should handle missing pagination data gracefully', () => {
      const stateWithoutPagination = {
        ...defaultState,
        approvals: {
          ...defaultState.approvals,
          cardApprovalsPagination: null,
        },
      }
      
      expect(() => 
        render(<Rejected />, { preloadedState: stateWithoutPagination })
      ).not.toThrow()
    })
  })
})
