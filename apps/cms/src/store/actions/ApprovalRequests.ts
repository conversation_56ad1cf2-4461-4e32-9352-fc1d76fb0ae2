import { Dispatch } from '@reduxjs/toolkit'
import {
  setCardApprovalRequests,
  setCardApprovalsPagination,
  setIsLoadingApprovals,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { faker } from '@faker-js/faker'
import { secureapi, secureapi2 } from '@dtbx/store/utils'

export const getAllCardsApprovalRequests = async (
  dispatch: Dispatch,
  params: string
) => {
  try {
    dispatch(setIsLoadingApprovals(true))
    const query = `channel=CARDS${params ? `&${params}` : ''}`
    const resp = await secureapi.get(
      `/backoffice-auth/maker-checker/approvals?${query}`
    )

    const { pageNumber, pageSize, totalElements, totalNumberOfPages } =
      resp.data
    dispatch(setCardApprovalRequests(resp.data.data))
    dispatch(
      setCardApprovalsPagination({
        pageNumber,
        pageSize,
        totalElements,
        totalNumberOfPages,
      })
    )
    dispatch(setIsLoadingApprovals(false))
  } catch (e) {
    const message = (e as Error).message
    dispatch(
      setNotification({
        message: message,
        type: 'error',
      })
    )
    dispatch(setIsLoadingApprovals(false))
  }
}
