import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  ICardStatus,
  ICreditCard,
  ICardResponse,
  InitialState,
  IPagination,
  IListCreditCard,
} from '@/store/interfaces'

const initialState: InitialState = {
  isLoadingCards: false,
  isLoadingSingleCard: false,
  isLoadingActivateCard: false,
  isLoadingSetPinCard: false,
  isLoadingResetPinRetries: false,
  isLoadingPan: false,
  cardsError: false,
  cardsSuccess: false,
  creditCardsList: [],
  creditCardResponse: {} as ICardResponse,
  selectedCardStatus: 'inactive',
  selectedCardToView: {} as ICreditCard,
  isBranchListView: false,
  currentTabIndex: 0,
  //branch view state
  isLoadingBranchCards: false,
  branchCardsList: [],
  branchCardListPagination: {} as IPagination,
}

const cardSlice = createSlice({
  name: 'card',
  initialState: initialState,
  reducers: {
    setIsLoadingCards(state, action: PayloadAction<boolean>) {
      state.isLoadingCards = action.payload
    },
    setIsLoadingPan(state, action: PayloadAction<boolean>) {
      state.isLoadingPan = action.payload
    },
    setCardsError: (state, action) => {
      state.cardsError = action.payload
    },
    setCreditCardsList: (state, action: PayloadAction<ICreditCard[]>) => {
      state.creditCardsList = action.payload
    },
    setCardsSuccess: (state, action) => {
      state.cardsSuccess = action.payload
    },
    setSelectedCardStatus: (state, action: PayloadAction<ICardStatus>) => {
      state.selectedCardStatus = action.payload
    },
    setSelectedCardToView: (state, action: PayloadAction<ICreditCard>) => {
      state.selectedCardToView = action.payload
    },
    setCardResponse: (state, action: PayloadAction<ICardResponse>) => {
      state.creditCardResponse = action.payload
    },
    setIsLoadingSingleCard: (state, action: PayloadAction<boolean>) => {
      state.isLoadingSingleCard = action.payload
    },
    setIsLoadingActivateCard: (state, action: PayloadAction<boolean>) => {
      state.isLoadingActivateCard = action.payload
    },
    setIsLoadingSetPinCard: (state, action: PayloadAction<boolean>) => {
      state.isLoadingSetPinCard = action.payload
    },
    setIsBranchListView: (state, action: PayloadAction<boolean>) => {
      state.isBranchListView = action.payload
    },
    setIsLoadingBranchCards: (state, action: PayloadAction<boolean>) => {
      state.isLoadingBranchCards = action.payload
    },
    setIsLoadingResetPinRetries: (state, action: PayloadAction<boolean>) => {
      state.isLoadingResetPinRetries = action.payload
    },
    setBranchCardsList: (state, action: PayloadAction<IListCreditCard[]>) => {
      state.branchCardsList = action.payload
    },
    setBranchCardsListPagination: (
      state,
      action: PayloadAction<IPagination>
    ) => {
      state.branchCardListPagination = action.payload
    },
    setCurrentTabIndex: (state, action) => {
      state.currentTabIndex = action.payload
    },
  },
})

export const {
  setIsLoadingCards,
  setIsLoadingPan,
  setIsLoadingSingleCard,
  setIsLoadingActivateCard,
  setIsLoadingSetPinCard,
  setIsLoadingResetPinRetries,
  setIsBranchListView,
  setCreditCardsList,
  setSelectedCardStatus,
  setSelectedCardToView,
  setCardResponse,
  //branch cards
  setIsLoadingBranchCards,
  setBranchCardsList,
  setBranchCardsListPagination,
  setCurrentTabIndex,
} = cardSlice.actions
export default cardSlice.reducer
