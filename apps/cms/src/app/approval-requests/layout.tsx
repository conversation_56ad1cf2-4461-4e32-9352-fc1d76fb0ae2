'use client'

import { Stack, Typography } from '@mui/material'
import React from 'react'
import { RequestsApprovalIcon } from '@dtbx/ui/icons'

export default function ApprovalRequestsLayout(props: {
  children: React.ReactNode
}) {
  return (
    <Stack>
      <Stack
        sx={{
          marginLeft: '2%',
          marginTop: '0.2%',
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'center',
          gap: '8px',
          py: '8px',
        }}
      >
        <RequestsApprovalIcon width="28" height="26" />
        <Typography variant="h5">Approval Requests</Typography>
      </Stack>
      <Stack>{props.children}</Stack>
    </Stack>
  )
}
