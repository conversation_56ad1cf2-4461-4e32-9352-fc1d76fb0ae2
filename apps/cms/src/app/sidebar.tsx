"use-client"
import {
  RequestsApprovalIcon,
  CreditCardIcon,
  DebitCardIcon,
  PrepaidCardIcon,
} from '@dtbx/ui/icons'
import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'
import { HasAccessToRights } from '@dtbx/store/utils'

// Function to get sidebar configuration based on user rights
export const getSidebarConfig = (): ISidebarConfigItem[] => {
  const hasBranchViewRights = HasAccessToRights(['BRANCH_VIEW_CARDS'])

  const baseConfig: ISidebarConfigItem[] = [
    {
      id: '1',
      title: 'Credit Cards',
      path: '/credit-cards',
      module: 'Cards',
      icon: <CreditCardIcon />,
      isProductionReady: true,
    },
    {
      id: '2',
      title: 'Debit Cards',
      path: '/debit-cards',
      module: 'Cards',
      icon: <DebitCardIcon />,
      isProductionReady: true,
    },
    {
      id: '3',
      title: 'Pre Paid Cards',
      path: '/prepaid-cards',
      module: 'Cards',
      icon: <PrepaidCardIcon />,
      isProductionReady: true,
    },
  ]

  if (!hasBranchViewRights) {
    baseConfig.push({
      id: '4',
      title: 'Approval Requests',
      path: '/approval-requests',
      module: 'Cards',
      icon: <RequestsApprovalIcon />,
      isProductionReady: true,
    })
  }

  return baseConfig
}

export const sidebarConfig: ISidebarConfigItem[] = [
  {
    id: '1',
    title: 'Credit Cards',
    path: '/credit-cards',
    module: 'Cards',
    icon: <CreditCardIcon />,
    isProductionReady: true,
  },
  {
    id: '2',
    title: 'Debit Cards',
    path: '/debit-cards',
    module: 'Cards',
    icon: <DebitCardIcon />,
    isProductionReady: true,
  },
  {
    id: '3',
    title: 'Pre Paid Cards',
    path: '/prepaid-cards',
    module: 'Cards',
    icon: <PrepaidCardIcon />,
    isProductionReady: true,
  },
  {
    id: '4',
    title: 'Approval Requests',
    path: '/approval-requests',
    module: 'Cards',
    icon: <RequestsApprovalIcon />,
    isProductionReady: true,
  },
]
