import { Button } from '@mui/material'
import React, { useState } from 'react'
import { Dialog } from '@dtbx/ui/components/Overlay'
import {
  activateCards,
  getCardById,
  resetCardPin,
} from '@/store/actions/CardsActions'
import { AccessControlWrapper, HasAccessToRights } from '@dtbx/store/utils'
import { ICreditCard } from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import { setNotification } from '@dtbx/store/reducers'
import { LoadingButton } from '@dtbx/ui/components'

export const ActivateCreditCard = ({ card }: { card: ICreditCard }) => {
  const [open, setOpen] = useState(false)
  const [operationType, setOperationType] = useState<'activate' | 'resetPin'>(
    'activate'
  )
  const dispatch = useAppDispatch()
  const { isLoadingActivateCard, isLoadingSetPinCard } = useAppSelector(
    (state) => state.cards
  )

  const reasons = [
    'New Card Issuance',
    'Card Replacement',
    'Delayed Activation',
    'forgot Pin',
    'Other',
  ]

  const operationConfig = {
    activate: {
      buttonText: 'Activate',
      title: 'Activate Credit Card',
      reasons: reasons,
      description: 'Select a reason for activation:',
      defaultComment: '',
    },
    resetPin: {
      buttonText: 'Reset PIN',
      title: 'Reset Card PIN',
      reasons: reasons,
      description: 'Select a reason to reset the PIN?',
      defaultComment: 'Reset Card PIN',
    },
  }

  const currentConfig = operationConfig[operationType]

  const handleOperation = async (reasons: string[]) => {
    try {
      const basePayload = {
        cardIdentifierId: card.cardId,
        cardIdentifierType: 'EXID',
        countryCode: 'KE',
      }

      if (operationType === 'activate') {
        const payload = {
          ...basePayload,
          comments: reasons.join(',') || 'Card activation',
        }

        if (HasAccessToRights(['SUPER_ACTIVATE_CARDS'])) {
          await activateCards(dispatch, payload, 'super')
        } else if (HasAccessToRights(['MAKE_ACTIVATE_CARDS', 'BRANCH_MAKE_ACTIVATE_CARDS'])) {
          await activateCards(dispatch, payload, 'make')
        }
      } else {
        const payload = {
          ...basePayload,
          comments: reasons.join(',') || 'Reset Card PIN',
        }

        if (HasAccessToRights(['SUPER_CARDS_SET_PIN'])) {
          await resetCardPin(dispatch, payload, 'super')
        } else if (HasAccessToRights(['MAKE_CARDS_SET_PIN'])) {
          await resetCardPin(dispatch, payload, 'make')
        }
      }

      await getCardById(dispatch, card.cardId)
    } catch (error) {
      console.error('Operation failed:', error)
      dispatch(
        setNotification({
          message: 'Operation failed. Please try again.',
          type: 'error',
        })
      )
    } finally {
      setOpen(false)
    }
  }

  const handleButtonClick = () => {
    const operation = card.active ? 'resetPin' : 'activate'
    setOperationType(operation)
    setOpen(true)
  }

  return (
    <>
      <AccessControlWrapper
        rights={[
          'SUPER_ACTIVATE_CARDS',
          'MAKE_ACTIVATE_CARDS',
          'BRANCH_MAKE_ACTIVATE_CARDS',
          'SUPER_CARDS_SET_PIN',
          'MAKE_CARDS_SET_PIN',
        ]}
      >
        {isLoadingActivateCard || isLoadingSetPinCard ? (
          <LoadingButton size="medium" />
        ) : (
          <Button variant="contained" onClick={handleButtonClick}>
            {card.active ? 'Reset Card PIN' : 'Activate'}
          </Button>
        )}
      </AccessControlWrapper>

      <Dialog
        buttonText={currentConfig.buttonText}
        title={currentConfig.title}
        open={open}
        isLoading={isLoadingActivateCard}
        descriptionText={currentConfig.description}
        setOpen={setOpen}
        buttonProps={{
          color: '#EB0045',
        }}
        reasons={currentConfig.reasons}
        onClick={handleOperation}
      />
    </>
  )
}
