import { Box, Stack, Typography } from '@mui/material'
import Image from 'next/image'
import React from 'react'

export const EmptyStateBranchView = () => {
  return (
    <Stack
      sx={{
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        py: '5vh',
      }}
    >
      {' '}
      <Stack
        sx={{
          width: '60%',
          height: '479px',
          backgroundImage: 'url("icons/empty-background.svg")',
          backgroundRepeat: 'no-repeat',
          backgroundSize: '',
          backgroundPosition: 'center',
          backgroundPositionX: '',
          flexShrink: 0,
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'column',
            alignItems: 'center',
            flex: '1 0 0',
            gap: '32px',
            paddingTop: '2vh',
          }}
        >
          {/* text box */}
          <Stack
            sx={{
              flexDirection: 'column',
              alignItems: 'center',
              gap: '20px',
              paddingTop: '15vh',
              alignSelf: 'stretch',
            }}
          >
            <Stack
              sx={{
                width: '56px',
                height: '56px',
                borderRadius: '12px',
                justifyContent: 'center',
                alignItems: 'center',
                border: '1px solid #EAECF0',
              }}
            >
              <Image
                src={'icons/credit-card-02.svg'}
                alt="search"
                width={28}
                height={28}
              />
            </Stack>
            <Stack
              sx={{
                justifyContent: 'center',
                alignItems: 'center',
                gap: '20px',
                width: '60%',
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  textAlign: 'center',
                }}
              >
                Search for a Credit Card to Activate
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  textWrap: 'wrap',
                  textAlign: 'center',
                }}
              >
                Enter the customer’s mobile number and last 4 PAN digits to find
                and activate their credit card.
              </Typography>
            </Stack>
          </Stack>
        </Stack>
      </Stack>
    </Stack>
  )
}
