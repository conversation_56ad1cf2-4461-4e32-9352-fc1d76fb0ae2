import { describe, it, expect, afterEach, vi } from 'vitest'
import { render, screen, cleanup } from '@testing-library/react'

vi.mock('@mui/material/Stack', () => ({
  default: ({ children }: { children: React.ReactNode }) => {
    return <div data-test-id="mock-stack">{children}</div>
  },
}))
import DTBMobilePage from '@/app/dtb-mobile/page'
describe('DTBMobile page', () => {
  //  vi.mock('@mui/material/Stack', ()=> ({
  //     default: ({children}: {children: React.ReactNode}) => {
  //       return <div data-test-id="mockStack">{children}</div>
  //     })
  //   })

  it('renders the DTBMobilePagw without issues', () => {
    // expect(() => render(<DTBMobilePage />)).not.toThrow()
    const page = render(<DTBMobilePage />)
    expect(page.getByText('DTB Mobile Requests')).toBeInTheDocument()
  })
  it('should display DTB Mobile heading', () => {
    const heading = 'DTB Mobile Requests'
    render(<DTBMobilePage />)
    expect(screen.getByText(heading)).toBeInTheDocument()
  })
  it('should render typography inside a stack', () => {
    render(<DTBMobilePage />)
    const stack = screen.getByTestId('mock-stack')
    expect(stack).toBeInTheDocument()
    expect(stack).toContainElement(screen.getByText('DTB Mobile Requests'))
  })
})
