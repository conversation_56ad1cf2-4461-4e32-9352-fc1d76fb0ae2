import { describe, it, expect, afterEach } from 'vitest'
import { render, screen, cleanup } from '@testing-library/react'
import ApprovalsPage from '@/app/approvals/page'
import { Stack, Typography } from '@mui/material'

describe('ApprovalsPage', () => {
  afterEach(() => {
    cleanup()
  })

  it('shouldRenderApprovalsPageWithoutCrashing', () => {
    expect(() => render(<ApprovalsPage />)).not.toThrow()
  })

  it('should Display Approval Requests Heading', () => {
    render(<ApprovalsPage />)
    expect(screen.getByText('Approval Requests')).toBeInTheDocument()
  })

  it('should Render Stack And Typography Components', () => {
    render(<ApprovalsPage />)
    const stack = screen.getByText('Approval Requests').parentElement
    expect(stack).toBeTruthy()
    expect(screen.getByText('Approval Requests').tagName.toLowerCase()).toBe(
      'p'
    )
  })

  it('should Handle Missing Parent Context Gracefully', () => {
    expect(() => render(<ApprovalsPage />)).not.toThrow()
  })

  it('should Ignore Unexpected Props', () => {
    // @ts-expect-error: passing unexpected prop
    expect(() => render(<ApprovalsPage unexpectedProp="test" />)).not.toThrow()
    expect(screen.getByText('Approval Requests')).toBeInTheDocument()
  })

  it('should NotRender Unintended Elements', () => {
    render(<ApprovalsPage />)
    expect(screen.queryByText('Unintended Element')).not.toBeInTheDocument()
    expect(screen.getAllByText('Approval Requests')).toHaveLength(1)
  })

  it('should Apply Default Stack Layout', () => {
    render(<ApprovalsPage />)
    const stack = screen.getByText('Approval Requests').parentElement
    expect(stack).toBeTruthy()
    // Stack renders as a div by default
    expect(stack?.tagName.toLowerCase()).toBe('div')
  })
})
