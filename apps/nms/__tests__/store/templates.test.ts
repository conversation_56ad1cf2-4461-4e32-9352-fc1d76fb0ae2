import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import {
  getAllTemplates,
  getNotificationEvents,
  createNotificationTemplate,
} from '@/store/actions/templates'
import {
  setIsLoadingNotificationEvents,
  setIsLoadingTemplates,
  setNotificationEvents,
  setNotificationTemplates,
  setTemplatesPagination,
} from '@/store/reducers'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi2 } from '@dtbx/store/utils'
import type { ICreateNotificationTemplate } from '@/store/interfaces'

vi.mock('@dtbx/store/utils', () => ({
  secureapi2: {
    get: vi.fn(),
    post: vi.fn(),
  },
}))
vi.mock('@/store/reducers', () => ({
  setIsLoadingNotificationEvents: vi.fn(),
  setIsLoadingTemplates: vi.fn(),
  setNotificationEvents: vi.fn(),
  setNotificationTemplates: vi.fn(),
  setTemplatesPagination: vi.fn(),
}))
vi.mock('@dtbx/store/reducers', () => ({
  setNotification: vi.fn(),
}))

describe('templates actions', () => {
  let dispatch: ReturnType<typeof vi.fn>

  beforeEach(() => {
    dispatch = vi.fn()
    vi.clearAllMocks()
  })

  it('should fetch and store notification templates on success', async () => {
    const paginationOptions = 'page=1&size=10'
    const apiResponse = {
      data: {
        data: {
          data: [{ id: 1, name: 'Template1' }],
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        },
      },
    }
    ;(secureapi2.get as any).mockResolvedValue(apiResponse)

    await getAllTemplates(dispatch, paginationOptions)

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingTemplates(true))
    expect(secureapi2.get).toHaveBeenCalledWith(
      `notifications/templates?${paginationOptions}`
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingTemplates(false))
    expect(dispatch).toHaveBeenCalledWith(
      //@ts-ignore
      setNotificationTemplates(apiResponse.data.data.data)
    )
    expect(dispatch).toHaveBeenCalledWith(
      setTemplatesPagination({
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      })
    )
  })

  it('should fetch and store notification events on success', async () => {
    const apiResponse = { data: [{ id: 'event1', name: 'Event 1' }] }
    ;(secureapi2.get as any).mockResolvedValue(apiResponse)

    await getNotificationEvents(dispatch)

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(true))
    expect(secureapi2.get).toHaveBeenCalledWith('notifications/events')
    expect(dispatch).toHaveBeenCalledWith(
      //@ts-ignore
      setNotificationEvents(apiResponse.data)
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(false))
  })

  it('should create notification template and notify success', async () => {
    const payload: ICreateNotificationTemplate = {
      templateName: 'Test',
      smsTemplateSubject: 'SMS Subject',
      emailTemplateSubject: 'Email Subject',
      smsTemplateContent: 'SMS Content',
      emailTemplateContent: 'Email Content',
      templateDescription: 'Desc',
      eventId: 'event1',
      htmlContent: 'email',
      placeholders: ['{name}'],
    }
    const apiResponse = { data: { message: 'Created successfully' } }
    ;(secureapi2.post as any).mockResolvedValue(apiResponse)

    await createNotificationTemplate(payload, dispatch)

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(true))
    expect(secureapi2.post).toHaveBeenCalled()
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Created successfully', type: 'success' })
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(false))
  })

  it('should handle error when fetching templates', async () => {
    const error = new Error('API failed')
    ;(secureapi2.get as any).mockRejectedValue(error)

    await getAllTemplates(dispatch, 'page=1')

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingTemplates(true))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({ message: 'API failed', type: 'error' })
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingTemplates(false))
  })

  it('should handle error when fetching notification events', async () => {
    const error = new Error('Events fetch failed')
    ;(secureapi2.get as any).mockRejectedValue(error)

    await getNotificationEvents(dispatch)

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(true))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Events fetch failed', type: 'error' })
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(false))
  })

  it('should handle error when creating notification template', async () => {
    const payload: ICreateNotificationTemplate = {
      templateName: 'Test',
      smsTemplateSubject: 'SMS Subject',
      emailTemplateSubject: 'Email Subject',
      smsTemplateContent: 'SMS Content',
      emailTemplateContent: 'Email Content',
      templateDescription: 'Desc',
      eventId: 'event1',
      htmlContent: 'email',
      placeholders: ['{name}'],
    }
    const error = new Error('Create failed')
    ;(secureapi2.post as any).mockRejectedValue(error)

    await createNotificationTemplate(payload, dispatch)

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(true))
    expect(dispatch).toHaveBeenCalledWith(
      setNotification({ message: 'Create failed', type: 'error' })
    )
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingNotificationEvents(false))
  })
})
