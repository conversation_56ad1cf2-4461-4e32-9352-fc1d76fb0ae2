'use client'

import { configureStore } from '@reduxjs/toolkit'
import { persistReducer } from 'redux-persist'
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux'

import rootReducer from './reducers/store'
import storage from 'redux-persist/lib/storage'

export const persistConfig = {
  key: 'backoffice',
  storage,
  blacklist: ['notification', 'overlays', 'chargeConfiguration'],
}

export const persistedReducer = persistReducer(persistConfig, rootReducer)

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
export default store
