'use client'

import React, { useState } from 'react'
import { Form, FormikProvider, useFormik } from 'formik'
import { Stack } from '@mui/material'
import * as Yup from 'yup'
import { HasAccessToRights } from '@dtbx/store/utils'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { useAppDispatch } from '@/store'
import { matchIsValidTel } from 'mui-tel-input'
import { CreateBrokerForm } from './CreateBrokerForm'
import { GetStarted } from './GetStarted'
import { createBroker } from '@/store/actions'
import { SelectExistingOrganization } from './FromExistingOrganization'

export type Stage =
  | 'GetStarted'
  | 'FromExistingOrganization'
  | 'CreateBrokerForm'

function CreateBrokerPage() {
  const [currentStage, setCurrentStage] = useState<string>('GetStarted')
  const dispatch = useAppDispatch()
  const router = useCustomRouter()

  const brokerValidation = Yup.object({
    mobile: Yup.string()
      .required('Phone must not be empty')
      .test('is-valid-phone', 'Invalid phone number', (value) => {
        return matchIsValidTel(value || '')
      }),
    name: Yup.string()
      .matches(/^[A-Za-z\s]+$/, 'Only alphabets are allowed')
      .required('Name must not be empty'),
    email: Yup.string()
      .email('Invalid email')
      .required('Email must not be empty'),
    bankName: Yup.string().required('Bank Name must not be empty'),
    bankAccountNumber: Yup.string().required(
      'Bank Account Number must not be empty'
    ),
    bankCode: Yup.string().required('Bank Code must not be empty'),
    swiftCode: Yup.string().required('Swift Code must not be empty'),
    branchCode: Yup.string().required('Branch Code must not be empty'),
    accountBranchName: Yup.string().required(
      'Account Branch Name must not be empty'
    ),
    physicalAddress: Yup.object().shape({
      country: Yup.string().required('Country is required'),
      town: Yup.string().required('Town is required'),
      physicalAddress: Yup.string().required(
        'Physical Address must not be empty'
      ),
    }),
    productIds: Yup.array()
      .of(Yup.string().required('Product is required'))
      .min(1, 'At least one product must be selected'),
    callBackUrl: Yup.string()
      .url('Value must be a valid URL (e.g., https://example.com)')
      .required('Callback URL must not be empty'),
    status: Yup.string().oneOf(['Active', 'Inactive']),
  })

  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      mobile: '',
      bankName: '',
      bankCode: '',
      swiftCode: '',
      bankAccountNumber: '',
      branchCode: '',
      accountBranchName: '',
      callBackUrl: '',
      physicalAddress: {
        country: '',
        town: '',
        physicalAddress: '',
      },
      productIds: [''],
      status: 'Active',
    },
    validationSchema: brokerValidation,
    onSubmit: async (values) => {
      if (HasAccessToRights(['SUPER_CREATE_BROKER'])) {
        await createBroker(dispatch, values, 'super')
        router.push('/settings/brokers/')
      } else {
        await createBroker(dispatch, values, 'make')
        router.push('/settings/brokers/')
      }
    },
  })

  const handleStageChange = (stage: string) => {
    setCurrentStage(stage)
  }
  const { handleSubmit } = formik

  return (
    <Stack>
      <FormikProvider value={formik}>
        <Form onSubmit={handleSubmit} noValidate>
          {(() => {
            switch (currentStage) {
              case 'GetStarted':
                return <GetStarted setCurrentStage={handleStageChange} />
              case 'FromExistingOrganization':
                return (
                  <SelectExistingOrganization
                    setCurrentStage={handleStageChange}
                    formik={formik}
                  />
                )
              case 'CreateBrokerForm':
                return (
                  <CreateBrokerForm
                    setCurrentStage={handleStageChange}
                    formik={formik}
                  />
                )
              default:
                return <GetStarted setCurrentStage={handleStageChange} />
            }
          })()}
        </Form>
      </FormikProvider>
    </Stack>
  )
}

export default CreateBrokerPage
