import React from 'react'
import { I<PERSON><PERSON>utton, Stack, Typography } from '@mui/material'
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined'
import { Broker, ExistingBroker } from '@dtbx/ui/icons'

import BrokerOption from './BrokerOption'
import { Stage } from './page'

const brokerOptions = [
  {
    imageSrc: <Broker />,
    title: 'Create a new broker',
    description: 'Create a new broker by entering all necessary details.',
    stage: 'CreateBrokerForm',
  },
  {
    imageSrc: <ExistingBroker />,
    title: 'Create a broker using existing organisation details',
    description:
      "The broker's information will be auto-filled using the selected organization's data.",
    stage: 'FromExistingOrganization',
  },
]

export const GetStarted = ({
  setCurrentStage,
}: {
  setCurrentStage: (stage: string) => void
}) => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignItems: 'center',
        gap: '10px',
        paddingTop: '10%',
        height: '100vh',
        position: 'relative',
      }}
    >
      <Stack direction="row" position="absolute" top="20px" left="16px" mb={2}>
        <IconButton
          sx={{
            background: '#FFFFFF',
            borderRadius: '8px',
            border: '1px solid #D0D5DD',
            width: '3vw',
            boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          }}
          onClick={() => window.history.back()}
        >
          <ArrowBackIosNewOutlinedIcon />
        </IconButton>
      </Stack>
      <Stack textAlign="center" mb={4}>
        <Typography variant="h4" fontWeight="700" color=" #000A12">
          Get started
        </Typography>
        <Typography variant="body1" fontWeight="400" color="#2A3339" mt={1}>
          Select the kind of broker you&apos;d like to create.
        </Typography>
      </Stack>
      <Stack display="flex" flexDirection="column" gap={2}>
        {brokerOptions.map((option, index) => (
          <BrokerOption
            key={index}
            imageSrc={option.imageSrc}
            title={option.title}
            description={option.description}
            onClick={() => setCurrentStage(option.stage)}
          />
        ))}
      </Stack>
    </Stack>
  )
}
