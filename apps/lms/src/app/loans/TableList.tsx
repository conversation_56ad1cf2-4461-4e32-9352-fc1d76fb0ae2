import {
  Box,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { ArrowForwardIos } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { ILoanProduct, ILoanRequest } from '@/store/interfaces'
import { getLoanRequests } from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { setSelectedRequest } from '@/store/reducers'
import { formatCurrency } from '@dtbx/store/utils'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import {
  CustomLoanFailedChip,
  CustomLoanSuccessChip,
  CustomStatusChip,
} from '@dtbx/ui/components/Chip'
import EmptyPage from '../reports/Tables/EmptyPage'

type Order = 'asc' | 'desc'
const header = [
  { id: 'requestId', label: 'Request ID', alignRight: false },
  { id: 'status', label: 'Status', alignRight: false },
  { id: 'loanAmount', label: 'Loan Amount', alignRight: false },
  { id: 'tenure', label: 'Tenure', alignRight: false },
  { id: 'policy', label: 'Insurance Policy', alignRight: false },
  { id: 'customerId', label: 'Customer ID', alignRight: false },
  { id: 'cancelled', label: 'Cancelled', alignRight: false },
  { id: '', label: 'Actions', alignRight: false },
]
export const LoanRequestsTableList = ({
  product,
}: {
  product: ILoanProduct
}) => {
  const dispatch = useAppDispatch()

  const { loanRequests, pageNumber, pageSize, totalPageCount, listFilters } =
    useAppSelector((state) => state.loans)
  const [selected, setSelected] = useState<string[]>([])
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const [paginationOptions, setPaginationOptions] = useState({
    page: pageNumber,
    size: pageSize,
    totalPages: totalPageCount,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    await getLoanRequests(
      dispatch,
      `productId=${product.id}&page=${newOptions.page}&size=${newOptions.size}${listFilters.status !== '' ? `&status=${listFilters.status}` : ''}`
    )
  }
  const ipfLoans = loanRequests
  // useAppSelector((state) => state.loans)
  /*************************end pagination handlers**************************/

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = ipfLoans.map((n: { id: string }) => n.id)
      setSelected(newSelected)
      return
    }
    setSelected([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }
  const router = useCustomRouter()
  const handleViewRequest = (row: ILoanRequest) => {
    dispatch(setSelectedRequest(row))
    // if (!row.status) return
    router.push(`/loans/request`)
  }
  if (totalPageCount === 0 || ipfLoans.length === 0) {
    return <EmptyPage />
  }
  return (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="loans table" size="small">
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={header}
            showCheckbox={true}
            rowCount={10}
            numSelected={selected.length}
            onRequestSort={handleRequestSort}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {ipfLoans &&
              ipfLoans.map((row) => {
                const isItemSelected = selected.indexOf(row.id) !== -1
                return (
                  <TableRow
                    hover
                    key={row.id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) => handleSelectOne(event, row.id)}
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': row.id,
                        }}
                      />
                    </TableCell>
                    <TableCell
                      sx={{
                        padding: '10px 24px 10px 16px',
                        display: 'flex',
                        gap: '12px',
                      }}
                    >
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#000A12',
                          }}
                        >
                          {row.requestReference}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 400,
                          }}
                        >
                          {product.name}
                          {/*{row.requestReference}*/}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {row.status &&
                      row.status.toLowerCase().includes('failed') ? (
                        <CustomLoanFailedChip
                          label={sentenceCase(row.status)}
                        />
                      ) : row.status &&
                        !row.status.toLowerCase().includes('failed') ? (
                        <CustomLoanSuccessChip
                          label={sentenceCase(row.status)}
                        />
                      ) : (
                        'N/A'
                      )}
                    </TableCell>

                    <TableCell>
                      <Typography variant="body2">
                        {formatCurrency(row.loanRequest.loanAmount)}
                      </Typography>
                    </TableCell>
                    <TableCell>{row.loanRequest.loanTenure} Months</TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#000A12',
                          }}
                        >
                          Purpose: {row.loanRequest.loanPurpose}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 400,
                          }}
                        >
                          Provider Product Ref:{' '}
                          {row.loanRequest.providerItemReference}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Stack direction={'column'}>
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#000A12',
                          }}
                        >
                          {row.customer?.firstName} {row.customer?.lastName}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 400,
                          }}
                        >
                          ID: {row.customer?.idDocumentNumber}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      {row.cancelled ? (
                        <CustomStatusChip label={'Yes'} color="error" />
                      ) : (
                        <CustomStatusChip label={'No'} />
                      )}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        disabled={!row.status}
                        onClick={() => {
                          handleViewRequest(row)
                        }}
                      >
                        <ArrowForwardIos />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
      {totalPageCount > 0 && (
        <CustomPagination
          options={{ ...paginationOptions, totalPages: totalPageCount }}
          handlePagination={handlePagination}
        />
      )}
    </>
  )
}
