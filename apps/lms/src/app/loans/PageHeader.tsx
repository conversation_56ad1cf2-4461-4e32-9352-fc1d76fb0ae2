'use client'
import {
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  FormControl,
  InputAdornment,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import FilterListIcon from '@mui/icons-material/FilterList'
import FilterListOffIcon from '@mui/icons-material/FilterListOff'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import { getLoanRequests, getOptinRequests } from '@/store/actions'
import { useAppDispatch, useAppSelector } from '@/store'
import { setCurrentListType, setListFilters } from '@/store/reducers'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'
import { LoanRequestsTableList } from '@/app/loans/TableList'
import { ILoanProduct } from '@/store/interfaces'
import { OptinRequestsTableList } from '@/app/loans/OptinTableList'
import { CloseRounded } from '@mui/icons-material'
import { EmptyListPlaceHolder } from '@/app/loans/EmptyPlaceHolder'
import { LoadingListsSkeleton } from '@dtbx/ui/components'

const StatusFilters = [
  {
    value: 'New',
    label: 'New',
  },
  {
    value: 'FailedPrevalidation',
    label: 'Failed Pre-validation',
  },
  {
    value: 'OngoingKYC',
    label: 'Ongoing KYC',
  },
  {
    value: 'FailedKYC',
    label: 'Failed KYC',
  },
  {
    value: 'PassedKYC',
    label: 'Passed KYC',
  },
  {
    value: 'OngoingChecks',
    label: 'Ongoing Checks',
  },
  {
    value: 'FailedChecks',
    label: 'Failed Checks',
  },
  {
    value: 'PassedChecks',
    label: 'Passed Checks',
  },
  {
    value: 'AwaitingAcceptance',
    label: 'Awaiting Acceptance',
  },
  {
    value: 'FailedAcceptance',
    label: 'Failed Acceptance',
  },
  {
    value: 'AwaitingPrepayment',
    label: 'Awaiting Prepayment',
  },
  {
    value: 'InsurerLimitExceeded',
    label: 'Insurer Limit Exceeded',
  },
  {
    value: 'PendingActivation',
    label: 'Pending Activation',
  },
  {
    value: 'Processed',
    label: 'Processed',
  },
  {
    value: 'Cancelled',
    label: 'Cancelled',
  },
]
export const LoansPageHeader = ({
  title,
  title2,
  requestCount,
  optinRequestCount,
  productId,
  currentProduct,
}: {
  title: string
  title2?: string
  requestCount: number
  optinRequestCount?: number
  productId: string
  currentProduct: ILoanProduct
}) => {
  const dispatch = useAppDispatch()
  const { listFilters, currentListType } = useAppSelector(
    (state) => state.loans
  )
  const [openFilter, setOpenFilter] = useState(false)
  const [searchValue, setSearchValue] = useState<string>('')
  const [selectedStatus, setSelectedStatus] = useState<string>(
    listFilters?.status
  )
  const [value, setValue] = useState(currentListType)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
    dispatch(setCurrentListType(newValue))
  }
  const handleLoanRequestsStatusChange = async (value: string) => {
    setSelectedStatus(value)
    dispatch(setListFilters({ status: value }))
    await getLoanRequests(
      dispatch,
      `productId=${productId}&status=${value}&page=0&size=10`
    )
  }
  const handleOptinRequestsStatusChange = async (value: string) => {
    setSelectedStatus(value)
    dispatch(setListFilters({ status: value }))
    await getOptinRequests(
      dispatch,
      `productId=${productId}&status=${value}&page=0&size=10`
    )
  }
  const handleClearOptin = async () => {
    setSelectedStatus('')
    dispatch(setListFilters({ status: '' }))
    await getOptinRequests(dispatch, `productId=${productId}&page=0&size=10`)
  }
  const handleClearLoan = async () => {
    setSelectedStatus('')
    dispatch(setListFilters({ status: '' }))
    await getLoanRequests(dispatch, `productId=${productId}&page=0&size=10`)
  }
  const handleSearch = async (
    event: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>
  ) => {
    const value = event.target.value
    setSearchValue(value)
    const params = `productId=${productId}&customerIdDocumentNumber=${value}&page=0&size=10`
    value.length >= 8 && (await getLoanRequests(dispatch, params))
  }
  useEffect(() => {
    listFilters.status !== '' && setOpenFilter(true)
  }, [listFilters])

  const { isLoadingLoans, isLoadingOptinRequests, loansCount, optinCount } =
    useAppSelector((state) => state.loans)
  return (
    <Stack
      sx={{
        justifyContent: 'space-between',
        flexDirection: 'column',
        marginBottom: '10px',
        padding: '1%',
      }}
    >
      <AntTabs
        sx={{
          marginLeft: '1%',
        }}
        onChange={handleChange}
        value={value}
      >
        <AntTab
          label={
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 700,
                fontSize: '18px',
                color: '#101828',
              }}
            >
              {title}{' '}
              <Chip
                label={`${requestCount} Requests`}
                sx={{
                  backgroundColor: '#F9DBAF',
                  border: '1px solid #B93815',
                  color: '#B42318',
                }}
              />
            </Typography>
          }
        />
        <AntTab
          label={
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: 700,
                fontSize: '18px',
                color: '#101828',
              }}
            >
              {title2}{' '}
              <Chip
                label={`${optinRequestCount} Requests`}
                sx={{
                  backgroundColor: '#F9DBAF',
                  border: '1px solid #B93815',
                  color: '#B42318',
                }}
              />
            </Typography>
          }
        />
      </AntTabs>
      <Divider />
      <TabPanel value={value} index={0}>
        {isLoadingLoans ? (
          <LoadingListsSkeleton />
        ) : !isLoadingLoans && loansCount < 1 && listFilters?.status === '' ? (
          <EmptyListPlaceHolder />
        ) : (
          <Stack gap={'3vh'}>
            <Stack direction="column">
              <Stack
                direction={'row'}
                justifyContent={'space-between'}
                sx={{ marginTop: '1vh' }}
              >
                <Stack>
                  <Typography variant={'subtitle3'}>
                    Showing {requestCount} requests
                  </Typography>
                </Stack>
                <Button
                  variant={'outlined'}
                  sx={{
                    border: openFilter
                      ? '1px solid #000A12'
                      : '1px solid #D0D5DD',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  startIcon={
                    openFilter ? <FilterListOffIcon /> : <FilterListIcon />
                  }
                  onClick={() => setOpenFilter(!openFilter)}
                >
                  {openFilter ? 'Hide' : 'Show'} Filters
                </Button>
              </Stack>
              {openFilter && (
                <Stack direction={'row'} mt={'2%'} gap={'2%'}>
                  <FormControl
                    sx={{
                      width: '30%',
                    }}
                    variant={'outlined'}
                  >
                    <InputLabel
                      sx={{
                        background: '#FFFFFF',
                      }}
                    >
                      Status
                    </InputLabel>
                    <Select
                      size={'small'}
                      value={selectedStatus}
                      onChange={(e) =>
                        handleLoanRequestsStatusChange(e.target.value)
                      }
                    >
                      {StatusFilters.map((status) => (
                        <MenuItem key={status.value} value={status.value}>
                          {status.label}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <CustomSearchInput
                    value={searchValue}
                    onChange={handleSearch}
                    placeholder="Search By Customer ID"
                    startAdornment={
                      <InputAdornment position="start">
                        <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
                      </InputAdornment>
                    }
                  />
                  <Button
                    sx={{
                      minWidth: '131px',
                      height: '40px',
                      gap: '0px',
                      boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                    }}
                    onClick={handleClearLoan}
                    endIcon={
                      <Typography
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                        }}
                      >
                        <CloseRounded />
                      </Typography>
                    }
                  >
                    <Typography>Clear</Typography>
                  </Button>
                </Stack>
              )}
            </Stack>
            <LoanRequestsTableList product={currentProduct} />
          </Stack>
        )}
      </TabPanel>
      <TabPanel value={value} index={1}>
        {isLoadingOptinRequests ? (
          <LoadingListsSkeleton />
        ) : !isLoadingOptinRequests &&
          optinCount < 1 &&
          listFilters?.status === '' ? (
          <EmptyListPlaceHolder />
        ) : (
          <Stack direction="column" mb={'3vh'}>
            <Stack
              direction={'row'}
              justifyContent={'space-between'}
              sx={{ marginTop: '1vh' }}
            >
              <Stack>
                <Typography variant={'subtitle3'}>
                  Showing {optinRequestCount} Opt in Requests
                </Typography>
              </Stack>
              <Button
                variant={'outlined'}
                sx={{
                  border: openFilter
                    ? '1px solid #000A12'
                    : '1px solid #D0D5DD',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
                startIcon={
                  openFilter ? <FilterListOffIcon /> : <FilterListIcon />
                }
                onClick={() => setOpenFilter(!openFilter)}
              >
                {openFilter ? 'Hide' : 'Show'} Filters
              </Button>
            </Stack>
            {openFilter && (
              <Stack direction={'row'} mt={'2%'} gap={'2%'}>
                <FormControl
                  sx={{
                    width: '30%',
                  }}
                  variant={'outlined'}
                >
                  <InputLabel>Status</InputLabel>
                  <Select
                    size={'small'}
                    value={selectedStatus}
                    onChange={(e) =>
                      handleOptinRequestsStatusChange(e.target.value)
                    }
                  >
                    {StatusFilters.map((status) => (
                      <MenuItem key={status.value} value={status.value}>
                        {status.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <CustomSearchInput
                  value={searchValue}
                  onChange={handleSearch}
                  placeholder="Search By Customer ID"
                  startAdornment={
                    <InputAdornment position="start">
                      <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
                    </InputAdornment>
                  }
                />
                <Button
                  sx={{
                    minWidth: '131px',
                    height: '40px',
                    gap: '0px',
                    boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  }}
                  onClick={handleClearOptin}
                  endIcon={
                    <Typography
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px',
                      }}
                    >
                      <CloseRounded />
                    </Typography>
                  }
                >
                  <Typography>Clear</Typography>
                </Button>
              </Stack>
            )}
            <OptinRequestsTableList product={currentProduct} />
          </Stack>
        )}
      </TabPanel>
    </Stack>
  )
}
