import { <PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import ArrowForwardIcon from '@mui/icons-material/ArrowForward'
import React, { useState } from 'react'
import { LMSCheckerRequestsApiHandler } from '@/app/CheckerRequestsApiHandler'
import { useAppDispatch, useAppSelector } from '@/store'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { ReasonsDialog } from '@dtbx/ui/components/Dialogs'
import { AccessControlWrapper } from '@dtbx/store/utils'
import { completeCancelLoanRequest } from '@/store/actions'
import { Dispatch } from '@reduxjs/toolkit'
import { ILoanOptinRequest } from '@/store/interfaces'

export const OptinRequestCheckerRejectApproveView = ({
  request,
}: {
  request: ILoanOptinRequest
}) => {
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const dispatch = useAppDispatch()
  const router = useCustomRouter()
  const [openReject, setOpenReject] = useState<boolean>(false)
  const handleApprove = async () => {
    await completeCancelLoanRequest({
      requestId: request.id,
      approvalId: selectedApprovalRequest.id,
      comments: 'approved',
      dispatch,
      type: 'accept',
    })
  }
  const handleReject = async (reasons: string) => {
    await completeCancelLoanRequest({
      requestId: request.id,
      approvalId: selectedApprovalRequest.id,
      comments: reasons,
      dispatch,
      type: 'reject',
    })
  }
  const isCancelReq =
    selectedApprovalRequest?.diff[0]?.field === 'cancelled' &&
    selectedApprovalRequest?.diff[0]?.newValue === 'true'
  return (
    <>
      <Stack
        direction="row"
        sx={{
          display: !isCancelReq ? 'none' : 'flex',
        }}
      >
        <AccessControlWrapper
          rights={selectedApprovalRequest?.makerCheckerType?.checkerPermissions?.filter(
            (perm) => perm.includes('REJECT')
          )}
          makerId={selectedApprovalRequest.maker}
        >
          <Button
            variant="contained"
            sx={{
              background: '#D92D20',
              height: '34px',
              '&:hover': {
                background: '#D92D20',
              },
            }}
            onClick={() => setOpenReject(!openReject)}
          >
            Reject Cancellation
            <CloseIcon />
          </Button>
        </AccessControlWrapper>
        <AccessControlWrapper
          rights={selectedApprovalRequest?.makerCheckerType?.checkerPermissions?.filter(
            (perm) => perm.includes('ACCEPT')
          )}
          makerId={selectedApprovalRequest.maker}
        >
          <Button
            variant="contained"
            sx={{
              height: '34px',
              marginLeft: '10px',
            }}
            onClick={handleApprove}
          >
            Approve Request
            <ArrowForwardIcon />
          </Button>
        </AccessControlWrapper>
      </Stack>
      <ReasonsDialog
        open={openReject}
        setOpen={setOpenReject}
        onClick={handleReject}
        title={'Reject Cancellation'}
        buttonText={'Save changes'}
        isLoading={false}
        buttonProps={{
          color: '#EB0045',
        }}
      />
    </>
  )
}
