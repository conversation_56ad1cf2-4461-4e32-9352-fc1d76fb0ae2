import React from 'react'
import { ICustomerCheck, ICustomerDocument } from '@/store/interfaces'
import { useAppSelector } from '@/store'

import { OptinKYCValidationCard } from './KYCCards'
import {
  NationalIDCheck,
  PIPPEPStatus,
  USACitizenship,
} from '../../loans/optinRequest/KYCStages'
import { OptinCreditValidationCard } from '@/app/loans/optinRequest/CreditCards'

export const CheckerView = ({
  kycData,
  requestId,
  customerDocuments,
}: {
  kycData: ICustomerCheck[]
  requestId: string
  customerDocuments: ICustomerDocument[]
}) => {
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  const { selectedOptinRequest, kycTypeValidations, creditTypeValidations } =
    useAppSelector((state) => state.loans)
  return (
    <>
      {kycData.find(
        (data) =>
          data.code === 'ID_DOCUMENT_FRONT_OCR' ||
          data.code === 'ID_DOCUMENT_BACK_OCR'
      ) && (
        <NationalIDCheck
          data={kycData.filter(
            (data) =>
              data.code === 'ID_DOCUMENT_FRONT_OCR' ||
              data.code === 'ID_DOCUMENT_BACK_OCR'
          )}
          requestId={requestId}
          documents={customerDocuments}
          selectedApprovalRequest={selectedApprovalRequest}
          isChecker={true}
          request={selectedOptinRequest}
        />
      )}
      {kycData
        .filter((check) =>
          kycTypeValidations.find(
            (type) =>
              check.code === type.code &&
              type.type === 'KYC' &&
              type.code !== 'ID_DOCUMENT_FRONT_OCR' &&
              type.code !== 'ID_DOCUMENT_BACK_OCR' &&
              type.code !== 'US_CITIZEN_CHECK' &&
              type.code !== 'PEP_PIP_VALIDATION' &&
              check.status !== 'NotApplicable'
          )
        )
        .map((check) => (
          <OptinKYCValidationCard
            key={check.code}
            check={check}
            isChecker={true}
            request={selectedOptinRequest}
            selectedApprovalRequest={selectedApprovalRequest}
          />
        ))}

      {kycData.find(
        (data) =>
          data.code === 'US_CITIZEN_CHECK' && data.status !== 'NotApplicable'
      ) && (
        <USACitizenship
          data={kycData.find((data) => data.code === 'US_CITIZEN_CHECK')}
          requestId={requestId}
          request={selectedOptinRequest}
          isChecker={true}
        />
      )}
      {kycData.find(
        (data) =>
          data.code === 'US_CITIZEN_CHECK' && data.status !== 'NotApplicable'
      ) && (
        <PIPPEPStatus
          data={kycData.find((data) => data.code === 'PEP_PIP_VALIDATION')}
          requestId={requestId}
          request={selectedOptinRequest}
          isChecker={true}
        />
      )}
      {kycData
        .filter((check) =>
          creditTypeValidations.find(
            (type) =>
              check.code === type.code &&
              type.type === 'CREDIT' &&
              check.status !== 'NotApplicable'
          )
        )
        .map((check) => (
          <OptinCreditValidationCard
            key={check.code}
            check={check}
            isChecker={true}
            request={selectedOptinRequest}
          />
        ))}
    </>
  )
}
