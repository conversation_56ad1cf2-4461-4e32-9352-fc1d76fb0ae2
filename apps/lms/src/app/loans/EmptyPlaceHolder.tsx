import { Stack, Typography } from '@mui/material'
import { EmptyFolder } from '@dtbx/ui/icons'

export const EmptyListPlaceHolder = () => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignItems: 'center',
        alignContent: 'center',
        gap: '10px',
        paddingTop: '10%',
        height: '80vh',
        border: '1px solid #EAECF0',
        borderRadius: '4px',
        boxShadow:
          '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
      }}
    >
      <Typography variant={'h6'}>Loan Requests</Typography>
      <EmptyFolder />
      <Typography
        sx={{
          fontWeight: 700,
        }}
      >
        There are no records to show.
      </Typography>
    </Stack>
  )
}

