'use client'
import { Paper, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAppDispatch, useAppSelector } from '@/store'
import { IApprovalRequest, ILoanProduct } from '@/store/interfaces'
import {
  getLoanProducts,
  getLoanRequests,
  getOptinRequests,
} from '@/store/actions'
import {
  setCurrentLoanProduct,
  setIsLoadingOptinRequests,
  setIsViewingRequest,
  setListFilters,
  setSelectedApprovalRequest,
} from '@/store/reducers'
import { LoadingListsSkeleton } from '@dtbx/ui/components/Loading'

import { EmptyListPlaceHolder } from '../loans/EmptyPlaceHolder'
import { LoansPageHeader } from '../loans/PageHeader'

const LoansPage = () => {
  const dispatch = useAppDispatch()
  const searchParams = useSearchParams()
  const productId = searchParams.get('productId') || ''
  const { loanProducts } = useAppSelector((state) => state.loans)
  const [currentProduct, setCurrentProduct] = useState<ILoanProduct>(
    {} as ILoanProduct
  )
  useEffect(() => {
    dispatch(setSelectedApprovalRequest({} as IApprovalRequest))
    getLoanProducts(dispatch, 'page=1&size=30')
  }, [])
  useEffect(() => {
    if (productId) {
      const prd =
        loanProducts.find((product) => product.id === productId) ||
        ({} as ILoanProduct)
      setCurrentProduct(prd)
      dispatch(setCurrentLoanProduct(prd))
    }
  }, [productId])
  const { loansCount, optinCount } = useAppSelector((state) => state.loans)
  useEffect(() => {
    productId &&
      getLoanRequests(dispatch, `productId=${productId}&page=0&size=10`)
    getOptinRequests(dispatch, `productId=${productId}&page=0&size=10`)
    dispatch(setIsViewingRequest(false))
    dispatch(setListFilters({ status: '' }))
  }, [productId])
  return (
    <Paper
      elevation={0}
      sx={{
        px: '2%',
      }}
    >
      <Stack>
        <Typography
          variant="h6"
          sx={{
            py: '0.5%',
          }}
        >
          {currentProduct?.name || 'Loans Product'}
        </Typography>
      </Stack>
      <Stack
        sx={{
          borderRadius: '12px',
          border: '1px solid #EAECF0',
          background: '#FFFFFF',
          boxShadow:
            '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
        }}
      >
        <LoansPageHeader
          title="Loan Requests"
          title2="Optin Requests"
          requestCount={loansCount}
          optinRequestCount={optinCount}
          productId={productId}
          currentProduct={currentProduct}
        />
      </Stack>
    </Paper>
  )
}
export default LoansPage
