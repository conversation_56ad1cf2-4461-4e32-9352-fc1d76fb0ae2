import { Stack, Typography } from '@mui/material'
import { EmptyFolder } from '@dtbx/ui/icons'

//components/SvgIcons/EmptyFolder
export const EmptyLoanProfile = ({
  customerName,
  status,
}: {
  customerName: string
  status: string
}) => {
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        alignItems: 'center',
        alignContent: 'center',
        gap: '10px',
        py: '5%',
      }}
    >
      <Typography variant={'h6'}>Loan Activity</Typography>
      <EmptyFolder />
      <Typography
        sx={{
          fontWeight: 700,
        }}
      >
        There are no records to show.
      </Typography>
      <Typography>
        {customerName}{' '}
        {status.toLowerCase().includes('failed')
          ? `has ${status}`
          : `is ${status}`}
      </Typography>
    </Stack>
  )
}
