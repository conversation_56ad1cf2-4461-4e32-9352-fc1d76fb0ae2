'use client'

import {
  Arrow<PERSON><PERSON><PERSON>IosOutlined,
  ChatBubbleOutlineOutlined,
  Close,
  QueryBuilder,
} from '@mui/icons-material'
import {
  Autocomplete,
  Button,
  DialogActions,
  DialogTitle,
  Drawer,
  FormControl,
  IconButton,
  Skeleton,
  Stack,
  styled,
  TextField,
  Typography,
} from '@mui/material'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import {
  completeValidationOverride,
  fetchPepPipCategories,
  initiateOverrideValidationCheck,
  rerunCheck,
  superOverrideValidationCheck,
  updateCustomerProfile,
  uploadDocument,
} from '@/store/actions'
import {
  IApprovalRequest,
  ICustomerCheck,
  ICustomerDocument,
  ILoanOptinRequest,
  ILoanRequest,
} from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  extractFields,
  formatTimestamp,
  getBase64,
  HasAccessToRights,
} from '@dtbx/store/utils'
import { LoadingButton } from '@dtbx/ui/components/Loading'
import { CustomDialog } from '@dtbx/ui/components/Dialogs'
import { UploadDocumentForm } from '@dtbx/ui/components/Input'

const StyledButton = styled(Button)({
  padding: 0,
  fontWeight: 500,
})
const StyledTitle = styled(Typography)({
  color: '#101828',
  fontWeight: 500,
})
interface IKYCCheckProps {
  data: ICustomerCheck | undefined
  comments?: string
  requestId: string
  request?: ILoanRequest
  isChecker?: boolean
  selectedApprovalRequest?: IApprovalRequest
}
interface INationalIDCheckProps {
  documents: ICustomerDocument[]
  data: ICustomerCheck[]
  comments?: string
  requestId: string
  isChecker?: boolean
  selectedApprovalRequest?: IApprovalRequest
  request?: ILoanRequest
}
export const NationalIDCheck = ({
  data,
  requestId,
  documents,
  selectedApprovalRequest,
  isChecker,
  request,
}: INationalIDCheckProps) => {
  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const dispatch = useAppDispatch()
  const [idFront, setIdFront] = useState<ICustomerDocument | undefined>()
  const [idBack, setIdBack] = useState<ICustomerDocument | undefined>()
  const handleRecheck = async () => {
    // await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    data.map(async (rec) => {
      if (rec.status === 'Failed') {
        const overrideData = {
          id: rec.id || '',
          status: 'Passed',
          comments: comments || '',
        }
        if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
          await superOverrideValidationCheck(
            overrideData,
            requestId,
            rec.id || '',
            dispatch
          )
        } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
          await initiateOverrideValidationCheck(
            overrideData,
            requestId,
            rec.id || '',
            dispatch
          )
        }
      }
    })
  }

  useEffect(() => {
    setIdBack(documents.find((doc) => doc.type === 'ID_BACK'))
    setIdFront(documents.find((doc) => doc.type === 'ID_FRONT'))
  }, [documents])
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${!!data?.find((rec) => rec.status === 'Failed') ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">National Id</StyledTitle>
        <Stack direction="row" gap="10px">
          {!!data?.find((rec) => rec.status === 'Failed') &&
            !request?.cancelled &&
            !isChecker && (
              <OverrideValidationDialog
                title="National ID check Override"
                description={'Override'}
                handleOverride={handleOverride}
              />
            )}
          {!!data?.find((rec) => rec.status === 'Failed') &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'National ID OCR'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'National ID OCR'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.find(
              (rec) => rec.status === 'Failed' || rec.status === 'Pending'
            ) && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
              >
                Check Again
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Stack
        sx={{
          flexDirection: 'row',
          gap: '4%',
        }}
      >
        <Stack sx={{ width: '48%' }}>
          <Typography variant="subtitle2" color="text.primary">
            Front
          </Typography>
          {idFront ? (
            <img
              src={idFront.file}
              alt={' ID Front'}
              style={{
                borderRadius: '8px',
                width: '100%',
                maxHeight: '30vh',
              }}
            />
          ) : (
            <Skeleton
              variant="rectangular"
              sx={{
                borderRadius: '8px',
              }}
              width="100%"
              height="15vh"
              animation={false}
            />
          )}
        </Stack>
        <Stack sx={{ width: '48%' }}>
          <Typography variant="subtitle2" color="text.primary">
            Back
          </Typography>
          {idBack ? (
            <img
              src={idBack.file}
              alt={' ID Front'}
              style={{
                borderRadius: '8px',
                width: '100%',
                maxHeight: '30vh',
              }}
            />
          ) : (
            <Skeleton
              variant="rectangular"
              sx={{
                borderRadius: '8px',
              }}
              width="100%"
              height="15vh"
              animation={false}
            />
          )}
        </Stack>
      </Stack>
      {data?.map((rec) => {
        if (rec.code === 'ID_DOCUMENT_FRONT_OCR') {
          return (
            <>
              <Typography
                key={rec.code}
                variant="subtitle2"
              >{`Front Message: ${rec.message}`}</Typography>
              {rec.status === 'Failed' ? (
                <Typography
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                  color="error.main"
                >
                  <InfoOutlinedIcon />
                  Failed Front ID OCR check
                </Typography>
              ) : rec.status === 'Pending' ? (
                <Typography
                  color={'warning.main'}
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                >
                  Pending Front ID OCR check
                  <QueryBuilder color="warning" />
                </Typography>
              ) : (
                <Typography
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                >
                  Passed Front ID OCR check
                  <CheckCircleIcon color="success" />
                </Typography>
              )}
            </>
          )
        } else if (rec.code === 'ID_DOCUMENT_BACK_OCR') {
          return (
            <>
              <Typography
                key={rec.code}
                variant="subtitle2"
              >{`Back Message: ${rec.message}`}</Typography>
              {rec.status === 'Failed' ? (
                <Typography
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                  color="error.main"
                >
                  <InfoOutlinedIcon />
                  Failed Back ID OCR check
                </Typography>
              ) : rec.status === 'Pending' ? (
                <Typography
                  color={'warning.main'}
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                >
                  Pending Back ID OCR check
                  <QueryBuilder color="warning" />
                </Typography>
              ) : (
                <Typography
                  sx={{
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                    alignItems: 'center',
                  }}
                >
                  Passed Back ID OCR check
                  <CheckCircleIcon color="success" />
                </Typography>
              )}
            </>
          )
        }
      })}
    </Stack>
  )
}

export const IPRSCheck = ({
  data,
  requestId,
  selectedApprovalRequest,
  isChecker,
  request,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()
  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    data?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">IPRS Status</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title="IPRS Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'IPRS Check'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'IPRS Check'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed IPRS Check
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending IPRS Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed IPRS Check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const WorldCheck = ({
  data,
  requestId,
  isChecker,
  selectedApprovalRequest,
  request,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    data?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">WorldCheck Status</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title="World Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'World Check'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'World Check'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      {selectedApprovalRequest ? (
        <Stack>
          <Typography variant="body2">
            {formatTimestamp(selectedApprovalRequest.dateCreated)}:
          </Typography>
          <Typography variant="subtitle2" color="primary.main">
            {selectedApprovalRequest.maker} sent a request to override{' '}
            <span style={{ fontWeight: 600 }}>world check</span> status with
            comments
          </Typography>
          <Typography variant="subtitle2">
            {selectedApprovalRequest.makerComments}
          </Typography>
        </Stack>
      ) : (
        <Typography variant="subtitle2">{data?.message}</Typography>
      )}

      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' && !isChecker ? (
          <Typography
            color={'error.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <InfoOutlinedIcon />
            Failed World Check
          </Typography>
        ) : isChecker ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Override Pending Approval
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <Typography
            color={'success.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Passed World Check
            <CheckCircleIcon color="success" />
          </Typography>
        )}
      </Typography>
    </Stack>
  )
}

export const KRAPin = ({
  data,
  requestId,
  request,
  isChecker,
  selectedApprovalRequest,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    data?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">KRA Pin</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !isChecker && (
            <OverrideValidationDialog
              title="KRA Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'KRA Check'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'KRA Check'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed KRA Pin Check
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending KRA Pin Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed KRA Pin Check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const CRBDelinquency = ({ data, requestId }: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">CRB Check</StyledTitle>
        {isCheckRerunLoading ? (
          <LoadingButton width={'15%'} />
        ) : (
          data?.status === 'Failed' && (
            <StyledButton
              variant="text"
              disabled={approvalActions}
              onClick={handleRecheck}
            >
              Check Again
            </StyledButton>
          )
        )}
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed CRB Delinquency Check
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending CRB Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed CRB Delinquency Check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const IdDocumentFrontOCR = ({ data, requestId }: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">National ID {'(Front)'}</StyledTitle>
        {isCheckRerunLoading ? (
          <LoadingButton width={'15%'} />
        ) : (
          data?.status === 'Failed' && (
            <StyledButton
              variant="text"
              disabled={approvalActions}
              onClick={handleRecheck}
            >
              Check Again
            </StyledButton>
          )
        )}
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed ID Front side OCR
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending ID Front Side OCR Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed ID Front side OCR
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const IdDocumentBackOCR = ({ data, requestId }: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">National ID {'(Back)'}</StyledTitle>
        {isCheckRerunLoading ? (
          <LoadingButton width={'15%'} />
        ) : (
          data?.status === 'Failed' && (
            <StyledButton
              variant="text"
              disabled={approvalActions}
              onClick={handleRecheck}
            >
              Check Again
            </StyledButton>
          )
        )}
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed ID Back side OCR
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending ID Back Side OCR Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed ID Back side OCR
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const SelfieMatch = ({
  data,
  requestId,
  request,
  selectedApprovalRequest,
  isChecker,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    data?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">Selfie</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title="Selfie Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'Selfie Check'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'Selfie Check'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color:
            data?.status === 'Failed'
              ? 'error.main'
              : data?.status === 'Pending'
                ? 'warning.main'
                : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed Selfie match
          </>
        ) : data?.status === 'Pending' ? (
          <>
            Pending Selfie match Check
            <QueryBuilder />
          </>
        ) : (
          <>
            Passed Selfie match
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const CustomerRiskCheck = ({
  data,
  requestId,
  request,
  selectedApprovalRequest,
  isChecker,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    data?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            requestId,
            data.id,
            dispatch
          ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">Customer Risk Rating</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title="Customer Risk Rating Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'Customer Risk Rating'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'Customer Risk Rating'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed customer risk Rating check
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending customer risk Rating check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed customer risk Rating check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}

export const USACitizenship = ({
  data,
  requestId,
  selectedApprovalRequest,
  request,
  isChecker,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading, customerDocuments } = useAppSelector(
    (state) => state.loans
  )
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }
    if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
      await superOverrideValidationCheck(
        overrideData,
        requestId,
        data?.id || '',
        dispatch
      )
    } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
      await initiateOverrideValidationCheck(
        overrideData,
        requestId,
        data?.id || '',
        dispatch
      )
    }
  }
  const handleUpload = async (file: File | null) => {
    if (file) {
      const result = await getBase64(file)
      const data = {
        document: result as string,
        documentType: 'W9_FORM',
        comments: 'W9 Form Upload',
      }
      if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
        await uploadDocument(data, dispatch, requestId, 'super')
      } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
        await uploadDocument(data, dispatch, requestId, 'normal')
      }
    }
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">US Citizen Status</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title="US Citizen Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'US Citizen Check'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'US Citizen Check'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Stack>
        <StyledTitle variant="subtitle2">W9 Form</StyledTitle>
        <UploadDocumentForm
          onFileUpload={handleUpload}
          initialFile={
            customerDocuments.find((doc) => doc.type === 'W9_FORM')?.file ||
            undefined
          }
          name={'W9 Form'}
        />
      </Stack>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed USA Citizen Check
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending USA Citizen Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed USA Citizen Check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}
export const PIPPEPStatus = ({
  data,
  requestId,
  request,
  selectedApprovalRequest,
  isChecker,
}: IKYCCheckProps) => {
  const dispatch = useAppDispatch()
  const { isCheckRerunLoading, customerDocuments, customerProfile } =
    useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const [categoryId, setCategoryId] = useState('')
  const [categories, setCategories] = useState<ICategory[]>([])
  interface ICategory {
    id: string
    categoryName: string
  }

  useEffect(() => {
    const loadPipPepData = async () => {
      const res = await fetchPepPipCategories()
      setCategories(res)
    }
    loadPipPepData()
  }, [])
  const handleCategoryChange = async (newValue: string) => {
    setCategoryId(newValue)
    const data = {
      pepPipRemarks: customerProfile.pepPipRemarks ?? '',
      pipPepCategoryId: newValue,
      pipPepDeclarationStatus: customerProfile.pepPipDeclarationStatus,
      sourceOfFunds: customerProfile.sourceOfFunds.value,
      sourceOfWealth: customerProfile.sourceOfWealth.value,
      occupation: customerProfile.occupation.description,
      comments: 'updating customer pep/pip category',
    }
    if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
      await updateCustomerProfile(
        data,
        request?.customer.id || '',
        dispatch,
        'super'
      )
    } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
      await updateCustomerProfile(
        data,
        request?.customer.id || '',
        dispatch,
        'make'
      )
    }
  }

  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: data?.id || '',
      status: 'Passed',
      comments: comments || '',
    }
    if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
      await superOverrideValidationCheck(
        overrideData,
        requestId,
        data?.id || '',
        dispatch
      )
    } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
      await initiateOverrideValidationCheck(
        overrideData,
        requestId,
        data?.id || '',
        dispatch
      )
    }
  }
  const handleUpload = async (file: File | null) => {
    if (file) {
      const result = await getBase64(file)
      const data = {
        document: result as string,
        documentType: 'ENHANCED_DUE_DILIGENCE',
        comments: 'EDD Form Upload',
      }
      if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
        await uploadDocument(data, dispatch, requestId, 'super')
      } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
        await uploadDocument(data, dispatch, requestId, 'normal')
      }
    }
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">PEP/PIP Status</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {data?.status === 'Failed' && !isChecker && !request?.cancelled && (
            <OverrideValidationDialog
              title="PEP/PIP Status Check Override"
              description={data.message}
              handleOverride={handleOverride}
            />
          )}
          {data?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={'PEP/PIP Status'}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={'PEP/PIP Status'}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            data?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled || approvalActions}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <StyledTitle variant="subtitle2">Category</StyledTitle>
      <FormControl fullWidth margin={'normal'}>
        <FormControl fullWidth margin="normal">
          <Autocomplete
            options={categories}
            getOptionLabel={(option) => option.categoryName}
            value={
              categories.find((category) => category.id === categoryId) || null
            }
            onChange={(event, newValue) =>
              handleCategoryChange(newValue?.id || '')
            }
            isOptionEqualToValue={(option, value) => option.id === value.id}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Category"
                variant="outlined"
              />
            )}
          />
        </FormControl>
      </FormControl>
      <Stack>
        <StyledTitle variant="subtitle2">EDD Form</StyledTitle>
        <UploadDocumentForm
          onFileUpload={handleUpload}
          initialFile={
            customerDocuments.find(
              (doc) => doc.type === 'ENHANCED_DUE_DILIGENCE'
            )?.file || undefined
          }
          name={'EDD Form'}
        />
      </Stack>
      <Typography
        variant="subtitle3"
        sx={{
          color: data?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed PEP/PIP Check
          </>
        ) : data?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending PEP/PIP Check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed PEP/PIP Check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}
export const TenantApprovalCheck = ({ data, requestId }: IKYCCheckProps) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleRecheck = async () => {
    await rerunCheck(requestId, { checkId: data?.id || '' }, dispatch)
  }
  return (
    <Stack
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${data?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">Tenant Approval</StyledTitle>
        {isCheckRerunLoading ? (
          <LoadingButton width={'15%'} />
        ) : (
          data?.status === 'Failed' && (
            <StyledButton
              variant="text"
              disabled={approvalActions}
              onClick={handleRecheck}
            >
              Check Again
            </StyledButton>
          )
        )}
      </Stack>
      <Typography variant="subtitle2">{data?.message}</Typography>
      <Typography
        variant="subtitle3"
        sx={{
          color:
            data?.status === 'Failed'
              ? 'error.main'
              : data?.status === 'Pending'
                ? 'warning.main'
                : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {data?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed tenant approval check
          </>
        ) : data?.status === 'Pending' ? (
          <>
            Pending tenant approval check
            <QueryBuilder />
          </>
        ) : (
          <>
            Passed tenant approval check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}
interface IOverrideProps {
  title: string
  description: string
  handleOverride: (comments: string) => void
}
export const OverrideValidationDialog = ({
  title,
  description,
  handleOverride,
}: IOverrideProps) => {
  const [open, setOpen] = useState<boolean>(false)
  const [comments, setComments] = useState<string>('')
  const [error, setError] = useState<boolean>(false)
  const { isLoadingOverrideValidationCheck } = useAppSelector(
    (state) => state.loans
  )
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleClose = (
    event: React.MouseEvent<HTMLElement> | null,
    reason: string
  ) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleSaveChanges = () => {
    if (comments === '') {
      setError(true)
      return
    }
    handleOverride(comments)
    handleClose(null, 'close')
  }
  return (
    <>
      <StyledButton
        variant="text"
        disabled={approvalActions}
        color="error"
        onClick={() => setOpen(!open)}
      >
        Override
      </StyledButton>
      <CustomDialog open={open} maxWidth="xs" fullWidth>
        <DialogTitle
          sx={{
            py: '1%',
            px: '3%',
          }}
        >
          <Typography variant="subtitle2" color="text.primary">
            {title}
          </Typography>
        </DialogTitle>
        <Stack
          sx={{
            flexDirection: 'column',
            px: '3%',
            gap: '0.5vh',
          }}
        >
          <Typography variant="subtitle3">
            Reason for failure: {description}. <br />
            Leave a reason for override below:
          </Typography>
          <TextField
            multiline
            minRows={3}
            label="Comments"
            error={error}
            onChange={(e) => {
              setComments(e.target.value)
              e.target.value.length < 1 ? setError(true) : setError(false)
            }}
          />
          <Typography variant="label1">
            Once you click save changes, your updates will be submitted to your
            manager for verification.
          </Typography>
          <Typography
            variant="subtitle3"
            sx={{
              color: 'error.main',
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <InfoOutlinedIcon />
            Failed {title}
          </Typography>
        </Stack>
        <DialogActions>
          <Button
            variant="contained"
            sx={{
              backgroundColor: '#D92D20 !important',
              width: '40%',
            }}
            onClick={(e) => handleClose(e, 'close')}
          >
            Cancel
          </Button>
          {isLoadingOverrideValidationCheck ? (
            <LoadingButton width="40%" />
          ) : (
            <Button
              variant="contained"
              endIcon={<ArrowForwardIosOutlined />}
              sx={{
                width: '40%',
                textWrap: 'nowrap',
                gap: 0,
              }}
              onClick={handleSaveChanges}
            >
              Save Changes
            </Button>
          )}
        </DialogActions>
      </CustomDialog>
    </>
  )
}

interface IOverrideApprovalProps {
  title: string
  handleOverride: (comments: string) => void
  approvalRequest: IApprovalRequest
  loanRequest: ILoanRequest
  handleReject: (comments: string) => void
}
export const ApproveChangesDrawer = ({
  title,
  handleOverride,
  approvalRequest,
  loanRequest,
  handleReject,
}: IOverrideApprovalProps) => {
  const [open, setOpen] = useState<boolean>(false)
  const { isLoadingOverrideValidationCheck } = useAppSelector(
    (state) => state.loans
  )
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleClose = (
    event: React.MouseEvent<HTMLElement> | null,
    reason: string
  ) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  return (
    <>
      <Button
        variant="text"
        sx={{
          color: '#1570EF',
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          alignContent: 'center',
          fontSize: '14px',
          fontWeight: 500,
          padding: 0,
        }}
        disabled={approvalActions}
        onClick={() => setOpen(!open)}
      >
        <ChatBubbleOutlineOutlined
          sx={{
            fontSize: '20px',
            marginTop: '5px',
          }}
        />
        Comment
      </Button>
      <Drawer
        open={open}
        anchor={'right'}
        sx={{
          '.MuiDrawer-paper': {
            width: '25%',
          },
        }}
      >
        <Stack
          sx={{
            justifyContent: 'space-between',
            alignItems: 'center',
            flexDirection: 'row',
            px: '1vw',
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
          }}
        >
          <Typography
            variant="subtitle2"
            color="text.primary"
            sx={{
              fontWeight: 600,
            }}
          >
            Activity log
          </Typography>
          <IconButton
            onClick={() => handleClose(null, 'close')}
            sx={{
              border: '1px solid #CBD5E1',
              my: '4px',
            }}
            size="small"
          >
            <Close />
          </IconButton>
        </Stack>
        <Stack
          sx={{
            px: '1vw',
            py: '1vh',
            gap: '1vh',
          }}
        >
          <Typography
            variant="subtitle2"
            color="text.primary"
            sx={{
              fontSize: '15px',
              fontWeight: 600,
            }}
          >
            {formatTimestamp(approvalRequest.dateCreated)}:
          </Typography>
          <Typography variant="subtitle2" color="text.primary">
            {approvalRequest.maker} sent a request to change{' '}
            {`${sentenceCase(loanRequest.customer.firstName)}'s`}{' '}
            <span
              style={{
                fontWeight: 600,
              }}
            >
              {title}
            </span>{' '}
            status to{' '}
            <span
              style={{
                color: '#17B26A',
              }}
            >
              {`${extractFields('status', approvalRequest)}`}
            </span>
          </Typography>
          <Typography>Comments</Typography>
          <Stack
            sx={{
              flexDirection: 'column',
              px: '3%',
              gap: '0.5vh',
              border: '1px solid #D0D5DD',
              borderRadius: '6px',
              padding: '3%',
            }}
          >
            <Typography variant="subtitle3" color="text.primary">
              {approvalRequest.maker} left a comment:
            </Typography>
            <Typography
              variant="subtitle3"
              color="text.primary"
              sx={{
                fontWeight: 400,
              }}
            >
              {approvalRequest.makerComments}
            </Typography>
            <Stack
              sx={{
                flexDirection: 'row',
                gap: '5%',
                pt: '1vh',
              }}
            >
              <RejectOverride
                title={title}
                handleReject={handleReject}
                fromActivityLog={true}
              />
              {isLoadingOverrideValidationCheck ? (
                <LoadingButton width="40%" />
              ) : (
                <Button
                  variant="contained"
                  fullWidth
                  endIcon={<ArrowForwardIosOutlined />}
                  sx={{
                    textWrap: 'nowrap',
                    gap: 0,
                  }}
                  onClick={() => handleOverride('Approved')}
                >
                  Approve Changes
                </Button>
              )}
            </Stack>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
interface IRejectOverrideProps {
  title: string
  handleReject: (comments: string) => void
  fromActivityLog?: boolean
}

export const RejectOverride = ({
  title,
  handleReject,
  fromActivityLog,
}: IRejectOverrideProps) => {
  const [open, setOpen] = useState<boolean>(false)
  const [comments, setComments] = useState<string>('')
  const [error, setError] = useState<boolean>(false)
  const { isLoadingOverrideValidationCheck } = useAppSelector(
    (state) => state.loans
  )
  const { approvalActions } = useAppSelector((state) => state.approvalRequests)
  const handleClose = (
    event: React.MouseEvent<HTMLElement> | null,
    reason: string
  ) => {
    if (reason === 'backdropClick') {
      return false
    }
    setOpen(false)
  }
  const handleSaveChanges = () => {
    if (comments === '') {
      setError(true)
      return
    }
    handleReject(comments)
    handleClose(null, 'close')
  }
  return (
    <>
      {fromActivityLog ? (
        <Button
          variant="contained"
          fullWidth
          sx={{
            backgroundColor: '#D92D20 !important',
            gap: 0,
          }}
          disabled={approvalActions}
          endIcon={<Close />}
          onClick={() => setOpen(!open)}
        >
          Reject
        </Button>
      ) : (
        <StyledButton
          variant="text"
          color="error"
          disabled={approvalActions}
          onClick={() => setOpen(!open)}
        >
          Reject
        </StyledButton>
      )}
      <CustomDialog open={open} maxWidth="xs" fullWidth>
        <DialogTitle
          sx={{
            py: '1%',
            px: '3%',
          }}
        >
          <Typography variant="subtitle2" color="text.primary">
            {title}
          </Typography>
        </DialogTitle>
        <Stack
          sx={{
            flexDirection: 'column',
            px: '3%',
            gap: '0.5vh',
          }}
        >
          <Typography variant="subtitle3">
            Leave a reason for rejecting the override below:
          </Typography>
          <TextField
            multiline
            minRows={3}
            placeholder="Leave a comment"
            sx={{
              backgroundColor: '#F2F4F7',
              borderRadius: '6px',
            }}
            error={error}
            onChange={(e) => {
              setComments(e.target.value)
              e.target.value.length < 1 ? setError(true) : setError(false)
            }}
          />
          <Typography variant="label1">
            Once you click save changes, your updates will be saved.
          </Typography>
          <Typography
            variant="subtitle3"
            sx={{
              color: 'error.main',
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            <InfoOutlinedIcon />
            Failed {title}
          </Typography>
        </Stack>
        <DialogActions>
          <Button
            variant="contained"
            sx={{
              backgroundColor: '#D92D20 !important',
              width: '40%',
            }}
            onClick={(e) => handleClose(e, 'close')}
          >
            Cancel
          </Button>
          {isLoadingOverrideValidationCheck ? (
            <LoadingButton width="40%" />
          ) : (
            <Button
              variant="contained"
              endIcon={<ArrowForwardIosOutlined />}
              sx={{
                width: '40%',
                textWrap: 'nowrap',
                gap: 0,
              }}
              onClick={handleSaveChanges}
            >
              Save Changes
            </Button>
          )}
        </DialogActions>
      </CustomDialog>
    </>
  )
}
