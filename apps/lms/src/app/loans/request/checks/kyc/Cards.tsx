'use client'
import { QueryBuilder } from '@mui/icons-material'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import { Button, Stack, styled, Typography } from '@mui/material'
import React from 'react'
import {
  IApprovalRequest,
  ICustomerCheck,
  ILoanRequest,
} from '@/store/interfaces'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  completeValidationOverride,
  initiateOverrideValidationCheck,
  rerunCheck,
  superOverrideValidationCheck,
} from '@/store/actions'
import { HasAccessToRights } from '@dtbx/store/utils'
import { LoadingButton } from '@dtbx/ui/components/Loading'

import {
  ApproveChangesDrawer,
  OverrideValidationDialog,
  RejectOverride,
} from '@/app/loans/request/KYCStages'

const StyledTitle = styled(Typography)({
  color: '#101828',
  fontWeight: 500,
})
const StyledButton = styled(Button)({
  padding: 0,
  fontWeight: 500,
})
export const KYCValidationCard = ({
  key,
  check,
  request,
  isChecker,
  selectedApprovalRequest,
}: {
  key: string
  check: ICustomerCheck
  request: ILoanRequest
  isChecker: boolean
  selectedApprovalRequest?: IApprovalRequest
}) => {
  const dispatch = useAppDispatch()

  const { isCheckRerunLoading } = useAppSelector((state) => state.loans)
  const handleRecheck = async () => {
    await rerunCheck(request.id, { checkId: check?.id || '' }, dispatch)
  }
  const handleRejectOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'reject', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleApproveOverride = async (comments: string) => {
    const overrideData = {
      comments: comments || '',
    }
    selectedApprovalRequest &&
      (await completeValidationOverride(
        overrideData,
        'approve', //reject or approve
        selectedApprovalRequest.id,
        dispatch
      ))
  }
  const handleOverride = async (comments: string) => {
    const overrideData = {
      id: check?.id || '',
      status: 'Passed',
      comments: comments || '',
    }

    check?.id &&
      (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])
        ? await superOverrideValidationCheck(
            overrideData,
            request.id,
            check.id,
            dispatch
          )
        : await initiateOverrideValidationCheck(
            overrideData,
            request.id,
            check.id,
            dispatch
          ))
  }
  return (
    <Stack
      key={key}
      sx={{
        flexDirection: 'column',
        gap: '5px',
        borderRadius: '4px',
        border: `1px solid ${check?.status === 'Failed' ? '#FDA29B' : '#D0D5DD'}`,
        padding: '2%',
      }}
    >
      <Stack
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
      >
        <StyledTitle variant="subtitle2">{check.code}</StyledTitle>
        <Stack
          sx={{
            flexDirection: 'row',
            gap: '10px',
          }}
        >
          {check?.status === 'Failed' && !request?.cancelled && !isChecker && (
            <OverrideValidationDialog
              title={`${check.code} Check Failed`}
              description={check.message}
              handleOverride={handleOverride}
            />
          )}
          {check?.status === 'Failed' &&
            isChecker &&
            selectedApprovalRequest && (
              <Stack
                sx={{
                  justifyContent: 'flex-end',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                <RejectOverride
                  title={check.code}
                  handleReject={handleRejectOverride}
                />
                <ApproveChangesDrawer
                  handleOverride={handleApproveOverride}
                  handleReject={handleRejectOverride}
                  loanRequest={request as ILoanRequest}
                  approvalRequest={selectedApprovalRequest}
                  title={check.code}
                />
              </Stack>
            )}
          {isCheckRerunLoading ? (
            <LoadingButton width={'15%'} />
          ) : (
            check?.status === 'Failed' &&
            !isChecker && (
              <StyledButton
                variant="text"
                disabled={request?.cancelled}
                onClick={handleRecheck}
                sx={{}}
              >
                {'Check Again'}
              </StyledButton>
            )
          )}
        </Stack>
      </Stack>
      <Typography variant="subtitle2">{check?.message}</Typography>
      {
        check.code === 'WORLD_CHECK_INDIVIDUAL' &&
        check.providerReference &&
        <Typography variant="subtitle2">Case ID: {check.providerReference}</Typography>
      }
      <Typography
        variant="subtitle3"
        sx={{
          color: check?.status === 'Failed' ? 'error.main' : 'success.main',
          display: 'flex',
          flexDirection: 'row',
          gap: '10px',
          alignItems: 'center',
        }}
      >
        {check?.status === 'Failed' ? (
          <>
            <InfoOutlinedIcon />
            Failed {check.code} check
          </>
        ) : check?.status === 'Pending' ? (
          <Typography
            color={'warning.main'}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              gap: '10px',
              alignItems: 'center',
            }}
          >
            Pending {check.code} check
            <QueryBuilder color="warning" />
          </Typography>
        ) : (
          <>
            Passed {check.code} check
            <CheckCircleIcon color="success" />
          </>
        )}
      </Typography>
    </Stack>
  )
}
