'use client'

import { Skeleton, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { ICustomerDocument } from '@/store/interfaces'
import {
  fetchDropdownData,
  getLoanCustomerProfile,
  updateCustomerProfile,
} from '@/store/actions'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import { CheckerView } from '@/app/loans/request/CheckerView'
import { MakerView } from '@/app/loans/request/MakerView'
import { LMSCheckerRequestsApiHandler } from '@/app/CheckerRequestsApiHandler'
import { HasAccessToRights } from '@dtbx/store/utils'
import { useCustomRouter } from '@dtbx/ui/hooks'
import { setOpenDialog } from '@/store/reducers'
import { ReasonsDialog } from '@dtbx/ui/components/Dialogs'
import { CustomAccordionWithDropdown } from '@dtbx/ui/components'

const CustomTypography = ({
  title,
  value,
}: {
  title: string
  value: string
}) => {
  return (
    <Stack>
      <Typography variant="subtitle2">{title}</Typography>
      <Typography
        variant="subtitle2"
        sx={{
          color: '#101828',
          fontWeight: 600,
        }}
      >
        {value}
      </Typography>
    </Stack>
  )
}

const CustomerProfilePage = () => {
  const {
    selectedRequest,
    customerProfile,
    customerDocuments,
    customerChecks,
    isCheckerViewProfileOpen,
    isLoadingCustomerProfile,
  } = useAppSelector((state) => state.loans)
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const { openDialog } = useAppSelector((state) => state.approvalRequests)
  const [passportPhoto, setPassportPhoto] = useState<
    ICustomerDocument | undefined
  >()

  const handleUpdateProfile = async (
    newValue: {
      id: number
      name: string
    },
    selectedOption: string | number
  ) => {
    let data
    if (selectedOption === 'sourceOfWealth') {
      data = { sourceOfWealth: newValue.name }
    } else if (selectedOption === 'sourceOfFunds') {
      data = { sourceOfFunds: newValue.name }
    } else if (selectedOption === 'occupation') {
      data = { occupation: newValue.name }
    }

    if (data) {
      if (HasAccessToRights(['SUPER_UPDATE_CUSTOMER_KYC'])) {
        await updateCustomerProfile(
          data,
          selectedRequest.customer?.id,
          dispatch,
          'super'
        )
        await getLoanCustomerProfile(
          dispatch,
          selectedRequest.customer?.id,
          selectedRequest.id
        )
      } else if (HasAccessToRights(['MAKE_UPDATE_CUSTOMER_KYC'])) {
        await updateCustomerProfile(
          data,
          selectedRequest.customer?.id,
          dispatch,
          'make'
        )
        await getLoanCustomerProfile(
          dispatch,
          selectedRequest.customer?.id,
          selectedRequest.id
        )
      }
    }
  }

  type Category = {
    id: string
    label: string
    options: { id: number; name: string; description?: string }[]
  }

  const [categories, setCategories] = useState<Category[]>([])
  const { selectedApprovalRequest } = useAppSelector(
    (state) => state.approvalRequests
  )
  useEffect(() => {
    setPassportPhoto(
      customerDocuments.find((doc) => doc.type === 'PASSPORT_PHOTO')
    )
    const loadDropDownData = async () => {
      const wealthData = await fetchDropdownData(
        'lms/customers/source-of-wealth'
      )
      const fundsData = await fetchDropdownData('lms/customers/source-of-funds')
      const occupationData = await fetchDropdownData(
        'lms/customers/occupations'
      )

      setCategories([
        {
          id: 'sourceOfWealth',
          label: 'Source of Wealth',
          options: wealthData,
        },
        { id: 'sourceOfFunds', label: 'Source of Funds', options: fundsData },
        { id: 'occupation', label: 'Occupation', options: occupationData },
      ])
    }

    loadDropDownData()
  }, [customerDocuments])
  useEffect(() => {
    getLoanCustomerProfile(
      dispatch,
      selectedRequest.customer?.id,
      selectedRequest.id
    )
  }, [selectedRequest])

  const rejectCancelRequest = async (reasons: string) => {
    if (selectedApprovalRequest && selectedApprovalRequest.id) {
      await LMSCheckerRequestsApiHandler(
        selectedApprovalRequest,
        dispatch,
        router,
        `REJECT_${selectedApprovalRequest.makerCheckerType.type}`,
        reasons
      )
    }
  }

  const setOpen = (val: boolean) => {
    dispatch(setOpenDialog(val))
  }
  return (
    <Stack direction="column">
      <Stack
        sx={{
          flexDirection: 'row',
          background: '#F7F7F7',
          px: '2%',
          py: '1%',
        }}
      >
        <ReasonsDialog
          open={openDialog}
          setOpen={setOpen}
          onClick={rejectCancelRequest}
          title={'Reject Cancellation'}
          buttonText={'Save changes'}
          isLoading={false}
          buttonProps={{
            color: '#EB0045',
          }}
        />
        {isLoadingCustomerProfile ? (
          <Stack
            sx={{
              flexDirection: 'row',
              gap: '1vw',
              width: '100%',
              px: '3%',
            }}
          >
            <CustomSkeleton
              animation="wave"
              variant="rectangular"
              width="30%"
              height="60vh"
            />
            <CustomSkeleton
              animation="wave"
              variant="rectangular"
              width="70%"
              height="60vh"
            />
          </Stack>
        ) : (
          <Stack
            sx={{
              flexDirection: 'row',
              background: '#FFF',
              px: '3%',
              py: '2%',
              width: '100%',
              borderRadius: '12px',
            }}
          >
            <Stack
              sx={{
                width: '30%',
                flexDirection: 'column',
                gap: '20px',
              }}
            >
              <Stack
                sx={{
                  width: '100%',
                  border: '1px solid #EAECF0',
                  borderRadius: '8px',
                  padding: '1%',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                  flexDirection: 'column',
                  gap: '20px',
                }}
              >
                {passportPhoto ? (
                  <img
                    src={passportPhoto.file}
                    alt={'passport photo'}
                    style={{
                      borderRadius: '8px',
                      maxHeight: '30vh',
                    }}
                  />
                ) : (
                  <Skeleton
                    variant="rectangular"
                    sx={{
                      borderRadius: '8px',
                    }}
                    width="100%"
                    height="15vh"
                    animation={false}
                  />
                )}

                <Typography
                  variant="subtitle2"
                  sx={{
                    color: '#101828',
                    fontWeight: 700,
                  }}
                >
                  {customerProfile.firstName} {customerProfile.lastName}
                </Typography>
                <CustomTypography
                  title={'Date of Birth'}
                  value={customerProfile.dateOfBirth}
                />
                <CustomTypography
                  title={'Email'}
                  value={customerProfile.email}
                />
                <CustomTypography
                  title={'KRA Pin'}
                  value={customerProfile.kraPinNumber}
                />
                <CustomTypography
                  title={'Mobile No'}
                  value={customerProfile.mobile}
                />
                <CustomTypography
                  title={'Nationality'}
                  value={customerProfile.nationality}
                />
                <CustomTypography
                  title={'National ID No'}
                  value={customerProfile.idDocumentNumber}
                />
                <CustomTypography
                  title={'Address'}
                  value={`${customerProfile.physicalAddress?.street}, ${customerProfile.physicalAddress?.city}, ${customerProfile.physicalAddress?.country}`}
                />
                {customerProfile.branch && (
                  <CustomTypography
                    title={'Branch'}
                    value={customerProfile.branch}
                  />
                )}
                {customerProfile.cif && (
                  <CustomTypography title={'CIF'} value={customerProfile.cif} />
                )}
                {customerProfile.customerType && (
                  <CustomTypography
                    title={'Customer Type'}
                    value={customerProfile.customerType}
                  />
                )}
                {customerProfile.pepPipDeclarationStatus && (
                  <CustomTypography
                    title={'Pep Pip Declaration Status'}
                    value={customerProfile.pepPipDeclarationStatus}
                  />
                )}
                {customerProfile.pepPipRemarks && (
                  <CustomTypography
                    title={'Pep Pip Remarks'}
                    value={customerProfile.pepPipRemarks}
                  />
                )}
                {customerProfile.pipPepCategory && (
                  <CustomTypography
                    title={'Pip Pep Category'}
                    value={customerProfile.pipPepCategory}
                  />
                )}
                {customerProfile.ssn && (
                  <CustomTypography title={'SSN'} value={customerProfile.ssn} />
                )}
                <CustomTypography
                  title={'Is US Citizen'}
                  value={customerProfile.usCitizen ? 'YES' : 'NO'}
                />
                {customerProfile.sourceOfFunds?.value && (
                  <CustomTypography
                    title={'Source of Funds'}
                    value={customerProfile.sourceOfFunds?.value}
                  />
                )}
                {customerProfile.sourceOfWealth?.value && (
                  <CustomTypography
                    title={'Source of Wealth'}
                    value={customerProfile.sourceOfWealth?.value}
                  />
                )}
                {customerProfile.occupation?.description && (
                  <CustomTypography
                    title={'Occupation'}
                    value={customerProfile.occupation?.description}
                  />
                )}
              </Stack>
              <Stack
                sx={{
                  flexDirection: 'column',
                  gap: '20px',
                  // border: '2px solid red',
                  border: '1px solid #EAECF0',
                  borderRadius: '8px',
                  boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                }}
              >
                <CustomAccordionWithDropdown
                  title="Update Financial Information"
                  variant="subtitle2"
                  color="#101828"
                  fontWeight="700"
                  categories={categories}
                  onChange={handleUpdateProfile}
                />
              </Stack>
            </Stack>
            <Stack
              sx={{
                width: '70%',
                px: '2%',
                flexDirection: 'column',
                gap: '20px',
              }}
            >
              {isCheckerViewProfileOpen ? (
                <CheckerView
                  kycData={customerChecks}
                  customerDocuments={customerDocuments}
                  requestId={selectedRequest.id}
                />
              ) : (
                <MakerView
                  kycData={customerChecks}
                  customerDocuments={customerDocuments}
                  requestId={selectedRequest.id}
                  request={selectedRequest}
                />
              )}
            </Stack>
          </Stack>
        )}
      </Stack>
    </Stack>
  )
}

export default CustomerProfilePage
