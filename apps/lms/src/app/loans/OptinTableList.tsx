import {
  Box,
  IconButton,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'
import { ArrowForwardIos } from '@mui/icons-material'
import { useAppDispatch, useAppSelector } from '@/store'
import { ILoanOptinRequest, ILoanProduct } from '@/store/interfaces'
import { getOptinRequests } from '@/store/actions'
import { useCustomRouter } from '@dtbx/ui/hooks'
import {
  setIsCheckerViewProfileOpen,
  setSelectedOptinRequest,
} from '@/store/reducers'
import { formatTimestamp } from '@dtbx/store/utils'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomCheckBox } from '@dtbx/ui/components/CheckBox'
import {
  CustomLoanFailedChip,
  CustomLoanSuccessChip,
  CustomStatusChip,
} from '@dtbx/ui/components/Chip'
import EmptyPage from '../reports/Tables/EmptyPage'

type Order = 'asc' | 'desc'
const header = [
  { id: 'customer.firstName', label: 'First Name', alignRight: false },
  { id: 'customer.lastName', label: 'Last Name', alignRight: false },
  {
    id: 'customer.idDocumentNumber',
    label: 'ID/Passport Number',
    alignRight: false,
  },
  { id: 'status', label: 'Status', alignRight: false },
  { id: 'reference', label: 'Reference Number', alignRight: false },
  { id: 'dateCreated', label: 'Date Created', alignRight: false },
  { id: 'createdBy', label: 'Created By', alignRight: false },
  { id: 'cancelled', label: 'Cancelled', alignRight: false },
  { id: '', label: 'Actions', alignRight: false },
]
export const OptinRequestsTableList = ({
  product,
}: {
  product: ILoanProduct
}) => {
  const dispatch = useAppDispatch()

  const {
    optinRequests,
    optinPageNumber,
    optinPageSize,
    optinTotalPageCount,
    listFilters,
  } = useAppSelector((state) => state.loans)
  const [selected, setSelected] = useState<string[]>([])
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('id')
  const [paginationOptions, setPaginationOptions] = useState({
    page: optinPageNumber,
    size: optinPageSize,
    totalPages: optinTotalPageCount,
  })
  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions) // Update parent state
    await getOptinRequests(
      dispatch,
      `productId=${product.id}&page=${newOptions.page}&size=${newOptions.size}${listFilters.status !== '' ? `&status=${listFilters.status}` : ''}`
    )
  }
  // useAppSelector((state) => state.loans)
  /*************************end pagination handlers**************************/

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = optinRequests.map((n: { id: string }) => n.id)
      setSelected(newSelected)
      return
    }
    setSelected([])
  }
  const handleSelectOne = (event: React.MouseEvent<unknown>, id: string) => {
    const selectedIndex = selected.indexOf(id)
    let newSelected: string[] = []

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selected, id)
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selected.slice(1))
    } else if (selectedIndex === selected.length - 1) {
      newSelected = newSelected.concat(selected.slice(0, -1))
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(
        selected.slice(0, selectedIndex),
        selected.slice(selectedIndex + 1)
      )
    }
    setSelected(newSelected)
  }
  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    setOrder(isAsc ? 'desc' : 'asc')
    setOrderBy(property)
  }
  const router = useCustomRouter()
  const handleViewRequest = (row: ILoanOptinRequest) => {
    dispatch(setSelectedOptinRequest(row))
    router.push(`/loans/optinRequest`)
  }
  if (optinTotalPageCount === 0 || optinRequests.length === 0) {
    return <EmptyPage />
  }
  useEffect(() => {
    dispatch(setIsCheckerViewProfileOpen(false))
  }, [])
  return (
    <>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table
          sx={{ minWidth: 650 }}
          aria-label="optin requests table"
          size="small"
        >
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={header}
            showCheckbox={true}
            rowCount={10}
            numSelected={selected.length}
            onRequestSort={handleRequestSort}
            onSelectAllClick={handleSelectAll}
          />
          <TableBody>
            {optinRequests &&
              optinRequests.map((row) => {
                const isItemSelected = selected.indexOf(row.id) !== -1
                return (
                  <TableRow
                    hover
                    key={row.id}
                    tabIndex={-1}
                    role="checkbox"
                    onClick={(event) => handleSelectOne(event, row.id)}
                    selected={isItemSelected}
                    aria-checked={isItemSelected}
                  >
                    <TableCell padding="checkbox">
                      <CustomCheckBox
                        checked={isItemSelected}
                        inputProps={{
                          'aria-labelledby': row.id,
                        }}
                      />
                    </TableCell>
                    <TableCell
                      sx={{
                        padding: '10px 24px 10px 16px',
                        display: 'flex',
                        gap: '12px',
                      }}
                    >
                      <Box>
                        <Typography
                          variant="body2"
                          sx={{
                            color: '#000A12',
                          }}
                        >
                          {row.customer.firstName}
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 400,
                          }}
                        >
                          {product.name}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>{row.customer.lastName}</TableCell>
                    <TableCell>{row.customer.idDocumentNumber}</TableCell>
                    <TableCell>
                      {row.status.toLowerCase().includes('failed') ? (
                        <CustomLoanFailedChip
                          label={sentenceCase(row.status)}
                        />
                      ) : (
                        <CustomLoanSuccessChip
                          label={sentenceCase(row.status)}
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Stack direction="column">
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 400,
                          }}
                        >
                          {row.reference}
                        </Typography>
                      </Stack>
                    </TableCell>
                    <TableCell>{formatTimestamp(row.dateCreated)}</TableCell>
                    <TableCell>{row.createdBy}</TableCell>
                    <TableCell>
                      {row.cancelled ? (
                        <CustomStatusChip label={'Yes'} color="error" />
                      ) : (
                        <CustomStatusChip label={'No'} />
                      )}
                    </TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => {
                          handleViewRequest(row)
                        }}
                      >
                        <ArrowForwardIos />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>
      {optinTotalPageCount > 0 && (
        <CustomPagination
          options={{ ...paginationOptions, totalPages: optinTotalPageCount }}
          handlePagination={handlePagination}
        />
      )}
    </>
  )
}
