import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import React, { useEffect, useState } from 'react'
import dayjs, { Dayjs } from 'dayjs'
import { useAppDispatch, useAppSelector } from '@/store'
import { IHeadCell } from '@dtbx/store/interfaces'
import { ILoanRequestReport } from '@/store/interfaces'
import {
  generateLoanRequestReports,
  getLoanRequestReports,
} from '@/store/actions'
import { DownloadCloudIcon } from '@dtbx/ui/icons'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { CustomSkeleton } from '@dtbx/ui/components/Loading'

import EmptyPage from './EmptyPage'
import ReportsDropDownFilterWithDatePicker from '../Filters/RequestReportsFilter'
import { ReportStatusChip } from '../StatusChips'
import { formatTimestamp } from '@dtbx/store/utils'

const tableHeader: IHeadCell[] = [
  {
    label: 'Organization',
    id: 'organizationName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Product',
    id: 'productName',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Customer',
    id: 'customerID',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Date requested',
    id: 'requestedDate',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Amount',
    id: 'amount',
    alignCenter: false,
    alignRight: false,
  },
  {
    label: 'Status',
    id: 'status',
    alignCenter: false,
    alignRight: false,
  },
]

// "requestStartDate": "2024-08-06",
// "requestEndDate": "2024-08-06",

const RequestReports = () => {
  const dispatch = useAppDispatch()
  const {
    loanReportRequestResponse: data,
    isLoadingReportRequest: isLoading,
    isSuccessfulReportRequest: isSuccess,
    isLoadingGenerateRequestsReport: loading,
  } = useAppSelector((state) => state.loans)
  const [page, setPage] = React.useState<number>(1)
  const [filters, setFilters] = React.useState<{
    orgName: string
    pageSize: number
    date?: {
      startDate: Dayjs
      endDate: Dayjs
    }
    status: string
  }>({
    orgName: '',
    pageSize: 10,
    status: '',
  })
  const [paginationOptions, setPaginationOptions] = useState({
    page: page,
    size: 10,
    totalPages: 0,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getLoanRequestReports({
      dispatch,
      params: {
        ...newOptions,
      },
    })
  }
  /*************************end pagination handlers**************************/
  useEffect(() => {
    const startDate = filters.date && filters.date?.startDate
    const endDate = filters.date && filters.date?.endDate
    getLoanRequestReports({
      dispatch,
      params: {
        page: page,
        size: filters.pageSize,
        status: filters.status,
        organizationName: filters.orgName,
        requestStartDate: startDate ? startDate.format('YYYY-MM-DD') : '',
        requestEndDate: endDate ? endDate.format('YYYY-MM-DD') : '',
      },
    })
  }, [page, filters.orgName, filters.pageSize, filters.date, filters.status])
  return (
    <>
      {data.totalElements === 0 && data.data.length === 0 ? (
        <EmptyPage />
      ) : (
        <Paper
          sx={{
            height: 'auto',
            overflow: 'auto',
            borderRadius: '12px',
          }}
        >
          <Stack
            sx={{
              padding: '20px 24px 0px 24px',
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}
          >
            {/* header */}
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                  gap: '10px',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                }}
              >
                <Typography
                  sx={{
                    fontSize: '18px',
                    lineHeight: '28px',
                    fontWeight: '700',
                  }}
                >
                  Loan Request Reports
                </Typography>
                <Chip
                  sx={{
                    maxHeight: '18px',
                    backgroundColor: '#F9DBAF',
                    color: '#B42318',
                    border: '1px solid #B93815',
                  }}
                  label={`${isSuccess ? data.totalElements : 0} loan request${isSuccess && data.totalElements > 0 ? 's' : ''}`}
                />
              </Box>
              <Typography variant={'subtitle3'}>
                Showing {isLoading ? <></> : isSuccess ? data.totalElements : 0}{' '}
                loan request
                {isSuccess && data.totalElements > 1 ? 's' : ''}
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                gap: '10px',
                alignItems: 'center',
              }}
            >
              <ReportsDropDownFilterWithDatePicker
                onFilter={(value: {
                  orgName: string
                  pageSize: number
                  date?: { startDate: Dayjs; endDate: Dayjs }
                  status: string
                }) => {
                  setFilters({ ...value })
                }}
              />
              <Box
                sx={{
                  m: 1,
                  position: 'relative',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <Button
                  disabled={loading}
                  variant="contained"
                  startIcon={<DownloadCloudIcon />}
                  onClick={() => {
                    const startDate = filters.date && filters.date?.startDate
                    const endDate = filters.date && filters.date?.endDate
                    generateLoanRequestReports({
                      dispatch,
                      params: {
                        page: page,
                        size: filters.pageSize,
                        status: filters.status,
                        organizationName: filters.orgName,
                        requestStartDate: startDate
                          ? startDate.format('YYYY-MM-DD')
                          : '',
                        requestEndDate: endDate
                          ? endDate.format('YYYY-MM-DD')
                          : '',
                      },
                    })
                  }}
                >
                  Export report
                </Button>
                {loading && (
                  <CircularProgress
                    size={24}
                    sx={{
                      color: 'white',
                      position: 'absolute',
                      top: '50%',
                      left: '50%',
                      marginTop: '-12px',
                      marginLeft: '-12px',
                    }}
                  />
                )}
              </Box>
            </Box>
          </Stack>
          {isLoading ? (
            <CustomSkeleton
              variant="rectangular"
              sx={{
                width: '100%',
                height: '45vh',
                margin: '10px 2px ',
              }}
            />
          ) : (
            <TableContainer
              component={Paper}
              elevation={0}
              sx={{
                width: '100%',
                height: '50vh',
                overflowX: 'auto',
              }}
            >
              <Table stickyHeader>
                <CustomTableHeader
                  order={'desc'}
                  orderBy={''}
                  rowCount={0}
                  headLabel={tableHeader}
                  numSelected={0}
                />
                <TableBody>
                  {isSuccess &&
                    data?.data.map((row: ILoanRequestReport, index: number) => {
                      const {
                        organizationName,
                        productName,
                        productID,
                        customerID,
                        requestDate,
                        requestAmount,
                        requestStatus,
                      } = row
                      return (
                        <TableRow key={index}>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            {organizationName}
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '0px',
                            }}
                          >
                            <Box
                              sx={{
                                width: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                gap: '1px',
                              }}
                            >
                              <Typography>{productName}</Typography>
                              <Typography variant="caption">
                                ID: {productID}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            {customerID}
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            {formatTimestamp(requestDate)}
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            KES {new Intl.NumberFormat().format(requestAmount)}
                          </TableCell>
                          <TableCell
                            sx={{
                              padding: '12px 24px',
                            }}
                          >
                            <ReportStatusChip label={requestStatus} />
                          </TableCell>
                        </TableRow>
                      )
                    })}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          {data.totalNumberOfPages > 0 && (
            <CustomPagination
              options={{
                ...paginationOptions,
                totalPages: data.totalNumberOfPages,
              }}
              handlePagination={handlePagination}
            />
          )}
        </Paper>
      )}
    </>
  )
}

export default RequestReports
