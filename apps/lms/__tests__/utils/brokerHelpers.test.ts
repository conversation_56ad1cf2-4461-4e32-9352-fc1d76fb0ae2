import { describe, it, expect } from 'vitest'
import { mockCreateBrokerForm, mockBroker, mockOrganizations } from '../stubs/brokerStubs'

// Helper functions to test
const validateBrokerForm = (formData: any) => {
  const errors: any = {}
  
  if (!formData.name || formData.name.trim() === '') {
    errors.name = 'Broker name is required'
  }
  
  if (!formData.email || formData.email.trim() === '') {
    errors.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
    errors.email = 'Invalid email format'
  }
  
  if (!formData.mobile || formData.mobile.trim() === '') {
    errors.mobile = 'Mobile number is required'
  } else if (!/^\+254\d{9}$/.test(formData.mobile)) {
    errors.mobile = 'Invalid mobile number format'
  }
  
  if (!formData.bankName || formData.bankName.trim() === '') {
    errors.bankName = 'Bank name is required'
  }
  
  if (!formData.bankCode || formData.bankCode.trim() === '') {
    errors.bankCode = 'Bank code is required'
  }
  
  if (!formData.swiftCode || formData.swiftCode.trim() === '') {
    errors.swiftCode = 'Swift code is required'
  }
  
  if (!formData.bankAccountNumber || formData.bankAccountNumber.trim() === '') {
    errors.bankAccountNumber = 'Bank account number is required'
  }
  
  if (!formData.branchCode || formData.branchCode.trim() === '') {
    errors.branchCode = 'Branch code is required'
  }
  
  if (!formData.accountBranchName || formData.accountBranchName.trim() === '') {
    errors.accountBranchName = 'Account branch name is required'
  }
  
  if (!formData.callBackUrl || formData.callBackUrl.trim() === '') {
    errors.callBackUrl = 'Callback URL is required'
  } else if (!/^https?:\/\/.+/.test(formData.callBackUrl)) {
    errors.callBackUrl = 'Invalid URL format'
  }
  
  if (!formData.productIds || formData.productIds.length === 0) {
    errors.productIds = 'At least one product must be selected'
  }
  
  if (!formData.status || formData.status.trim() === '') {
    errors.status = 'Status is required'
  }
  
  // Validate physical address
  if (!formData.physicalAddress) {
    errors.physicalAddress = 'Physical address is required'
  } else {
    const addressErrors: any = {}
    
    if (!formData.physicalAddress.country || formData.physicalAddress.country.trim() === '') {
      addressErrors.country = 'Country is required'
    }
    
    if (!formData.physicalAddress.town || formData.physicalAddress.town.trim() === '') {
      addressErrors.town = 'Town is required'
    }
    
    if (!formData.physicalAddress.physicalAddress || formData.physicalAddress.physicalAddress.trim() === '') {
      addressErrors.physicalAddress = 'Physical address is required'
    }
    
    if (Object.keys(addressErrors).length > 0) {
      errors.physicalAddress = addressErrors
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

const formatBrokerData = (formData: any) => {
  return {
    ...formData,
    name: formData.name?.trim(),
    email: formData.email?.toLowerCase().trim(),
    mobile: formData.mobile?.replace(/\s/g, ''),
    bankName: formData.bankName?.trim(),
    bankCode: formData.bankCode?.trim(),
    swiftCode: formData.swiftCode?.toUpperCase().trim(),
    bankAccountNumber: formData.bankAccountNumber?.trim(),
    branchCode: formData.branchCode?.trim(),
    accountBranchName: formData.accountBranchName?.trim(),
    callBackUrl: formData.callBackUrl?.trim(),
    status: formData.status?.trim(),
    physicalAddress: {
      country: formData.physicalAddress?.country?.trim(),
      town: formData.physicalAddress?.town?.trim(),
      physicalAddress: formData.physicalAddress?.physicalAddress?.trim(),
    }
  }
}

const mapOrganizationToBrokerForm = (organization: any) => {
  return {
    name: organization.name,
    email: organization.email,
    mobile: organization.mobile,
    bankName: organization.bankName,
    bankCode: organization.bankCode,
    swiftCode: organization.swiftCode,
    bankAccountNumber: organization.bankAccountNumber,
    branchCode: organization.branchCode,
    accountBranchName: organization.accountBranchName,
    status: organization.status,
    physicalAddress: {
      country: organization.physicalAddress?.country || '',
      town: organization.physicalAddress?.town || '',
      physicalAddress: organization.physicalAddress?.physicalAddress || '',
    },
    productIds: [],
    callBackUrl: '',
  }
}

const calculateBrokerScore = (broker: any) => {
  let score = 0
  
  // Basic information completeness
  if (broker.name) score += 10
  if (broker.email) score += 10
  if (broker.mobile) score += 10
  
  // Banking information completeness
  if (broker.bankName) score += 15
  if (broker.bankCode) score += 10
  if (broker.swiftCode) score += 10
  if (broker.bankAccountNumber) score += 15
  if (broker.branchCode) score += 5
  if (broker.accountBranchName) score += 5
  
  // Address completeness
  if (broker.physicalAddress?.country) score += 5
  if (broker.physicalAddress?.town) score += 5
  if (broker.physicalAddress?.physicalAddress) score += 5
  
  // Product and callback configuration
  if (broker.productIds && broker.productIds.length > 0) score += 10
  if (broker.callBackUrl) score += 5
  
  return Math.min(score, 100) // Cap at 100
}

describe('Broker Helper Functions', () => {
  describe('validateBrokerForm', () => {
    it('should validate a complete valid form', () => {
      const result = validateBrokerForm(mockCreateBrokerForm)
      
      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual({})
    })

    it('should return errors for missing required fields', () => {
      const invalidForm = {
        name: '',
        email: '',
        mobile: '',
        bankName: '',
        bankCode: '',
        swiftCode: '',
        bankAccountNumber: '',
        branchCode: '',
        accountBranchName: '',
        callBackUrl: '',
        productIds: [],
        status: '',
        physicalAddress: {
          country: '',
          town: '',
          physicalAddress: '',
        }
      }
      
      const result = validateBrokerForm(invalidForm)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.name).toBe('Broker name is required')
      expect(result.errors.email).toBe('Email is required')
      expect(result.errors.mobile).toBe('Mobile number is required')
      expect(result.errors.bankName).toBe('Bank name is required')
      expect(result.errors.productIds).toBe('At least one product must be selected')
    })

    it('should validate email format', () => {
      const formWithInvalidEmail = {
        ...mockCreateBrokerForm,
        email: 'invalid-email'
      }
      
      const result = validateBrokerForm(formWithInvalidEmail)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.email).toBe('Invalid email format')
    })

    it('should validate mobile number format', () => {
      const formWithInvalidMobile = {
        ...mockCreateBrokerForm,
        mobile: '*********'
      }
      
      const result = validateBrokerForm(formWithInvalidMobile)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.mobile).toBe('Invalid mobile number format')
    })

    it('should validate URL format', () => {
      const formWithInvalidUrl = {
        ...mockCreateBrokerForm,
        callBackUrl: 'not-a-url'
      }
      
      const result = validateBrokerForm(formWithInvalidUrl)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.callBackUrl).toBe('Invalid URL format')
    })

    it('should validate physical address fields', () => {
      const formWithInvalidAddress = {
        ...mockCreateBrokerForm,
        physicalAddress: {
          country: '',
          town: '',
          physicalAddress: '',
        }
      }
      
      const result = validateBrokerForm(formWithInvalidAddress)
      
      expect(result.isValid).toBe(false)
      expect(result.errors.physicalAddress.country).toBe('Country is required')
      expect(result.errors.physicalAddress.town).toBe('Town is required')
      expect(result.errors.physicalAddress.physicalAddress).toBe('Physical address is required')
    })
  })

  describe('formatBrokerData', () => {
    it('should format broker data correctly', () => {
      const rawData = {
        name: '  Test Broker  ',
        email: '  <EMAIL>  ',
        mobile: '+254 712 345 678',
        swiftCode: 'testke22',
        physicalAddress: {
          country: '  Kenya  ',
          town: '  Nairobi  ',
          physicalAddress: '  123 Test Street  ',
        }
      }
      
      const formatted = formatBrokerData(rawData)
      
      expect(formatted.name).toBe('Test Broker')
      expect(formatted.email).toBe('<EMAIL>')
      expect(formatted.mobile).toBe('+254712345678')
      expect(formatted.swiftCode).toBe('TESTKE22')
      expect(formatted.physicalAddress.country).toBe('Kenya')
      expect(formatted.physicalAddress.town).toBe('Nairobi')
      expect(formatted.physicalAddress.physicalAddress).toBe('123 Test Street')
    })
  })

  describe('mapOrganizationToBrokerForm', () => {
    it('should map organization data to broker form', () => {
      const organization = mockOrganizations[0]
      const brokerForm = mapOrganizationToBrokerForm(organization)
      
      expect(brokerForm.name).toBe(organization.name)
      expect(brokerForm.email).toBe(organization.email)
      expect(brokerForm.mobile).toBe(organization.mobile)
      expect(brokerForm.bankName).toBe(organization.bankName)
      expect(brokerForm.physicalAddress.country).toBe(organization.physicalAddress.country)
      expect(brokerForm.productIds).toEqual([])
      expect(brokerForm.callBackUrl).toBe('')
    })

    it('should handle organization with missing physical address', () => {
      const organizationWithoutAddress = {
        ...mockOrganizations[0],
        physicalAddress: null
      }
      
      const brokerForm = mapOrganizationToBrokerForm(organizationWithoutAddress)
      
      expect(brokerForm.physicalAddress.country).toBe('')
      expect(brokerForm.physicalAddress.town).toBe('')
      expect(brokerForm.physicalAddress.physicalAddress).toBe('')
    })
  })

  describe('calculateBrokerScore', () => {
    it('should calculate score for complete broker', () => {
      const score = calculateBrokerScore(mockBroker)
      
      expect(score).toBeGreaterThan(90)
      expect(score).toBeLessThanOrEqual(100)
    })

    it('should calculate score for incomplete broker', () => {
      const incompleteBroker = {
        name: 'Test Broker',
        email: '<EMAIL>',
        mobile: '+254712345678',
      }
      
      const score = calculateBrokerScore(incompleteBroker)
      
      expect(score).toBe(30) // 10 + 10 + 10
    })

    it('should cap score at 100', () => {
      const overCompleteBroker = {
        ...mockBroker,
        extraField1: 'value1',
        extraField2: 'value2',
      }
      
      const score = calculateBrokerScore(overCompleteBroker)
      
      expect(score).toBe(100)
    })

    it('should handle broker with no data', () => {
      const emptyBroker = {}
      
      const score = calculateBrokerScore(emptyBroker)
      
      expect(score).toBe(0)
    })
  })
})
