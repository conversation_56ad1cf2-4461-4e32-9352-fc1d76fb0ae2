import { describe, it, expect } from 'vitest'
import { mockCreateBrokerF<PERSON>, mockBroker, mockLoanProducts, mockOrganizations } from '../stubs/brokerStubs'

// Transformer functions to test
const transformBrokerForAPI = (formData: any) => {
  return {
    name: formData.name,
    email: formData.email,
    mobile: formData.mobile,
    bankName: formData.bankName,
    bankCode: formData.bankCode,
    swiftCode: formData.swiftCode,
    bankAccountNumber: formData.bankAccountNumber,
    branchCode: formData.branchCode,
    accountBranchName: formData.accountBranchName,
    physicalAddress: formData.physicalAddress,
    callBackUrl: formData.callBackUrl,
    productIds: formData.productIds,
    status: formData.status,
  }
}

const transformAPIResponseToBroker = (apiResponse: any) => {
  return {
    id: apiResponse.id,
    name: apiResponse.name,
    email: apiResponse.email,
    mobile: apiResponse.mobile,
    bankName: apiResponse.bankName,
    bankCode: apiResponse.bankCode,
    swiftCode: apiResponse.swiftCode,
    bankAccountNumber: apiResponse.bankAccountNumber,
    branchCode: apiResponse.branchCode,
    accountBranchName: apiResponse.accountBranchName,
    physicalAddress: apiResponse.physicalAddress,
    callBackUrl: apiResponse.callBackUrl,
    productIds: apiResponse.productIds || [],
    status: apiResponse.status,
    createdAt: apiResponse.dateCreated || apiResponse.createdAt,
    updatedAt: apiResponse.dateModified || apiResponse.updatedAt,
  }
}

const filterBrokersByStatus = (brokers: any[], status: string) => {
  return brokers.filter(broker => broker.status === status)
}

const filterBrokersByProduct = (brokers: any[], productId: string) => {
  return brokers.filter(broker => 
    broker.productIds && broker.productIds.includes(productId)
  )
}

const sortBrokersByName = (brokers: any[], ascending: boolean = true) => {
  return [...brokers].sort((a, b) => {
    const nameA = a.name.toLowerCase()
    const nameB = b.name.toLowerCase()
    
    if (ascending) {
      return nameA < nameB ? -1 : nameA > nameB ? 1 : 0
    } else {
      return nameA > nameB ? -1 : nameA < nameB ? 1 : 0
    }
  })
}

const sortBrokersByDate = (brokers: any[], field: 'createdAt' | 'updatedAt', ascending: boolean = true) => {
  return [...brokers].sort((a, b) => {
    const dateA = new Date(a[field]).getTime()
    const dateB = new Date(b[field]).getTime()
    
    if (ascending) {
      return dateA - dateB
    } else {
      return dateB - dateA
    }
  })
}

const searchBrokers = (brokers: any[], searchTerm: string) => {
  const term = searchTerm.toLowerCase()
  
  return brokers.filter(broker => 
    broker.name.toLowerCase().includes(term) ||
    broker.email.toLowerCase().includes(term) ||
    broker.mobile.includes(term) ||
    broker.bankName.toLowerCase().includes(term)
  )
}

const paginateBrokers = (brokers: any[], page: number, pageSize: number) => {
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  
  return {
    data: brokers.slice(startIndex, endIndex),
    totalItems: brokers.length,
    totalPages: Math.ceil(brokers.length / pageSize),
    currentPage: page,
    pageSize: pageSize,
    hasNextPage: endIndex < brokers.length,
    hasPreviousPage: page > 1,
  }
}

const getBrokerSummary = (brokers: any[]) => {
  const summary = {
    total: brokers.length,
    active: 0,
    inactive: 0,
    pending: 0,
    byProduct: {} as Record<string, number>,
    byBank: {} as Record<string, number>,
  }
  
  brokers.forEach(broker => {
    // Count by status
    if (broker.status === 'Active') {
      summary.active++
    } else if (broker.status === 'Inactive') {
      summary.inactive++
    } else if (broker.status === 'Pending') {
      summary.pending++
    }
    
    // Count by products
    if (broker.productIds) {
      broker.productIds.forEach((productId: string) => {
        summary.byProduct[productId] = (summary.byProduct[productId] || 0) + 1
      })
    }
    
    // Count by bank
    if (broker.bankName) {
      summary.byBank[broker.bankName] = (summary.byBank[broker.bankName] || 0) + 1
    }
  })
  
  return summary
}

describe('Broker Transformer Functions', () => {
  const sampleBrokers = [
    { ...mockBroker, id: '1', name: 'Alpha Broker', status: 'Active', createdAt: '2024-01-01' },
    { ...mockBroker, id: '2', name: 'Beta Broker', status: 'Inactive', createdAt: '2024-01-02' },
    { ...mockBroker, id: '3', name: 'Gamma Broker', status: 'Active', createdAt: '2024-01-03' },
  ]

  describe('transformBrokerForAPI', () => {
    it('should transform form data for API submission', () => {
      const transformed = transformBrokerForAPI(mockCreateBrokerForm)
      
      expect(transformed).toHaveProperty('name', mockCreateBrokerForm.name)
      expect(transformed).toHaveProperty('email', mockCreateBrokerForm.email)
      expect(transformed).toHaveProperty('mobile', mockCreateBrokerForm.mobile)
      expect(transformed).toHaveProperty('bankName', mockCreateBrokerForm.bankName)
      expect(transformed).toHaveProperty('physicalAddress', mockCreateBrokerForm.physicalAddress)
      expect(transformed).toHaveProperty('productIds', mockCreateBrokerForm.productIds)
    })

    it('should include all required fields', () => {
      const transformed = transformBrokerForAPI(mockCreateBrokerForm)
      
      const requiredFields = [
        'name', 'email', 'mobile', 'bankName', 'bankCode', 'swiftCode',
        'bankAccountNumber', 'branchCode', 'accountBranchName', 'physicalAddress',
        'callBackUrl', 'productIds', 'status'
      ]
      
      requiredFields.forEach(field => {
        expect(transformed).toHaveProperty(field)
      })
    })
  })

  describe('transformAPIResponseToBroker', () => {
    it('should transform API response to broker object', () => {
      const apiResponse = {
        id: 'broker-123',
        name: 'Test Broker',
        email: '<EMAIL>',
        mobile: '+************',
        bankName: 'Test Bank',
        bankCode: '001',
        swiftCode: 'TESTKE22',
        bankAccountNumber: '**********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: { country: 'Kenya', town: 'Nairobi', physicalAddress: '123 Test St' },
        callBackUrl: 'https://test.com/callback',
        productIds: ['product-1'],
        status: 'Active',
        dateCreated: '2024-01-01T00:00:00Z',
        dateModified: '2024-01-02T00:00:00Z',
      }
      
      const transformed = transformAPIResponseToBroker(apiResponse)
      
      expect(transformed.id).toBe(apiResponse.id)
      expect(transformed.name).toBe(apiResponse.name)
      expect(transformed.createdAt).toBe(apiResponse.dateCreated)
      expect(transformed.updatedAt).toBe(apiResponse.dateModified)
      expect(transformed.productIds).toEqual(apiResponse.productIds)
    })

    it('should handle missing productIds', () => {
      const apiResponse = {
        id: 'broker-123',
        name: 'Test Broker',
        email: '<EMAIL>',
        status: 'Active',
      }
      
      const transformed = transformAPIResponseToBroker(apiResponse)
      
      expect(transformed.productIds).toEqual([])
    })
  })

  describe('filterBrokersByStatus', () => {
    it('should filter brokers by active status', () => {
      const activeBrokers = filterBrokersByStatus(sampleBrokers, 'Active')
      
      expect(activeBrokers).toHaveLength(2)
      expect(activeBrokers.every(broker => broker.status === 'Active')).toBe(true)
    })

    it('should filter brokers by inactive status', () => {
      const inactiveBrokers = filterBrokersByStatus(sampleBrokers, 'Inactive')
      
      expect(inactiveBrokers).toHaveLength(1)
      expect(inactiveBrokers[0].status).toBe('Inactive')
    })

    it('should return empty array for non-existent status', () => {
      const result = filterBrokersByStatus(sampleBrokers, 'NonExistent')
      
      expect(result).toHaveLength(0)
    })
  })

  describe('filterBrokersByProduct', () => {
    it('should filter brokers by product ID', () => {
      const brokersWithProduct = filterBrokersByProduct(sampleBrokers, 'product-1')
      
      expect(brokersWithProduct.length).toBeGreaterThan(0)
      expect(brokersWithProduct.every(broker => 
        broker.productIds && broker.productIds.includes('product-1')
      )).toBe(true)
    })

    it('should return empty array for non-existent product', () => {
      const result = filterBrokersByProduct(sampleBrokers, 'non-existent-product')
      
      expect(result).toHaveLength(0)
    })
  })

  describe('sortBrokersByName', () => {
    it('should sort brokers by name in ascending order', () => {
      const sorted = sortBrokersByName(sampleBrokers, true)
      
      expect(sorted[0].name).toBe('Alpha Broker')
      expect(sorted[1].name).toBe('Beta Broker')
      expect(sorted[2].name).toBe('Gamma Broker')
    })

    it('should sort brokers by name in descending order', () => {
      const sorted = sortBrokersByName(sampleBrokers, false)
      
      expect(sorted[0].name).toBe('Gamma Broker')
      expect(sorted[1].name).toBe('Beta Broker')
      expect(sorted[2].name).toBe('Alpha Broker')
    })

    it('should not mutate original array', () => {
      const originalOrder = sampleBrokers.map(b => b.name)
      sortBrokersByName(sampleBrokers, true)
      
      expect(sampleBrokers.map(b => b.name)).toEqual(originalOrder)
    })
  })

  describe('sortBrokersByDate', () => {
    it('should sort brokers by creation date in ascending order', () => {
      const sorted = sortBrokersByDate(sampleBrokers, 'createdAt', true)
      
      expect(sorted[0].createdAt).toBe('2024-01-01')
      expect(sorted[1].createdAt).toBe('2024-01-02')
      expect(sorted[2].createdAt).toBe('2024-01-03')
    })

    it('should sort brokers by creation date in descending order', () => {
      const sorted = sortBrokersByDate(sampleBrokers, 'createdAt', false)
      
      expect(sorted[0].createdAt).toBe('2024-01-03')
      expect(sorted[1].createdAt).toBe('2024-01-02')
      expect(sorted[2].createdAt).toBe('2024-01-01')
    })
  })

  describe('searchBrokers', () => {
    it('should search brokers by name', () => {
      const results = searchBrokers(sampleBrokers, 'Alpha')
      
      expect(results).toHaveLength(1)
      expect(results[0].name).toBe('Alpha Broker')
    })

    it('should search brokers by email', () => {
      const results = searchBrokers(sampleBrokers, mockBroker.email)
      
      expect(results.length).toBeGreaterThan(0)
    })

    it('should be case insensitive', () => {
      const results = searchBrokers(sampleBrokers, 'ALPHA')
      
      expect(results).toHaveLength(1)
      expect(results[0].name).toBe('Alpha Broker')
    })

    it('should return empty array for no matches', () => {
      const results = searchBrokers(sampleBrokers, 'NonExistentBroker')
      
      expect(results).toHaveLength(0)
    })
  })

  describe('paginateBrokers', () => {
    it('should paginate brokers correctly', () => {
      const result = paginateBrokers(sampleBrokers, 1, 2)
      
      expect(result.data).toHaveLength(2)
      expect(result.totalItems).toBe(3)
      expect(result.totalPages).toBe(2)
      expect(result.currentPage).toBe(1)
      expect(result.pageSize).toBe(2)
      expect(result.hasNextPage).toBe(true)
      expect(result.hasPreviousPage).toBe(false)
    })

    it('should handle last page correctly', () => {
      const result = paginateBrokers(sampleBrokers, 2, 2)
      
      expect(result.data).toHaveLength(1)
      expect(result.hasNextPage).toBe(false)
      expect(result.hasPreviousPage).toBe(true)
    })

    it('should handle empty results', () => {
      const result = paginateBrokers([], 1, 10)
      
      expect(result.data).toHaveLength(0)
      expect(result.totalItems).toBe(0)
      expect(result.totalPages).toBe(0)
    })
  })

  describe('getBrokerSummary', () => {
    it('should generate broker summary correctly', () => {
      const summary = getBrokerSummary(sampleBrokers)
      
      expect(summary.total).toBe(3)
      expect(summary.active).toBe(2)
      expect(summary.inactive).toBe(1)
      expect(summary.pending).toBe(0)
    })

    it('should count products correctly', () => {
      const summary = getBrokerSummary(sampleBrokers)
      
      expect(summary.byProduct['product-1']).toBeGreaterThan(0)
    })

    it('should count banks correctly', () => {
      const summary = getBrokerSummary(sampleBrokers)
      
      expect(summary.byBank[mockBroker.bankName]).toBeGreaterThan(0)
    })

    it('should handle empty broker list', () => {
      const summary = getBrokerSummary([])
      
      expect(summary.total).toBe(0)
      expect(summary.active).toBe(0)
      expect(summary.inactive).toBe(0)
      expect(summary.pending).toBe(0)
    })
  })
})
