import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { FormikProvider, useFormik } from 'formik'
import * as Yup from 'yup'
import { vi } from 'vitest'

import loansReducer from '@/store/reducers/loansReducer'
import { ICreateBrokerForm, IBroker } from '@/store/interfaces'
import { mockCreateBrokerForm, mockBroker } from '../stubs/brokerStubs'

// Mock store configuration
export const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      loans: loansReducer,
    },
    preloadedState: {
      loans: {
        // Core loan request properties
        isViewingRequest: false,
        loanRequests: [],
        isLoadingLoans: false,
        loansCount: 0,
        pageNumber: 0,
        pageSize: 10,
        totalPageCount: 0,
        selectedRequest: {} as any,
        customerProfile: {} as any,
        isLoadingCustomerProfile: false,
        customerDocuments: [],
        customerChecks: [],
        loanRepayments: [],

        // Loan products
        loanProducts: [],
        loanProductsSummary: {} as any,
        isLoadingLoanProducts: false,
        currentProduct: {} as any,
        isLoadingOverrideValidationCheck: false,
        createdProduct: {} as any,
        isLoadingCreateProduct: false,
        isLoadingUpdateProduct: false,
        userProducts: [],
        productToCopy: {} as any,
        productToView: {} as any,
        productToAssignValidations: {} as any,

        // Organizations
        organizations: [],
        organizationsSummary: {} as any,
        editOrganization: {} as any,
        isLoadingOrganizations: false,
        isLoadingCreateOrganization: false,
        isLoadingEditOrganization: false,

        // Filters and validations
        listFilters: { status: '' },
        loanValidations: [],
        kycTypeValidations: [],
        creditTypeValidations: [],
        productValidations: [],
        singleProductValidations: [],
        isLoadingProductValidations: false,
        isLoadingAddValidation: false,

        // Product categories
        productCategories: [],
        productCategoryTypes: [],
        productCategoriesRequest: {} as any,
        isLoadingCreateCategory: false,
        isLoadingUpdateCategory: false,

        // Bank branches
        bankBranches: [],

        // Reports
        organizationLimitReportResponse: {} as any,
        isLoadingOrgLimitReports: false,
        isLoadingOrgLimitReportsExport: false,
        isSuccessOrgLimitReports: false,
        isSuccessOrgLimitReportsExport: false,
        loanReportRequestResponse: {} as any,
        isLoadingReportRequest: false,
        isSuccessfulReportRequest: false,
        isLoadingGenerateRequestsReport: false,
        isSuccessfulGenerateRequestsReport: false,
        loanReportResponse: {} as any,
        isLoadingLoanReport: false,
        isSuccessfulLoanReport: false,
        isLoadingGenerateLoanReport: false,
        isSuccessfulGenerateLoanReport: false,
        requestReports: {} as any,
        isLoadingRequestReportsCheck: false,
        isLoadingRequestReportsCheckExport: false,
        isSuccessRequestReportsCheck: false,
        isSuccessRequestReportsCheckExport: false,

        // Customer request checks
        isLoadingCustomerRequestChecksReport: false,
        customerRequestChecksReport: {} as any,
        isLoadingCustomerReportCheckReportExport: false,
        customerRequestChecksFilters: {},

        // UI states
        isCheckerViewProfileOpen: false,
        isCheckRerunLoading: false,
        isLoadingCancelRequest: false,
        isLoadingUploadDocument: false,

        // Broker management
        brokersSummary: {} as any,
        isLoadingBrokers: false,
        brokers: [],
        createdBroker: {} as any,
        selectedBroker: {} as any,
        isLoadingCreateBroker: false,
        isCreateBrokerSuccess: false,
        isCreateBrokerFailure: false,
        isLoadingUpdateBroker: false,
        isUpdateBrokerSuccess: false,
        isUpdateBrokerFailure: false,
        isLoadingAssignBrokerProduct: false,
        isSuccessAssignBrokerProduct: false,
        isFailureAssignBrokerProduct: false,
        isGenerateBrokerSecretLoading: false,
        isGenerateBrokerSecretSuccess: false,
        isGenerateBrokerSecretFailure: false,
        brokerProducts: [],
        selectedBrokerExisting: {} as any,

        // Optin requests
        optinCount: 0,
        optinPageNumber: 0,
        optinTotalPageCount: 0,
        optinPageSize: 10,
        optinRequests: [],
        isLoadingOptinRequests: false,
        selectedOptinRequest: {} as any,
        optinCustomerChecks: [],
        currentListType: 0,
        isLoadingCancelOptinRequest: false,

        ...initialState,
      },
    },
  })
}

// Broker form validation schema
export const brokerValidationSchema = Yup.object({
  name: Yup.string().required('Broker name is required'),
  email: Yup.string().email('Invalid email').required('Email is required'),
  mobile: Yup.string().required('Mobile number is required'),
  bankName: Yup.string().required('Bank name is required'),
  bankCode: Yup.string().required('Bank code is required'),
  swiftCode: Yup.string().required('Swift code is required'),
  bankAccountNumber: Yup.string().required('Bank account number is required'),
  branchCode: Yup.string().required('Branch code is required'),
  accountBranchName: Yup.string().required('Account branch name is required'),
  physicalAddress: Yup.object({
    country: Yup.string().required('Country is required'),
    town: Yup.string().required('Town is required'),
    physicalAddress: Yup.string().required('Physical address is required'),
  }),
  callBackUrl: Yup.string().url('Invalid URL').required('Callback URL is required'),
  productIds: Yup.array().min(1, 'At least one product must be selected'),
  status: Yup.string().required('Status is required'),
})

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any
  store?: any
}

export const renderWithProviders = (
  ui: React.ReactElement,
  {
    initialState = {},
    store = createMockStore(initialState),
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        {children}
      </Provider>
    )
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

// Formik wrapper for testing form components
interface FormikWrapperProps {
  children: (formik: any) => React.ReactNode
  initialValues?: ICreateBrokerForm
  onSubmit?: (values: ICreateBrokerForm) => void
  validationSchema?: any
}

export const FormikWrapper: React.FC<FormikWrapperProps> = ({
  children,
  initialValues = mockCreateBrokerForm,
  onSubmit = vi.fn(),
  validationSchema = brokerValidationSchema,
}) => {
  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit,
  })

  return <FormikProvider value={formik}>{children(formik)}</FormikProvider>
}

// Mock functions for testing
export const mockSetCurrentStage = vi.fn()
export const mockDispatch = vi.fn()
export const mockNavigate = vi.fn()

// Helper function to create mock formik instance
export const createMockFormik = (
  initialValues: ICreateBrokerForm = mockCreateBrokerForm,
  overrides: Partial<any> = {}
) => ({
  values: initialValues,
  initialValues: initialValues,
  errors: {},
  touched: {},
  isSubmitting: false,
  isValidating: false,
  submitCount: 0,
  initialErrors: {},
  initialTouched: {},
  handleBlur: vi.fn(),
  handleChange: vi.fn(),
  handleReset: vi.fn(),
  handleSubmit: vi.fn(),
  resetForm: vi.fn(),
  setErrors: vi.fn(),
  setFieldError: vi.fn(),
  setFieldTouched: vi.fn(),
  setFieldValue: vi.fn(),
  setFormikState: vi.fn(),
  setStatus: vi.fn(),
  setSubmitting: vi.fn(),
  setTouched: vi.fn(),
  setValues: vi.fn(),
  submitForm: vi.fn(),
  validateForm: vi.fn(),
  validateField: vi.fn(),
  getFieldProps: vi.fn((nameOrOptions: any) => {
    const name = typeof nameOrOptions === 'string' ? nameOrOptions : nameOrOptions.name;
    return {
      name,
      value: initialValues[name as keyof ICreateBrokerForm] as any || '',
      onChange: vi.fn(),
      onBlur: vi.fn(),
    };
  }),
  getFieldMeta: vi.fn(),
  getFieldHelpers: vi.fn(),
  dirty: false,
  isValid: true,
  status: undefined,
  registerField: vi.fn(),
  unregisterField: vi.fn(),
  ...overrides,
})

// Mock API responses
export const mockApiResponses = {
  createBrokerSuccess: {
    data: mockBroker,
    status: 201,
    statusText: 'Created',
  },
  updateBrokerSuccess: {
    data: { ...mockBroker, name: 'Updated Broker Name' },
    status: 200,
    statusText: 'OK',
  },
  createBrokerError: {
    response: {
      data: { message: 'Broker creation failed' },
      status: 400,
      statusText: 'Bad Request',
    },
  },
  updateBrokerError: {
    response: {
      data: { message: 'Broker update failed' },
      status: 400,
      statusText: 'Bad Request',
    },
  },
}

// Test data generators
export const generateBrokerFormData = (overrides: Partial<ICreateBrokerForm> = {}): ICreateBrokerForm => ({
  ...mockCreateBrokerForm,
  ...overrides,
})

export const generateBrokerData = (overrides: Partial<IBroker> = {}): IBroker => ({
  ...mockBroker,
  ...overrides,
})

// Common test scenarios
export const testScenarios = {
  validFormData: mockCreateBrokerForm,
  invalidFormData: {
    ...mockCreateBrokerForm,
    name: '',
    email: 'invalid-email',
    mobile: '',
  },
  partialFormData: {
    name: 'Test Broker',
    email: '<EMAIL>',
    mobile: '+************',
  },
}

// Mock store states
export const mockStoreStates = {
  initial: {
    isLoadingCreateBroker: false,
    isLoadingUpdateBroker: false,
    isCreateBrokerSuccess: false,
    isCreateBrokerFailure: false,
    isUpdateBrokerSuccess: false,
    isUpdateBrokerFailure: false,
    createdBroker: {} as any,
    brokers: [],
    organizations: [],
    loanProducts: [],
    bankBranches: [],
  },
  loading: {
    isLoadingCreateBroker: true,
    isLoadingUpdateBroker: false,
  },
  success: {
    isLoadingCreateBroker: false,
    isCreateBrokerSuccess: true,
    createdBroker: mockBroker,
  },
  error: {
    isLoadingCreateBroker: false,
    isCreateBrokerFailure: true,
  },
}
