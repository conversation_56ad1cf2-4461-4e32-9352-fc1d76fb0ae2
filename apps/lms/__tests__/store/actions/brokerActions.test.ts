import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createBroker, updateBroker } from '@/store/actions/loans'
import { mockApiResponses } from '../../utils/brokerTestUtils'
import { mockBroker, mockCreateBrokerForm } from '../../stubs/brokerStubs'

// Mock the secure API
vi.mock('@dtbx/store/utils', () => ({
  secureapi2: {
    post: vi.fn(),
    put: vi.fn(),
  },
  HasAccessToRights: vi.fn(() => true),
  downloadBlob: vi.fn(),
}))

// Mock the notification system
vi.mock('@dtbx/store/reducers', () => ({
  setNotification: vi.fn((payload) => ({ type: 'SET_NOTIFICATION', payload })),
}))

// Mock the store reducers
vi.mock('@/store/reducers', () => ({
  setLoadingCreateBroker: vi.fn((payload) => ({ type: 'SET_LOADING_CREATE_BROKER', payload })),
  setCreateBrokerSuccess: vi.fn((payload) => ({ type: 'SET_CREATE_BROKER_SUCCESS', payload })),
  setCreateBrokerFailure: vi.fn((payload) => ({ type: 'SET_CREATE_BROKER_FAILURE', payload })),
  setCreatedBroker: vi.fn((payload) => ({ type: 'SET_CREATED_BROKER', payload })),
  setLoadingUpdateBroker: vi.fn((payload) => ({ type: 'SET_LOADING_UPDATE_BROKER', payload })),
  setUpdateBrokerSuccess: vi.fn((payload) => ({ type: 'SET_UPDATE_BROKER_SUCCESS', payload })),
  setUpdateBrokerFailure: vi.fn((payload) => ({ type: 'SET_UPDATE_BROKER_FAILURE', payload })),
}))

const mockDispatch = vi.fn()

describe('Broker Store Actions', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createBroker', () => {
    it('should successfully create a broker', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { 
        setLoadingCreateBroker, 
        setCreateBrokerSuccess, 
        setCreatedBroker 
      } = await import('@/store/reducers')
      const { setNotification } = await import('@dtbx/store/reducers')

      secureapi2.post.mockResolvedValue(mockApiResponses.createBrokerSuccess)

      await createBroker(mockDispatch, mockCreateBrokerForm, 'make')

      // Should set loading to true initially
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))

      // Should call the API with correct parameters
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/brokers/make', mockCreateBrokerForm)

      // Should set loading to false after success
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateBroker(false))

      // Should set success state
      expect(mockDispatch).toHaveBeenCalledWith(setCreateBrokerSuccess(true))

      // Should set created broker data
      expect(mockDispatch).toHaveBeenCalledWith(setCreatedBroker(mockApiResponses.createBrokerSuccess.data))

      // Should show success notification
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker created successfully.',
          type: 'success',
        })
      )
    })

    it('should create broker without make parameter', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')

      secureapi2.post.mockResolvedValue(mockApiResponses.createBrokerSuccess)

      await createBroker(mockDispatch, mockCreateBrokerForm, 'direct')

      // Should call API without 'make' in URL
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/brokers/', mockCreateBrokerForm)
    })

    it('should handle broker creation failure', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { 
        setLoadingCreateBroker, 
        setCreateBrokerSuccess, 
        setCreateBrokerFailure 
      } = await import('@/store/reducers')
      const { setNotification } = await import('@dtbx/store/reducers')

      const error = new Error('Broker creation failed')
      secureapi2.post.mockRejectedValue(error)

      await createBroker(mockDispatch, mockCreateBrokerForm, 'make')

      // Should set loading to true initially
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))

      // Should set success to false
      expect(mockDispatch).toHaveBeenCalledWith(setCreateBrokerSuccess(false))

      // Should set failure to true
      expect(mockDispatch).toHaveBeenCalledWith(setCreateBrokerFailure(true))

      // Should set loading to false after error
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingCreateBroker(false))

      // Should show error notification
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker creation failed',
          type: 'error',
        })
      )
    })

    it('should handle API error with custom message', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { setNotification } = await import('@dtbx/store/reducers')

      const customError = new Error('Custom error message')
      secureapi2.post.mockRejectedValue(customError)

      await createBroker(mockDispatch, mockCreateBrokerForm, 'make')

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Custom error message',
          type: 'error',
        })
      )
    })
  })

  describe('updateBroker', () => {
    const brokerId = 'broker-123'
    const updateData = {
      ...mockCreateBrokerForm,
      name: 'Updated Broker Name',
    }

    it('should successfully update a broker with make type', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { 
        setLoadingUpdateBroker, 
        setUpdateBrokerSuccess, 
        setUpdateBrokerFailure 
      } = await import('@/store/reducers')
      const { setNotification } = await import('@dtbx/store/reducers')

      secureapi2.put.mockResolvedValue(mockApiResponses.updateBrokerSuccess)

      await updateBroker(brokerId, mockDispatch, updateData, 'make')

      // Should set loading states correctly
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))

      // Should call API with correct URL and data
      expect(secureapi2.put).toHaveBeenCalledWith(`lms/brokers/${brokerId}/make`, updateData)

      // Should set success states
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))

      // Should show success notification for make type
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker Details update successful pending approval',
          type: 'success',
        })
      )
    })

    it('should successfully update a broker with deactivate type', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { setNotification } = await import('@dtbx/store/reducers')

      secureapi2.put.mockResolvedValue(mockApiResponses.updateBrokerSuccess)

      await updateBroker(brokerId, mockDispatch, updateData, 'deactivate')

      // Should call API without 'make' in URL
      expect(secureapi2.put).toHaveBeenCalledWith(`lms/brokers/${brokerId}/`, updateData)

      // Should show deactivate success message
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker deactivated successfully.',
          type: 'success',
        })
      )
    })

    it('should successfully update a broker with reactivate type', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { setNotification } = await import('@dtbx/store/reducers')

      secureapi2.put.mockResolvedValue(mockApiResponses.updateBrokerSuccess)

      await updateBroker(brokerId, mockDispatch, updateData, 'reactivate')

      // Should show reactivate success message
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker re-activated successfully.',
          type: 'success',
        })
      )
    })

    it('should successfully update a broker with activate type', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { setNotification } = await import('@dtbx/store/reducers')

      secureapi2.put.mockResolvedValue(mockApiResponses.updateBrokerSuccess)

      await updateBroker(brokerId, mockDispatch, updateData, 'activate')

      // Should show activate success message
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker activated successfully.',
          type: 'success',
        })
      )
    })

    it('should handle broker update failure', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { 
        setLoadingUpdateBroker, 
        setUpdateBrokerSuccess, 
        setUpdateBrokerFailure 
      } = await import('@/store/reducers')
      const { setNotification } = await import('@dtbx/store/reducers')

      const error = new Error('Broker update failed')
      secureapi2.put.mockRejectedValue(error)

      await updateBroker(brokerId, mockDispatch, updateData, 'make')

      // Should set loading to true initially
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))

      // Should set error states
      expect(mockDispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(true))

      // Should show error notification
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker update failed',
          type: 'error',
        })
      )
    })

    it('should handle different update types correctly', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')

      secureapi2.put.mockResolvedValue(mockApiResponses.updateBrokerSuccess)

      const testCases = [
        { type: 'make', expectedUrl: `lms/brokers/${brokerId}/make` },
        { type: 'deactivate', expectedUrl: `lms/brokers/${brokerId}/` },
        { type: 'reactivate', expectedUrl: `lms/brokers/${brokerId}/` },
        { type: 'activate', expectedUrl: `lms/brokers/${brokerId}/` },
        { type: 'other', expectedUrl: `lms/brokers/${brokerId}/` },
      ]

      for (const testCase of testCases) {
        vi.clearAllMocks()
        await updateBroker(brokerId, mockDispatch, updateData, testCase.type)
        expect(secureapi2.put).toHaveBeenCalledWith(testCase.expectedUrl, updateData)
      }
    })

    it('should handle network errors gracefully', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')
      const { setNotification } = await import('@dtbx/store/reducers')

      const networkError = new Error('Network Error')
      secureapi2.put.mockRejectedValue(networkError)

      await updateBroker(brokerId, mockDispatch, updateData, 'make')

      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Network Error',
          type: 'error',
        })
      )
    })

    it('should handle empty broker ID', async () => {
      const { secureapi2 } = await import('@dtbx/store/utils')

      secureapi2.put.mockResolvedValue(mockApiResponses.updateBrokerSuccess)

      await updateBroker('', mockDispatch, updateData, 'make')

      expect(secureapi2.put).toHaveBeenCalledWith('lms/brokers//make', updateData)
    })
  })
})
