import { describe, it, expect } from 'vitest'
import * as Yup from 'yup'
import { brokerValidationSchema } from '../utils/brokerTestUtils'
import { mockCreateBrokerForm, mockValidationErrors } from '../stubs/brokerStubs'

describe('Broker Form Validation', () => {
  describe('Required Fields Validation', () => {
    it('should validate all required fields are present', async () => {
      const validData = mockCreateBrokerForm

      const result = await brokerValidationSchema.isValid(validData)
      expect(result).toBe(true)
    })

    it('should fail validation when name is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, name: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Broker name is required')
      }
    })

    it('should fail validation when email is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, email: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Email is required')
      }
    })

    it('should fail validation when mobile is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, mobile: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Mobile number is required')
      }
    })

    it('should fail validation when bank name is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, bankName: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Bank name is required')
      }
    })

    it('should fail validation when bank code is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, bankCode: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Bank code is required')
      }
    })

    it('should fail validation when swift code is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, swiftCode: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Swift code is required')
      }
    })

    it('should fail validation when bank account number is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, bankAccountNumber: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Bank account number is required')
      }
    })

    it('should fail validation when branch code is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, branchCode: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Branch code is required')
      }
    })

    it('should fail validation when account branch name is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, accountBranchName: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Account branch name is required')
      }
    })

    it('should fail validation when callback URL is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, callBackUrl: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Callback URL is required')
      }
    })

    it('should fail validation when status is missing', async () => {
      const invalidData = { ...mockCreateBrokerForm, status: '' }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Status is required')
      }
    })
  })

  describe('Email Validation', () => {
    it('should accept valid email addresses', async () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]

      for (const email of validEmails) {
        const data = { ...mockCreateBrokerForm, email }
        const result = await brokerValidationSchema.isValid(data)
        expect(result).toBe(true)
      }
    })

    it('should reject invalid email addresses', async () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>',
        'user@domain',
        'user <EMAIL>',
      ]

      for (const email of invalidEmails) {
        const data = { ...mockCreateBrokerForm, email }
        try {
          await brokerValidationSchema.validate(data, { abortEarly: false })
        } catch (error) {
          expect(error.errors).toContain('Invalid email')
        }
      }
    })
  })

  describe('URL Validation', () => {
    it('should accept valid URLs', async () => {
      const validUrls = [
        'https://example.com',
        'http://test.com/callback',
        'https://api.broker.com/webhook',
        'https://subdomain.domain.com/path?param=value',
      ]

      for (const url of validUrls) {
        const data = { ...mockCreateBrokerForm, callBackUrl: url }
        const result = await brokerValidationSchema.isValid(data)
        expect(result).toBe(true)
      }
    })

    it('should reject invalid URLs', async () => {
      const invalidUrls = [
        'not-a-url',
        'ftp://example.com',
        'just-text',
        'http://',
        'https://',
        'www.example.com',
      ]

      for (const url of invalidUrls) {
        const data = { ...mockCreateBrokerForm, callBackUrl: url }
        try {
          await brokerValidationSchema.validate(data, { abortEarly: false })
        } catch (error) {
          expect(error.errors).toContain('Invalid URL')
        }
      }
    })
  })

  describe('Physical Address Validation', () => {
    it('should validate all physical address fields are required', async () => {
      const invalidData = {
        ...mockCreateBrokerForm,
        physicalAddress: {
          country: '',
          town: '',
          physicalAddress: '',
        },
      }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Country is required')
        expect(error.errors).toContain('Town is required')
        expect(error.errors).toContain('Physical address is required')
      }
    })

    it('should validate partial physical address fields', async () => {
      const invalidData = {
        ...mockCreateBrokerForm,
        physicalAddress: {
          country: 'Kenya',
          town: '',
          physicalAddress: 'Some address',
        },
      }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('Town is required')
        expect(error.errors).not.toContain('Country is required')
        expect(error.errors).not.toContain('Physical address is required')
      }
    })
  })

  describe('Product Selection Validation', () => {
    it('should require at least one product to be selected', async () => {
      const invalidData = { ...mockCreateBrokerForm, productIds: [] }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors).toContain('At least one product must be selected')
      }
    })

    it('should accept single product selection', async () => {
      const validData = { ...mockCreateBrokerForm, productIds: ['product-1'] }

      const result = await brokerValidationSchema.isValid(validData)
      expect(result).toBe(true)
    })

    it('should accept multiple product selection', async () => {
      const validData = { ...mockCreateBrokerForm, productIds: ['product-1', 'product-2', 'product-3'] }

      const result = await brokerValidationSchema.isValid(validData)
      expect(result).toBe(true)
    })
  })

  describe('Edge Cases and Complex Validation', () => {
    it('should handle null and undefined values', async () => {
      const invalidData = {
        ...mockCreateBrokerForm,
        name: null,
        email: undefined,
        mobile: null,
      }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors.length).toBeGreaterThan(0)
      }
    })

    it('should handle whitespace-only values', async () => {
      const invalidData = {
        ...mockCreateBrokerForm,
        name: '   ',
        email: '  ',
        bankName: '\t\n',
      }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        expect(error.errors.length).toBeGreaterThan(0)
      }
    })

    it('should validate all fields simultaneously', async () => {
      const invalidData = {
        name: '',
        email: 'invalid-email',
        mobile: '',
        bankName: '',
        bankCode: '',
        swiftCode: '',
        bankAccountNumber: '',
        branchCode: '',
        accountBranchName: '',
        physicalAddress: {
          country: '',
          town: '',
          physicalAddress: '',
        },
        callBackUrl: 'invalid-url',
        productIds: [],
        status: '',
      }

      try {
        await brokerValidationSchema.validate(invalidData, { abortEarly: false })
      } catch (error) {
        // multiple validation errors
        expect(error.errors.length).toBeGreaterThan(10)
      }
    })

    it('should pass validation with minimal valid data', async () => {
      const minimalValidData = {
        name: 'Test Broker',
        email: '<EMAIL>',
        mobile: '+************',
        bankName: 'Test Bank',
        bankCode: '001',
        swiftCode: 'TESTKE22',
        bankAccountNumber: '**********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Kenya',
          town: 'Nairobi',
          physicalAddress: '123 Test Street',
        },
        callBackUrl: 'https://test.com/callback',
        productIds: ['product-1'],
        status: 'Active',
      }

      const result = await brokerValidationSchema.isValid(minimalValidData)
      expect(result).toBe(true)
    })

    it('should handle very long field values', async () => {
      const longString = 'a'.repeat(1000)
      const dataWithLongValues = {
        ...mockCreateBrokerForm,
        name: longString,
        email: `${longString}@example.com`,
        physicalAddress: {
          ...mockCreateBrokerForm.physicalAddress,
          physicalAddress: longString,
        },
      }

      const result = await brokerValidationSchema.isValid(dataWithLongValues)
      expect(typeof result).toBe('boolean')
    })
  })
})
