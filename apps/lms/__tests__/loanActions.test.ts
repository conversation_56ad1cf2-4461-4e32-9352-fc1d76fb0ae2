import { afterEach, describe, expect, it, vi } from 'vitest'

import {
  acceptCancelLoanRequest,
  addProductValidation,
  approveMakeCreateBroker,
  approveOrganizationRequest,
  approveUpdateBroker,
  assignProductToBroker,
  cancelLoanRequest,
  completeValidationOverride,
  createBroker,
  createBrokerAndAssignProducts,
  createLoanProduct,
  createOrganization,
  createProductCategory,
  generateBrokerSecret,
  generateLoanReports,
  generateLoanRequestReports,
  generateOrganizationLimitReport,
  generateRequestChecksReport,
  getAllValidations,
  getBankBranches,
  getBrokerProducts,
  getBrokers,
  getCustomerChecks,
  getCustomerDocuments,
  getCustomerDocumentsByRequestId,
  getLoanCustomerProfile,
  getLoanProductById,
  getLoanProductCategories,
  getLoanProductCategoryTypes,
  getLoanProducts,
  getLoanRepayments,
  getLoanReports,
  getLoanRequestById,
  getLoanRequestReports,
  getLoanRequests,
  getOrganization,
  getOrganizationLimitReports,
  getOrganizations,
  getRequestCheckReport,
  getValidationsByProductId,
  initiateOverrideValidationCheck,
  makeCreateOrganization,
  makeUpdateOrganization,
  rejectCancelLoanRequest,
  rejectOrganizationRequest,
  removeProductFromBroker,
  rerunCheck,
  superOverrideValidationCheck,
  updateBroker,
  updateCustomerProfile,
  updateLoanProduct,
  updateOrganization,
  updateProductCategory,
  uploadDocument,
} from '@dtbx/store/actions'
import { setNotification } from '@dtbx/store/reducers'
import { secureapi2 } from '../../../packages/store/src/utils/api'
import {
  setAssignBrokerProductFailure,
  setAssignBrokerProductLoading,
  setAssignBrokerProductSuccess,
  setBankBranches,
  setBrokerProducts,
  setBrokers,
  setCreateBrokerFailure,
  setCreateBrokerSuccess,
  setCreatedBroker,
  setCreatedProduct,
  setCustomerChecks,
  setCustomerDocuments,
  setCustomerProfile,
  setEditOrganization,
  setGenerateBrokerSecretFailure,
  setGenerateBrokerSecretSuccess,
  setGeneratedLoanReportLoading,
  setGeneratedLoanReportSuccess,
  setGeneratedRequestReportLoading,
  setIsCheckRerunLoading,
  setIsGenerateBrokerSecretLoading,
  setIsLoadingAddValidation,
  setIsLoadingBrokers,
  setIsLoadingCancelRequest,
  setIsLoadingCreateCategory,
  setIsLoadingCreateProduct,
  setIsLoadingCustomerProfile,
  setIsLoadingEditOrganization,
  setIsLoadingLoanProducts,
  setIsLoadingOrganizations,
  setIsLoadingOverrideValidationCheck,
  setIsLoadingProductValidations,
  setIsLoadingUpdateCategory,
  setIsLoadingUpdateProduct,
  setIsLoadingUploadDocument,
  setLoadingCreateBroker,
  setLoadingCreateOrganization,
  setLoadingLoans,
  setLoadingUpdateBroker,
  setLoanProducts,
  setLoanProductsSummary,
  setLoanRepaymentHistory,
  setLoanReportLoading,
  setLoanReports,
  setLoanReportsSuccess,
  setLoanRequestReportLoading,
  setLoanRequestReportsResponse,
  setLoanRequestReportSuccess,
  setLoanRequests,
  setLoanRequestsSummary,
  setLoanValidations,
  setOrganizationLimitReport,
  setOrganizationLimitReportExportLoading,
  setOrganizationLimitReportExportSuccess,
  setOrganizationLimitReportLoading,
  setOrganizationLimitReportSuccess,
  setOrganizations,
  setOrganizationsSummary,
  setProductCategories,
  setProductCategoryTypes,
  setProductToView,
  setProductValidations,
  setRequestCheckReportsExportLoading,
  setRequestCheckReportsExportSuccess,
  setRequestCheckReportsLoading,
  setRequestCheckReportsResponse,
  setRequestCheckReportsSuccess,
  setSelectedRequest,
  setSingleProductValidations,
  setUpdateBrokerFailure,
  setUpdateBrokerSuccess,
  setUserProducts,
} from '@dtbx/store/reducers'
import { setApprovalActionsLMS } from '@dtbx/store/reducers'

import {
  customerChecksStub,
  customerDocumentsStub,
  customerLoanProfileStub,
  decodedTokenStub,
  loanProductCreateStub,
  loanProductStub,
  loanRequestsSummaryStub,
  organizationMakeStub,
  productCategoryStub,
  repaymentHistoryStub,
} from '../../../packages/store/__tests__/stubs/loanStubs'

describe.skip('loanActions', () => {
  describe('loanActions', () => {})
  describe('getLoanRequests', () => {
    it('should dispatch loading state, fetch loan requests, and dispatch the result', async () => {
      const dispatch = vi.fn()
      const mockResponse = {
        data: {
          data: [loanProductStub],
          pageNumber: 1,
          pageSize: 10,
          totalNumberOfPages: 1,
          totalElements: 1,
        },
      }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanRequests(dispatch, '')

      //Assertions
      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(false))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRequests(mockResponse.data.data)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRequestsSummary({
          pageNumber: 1,
          pageSize: 10,
          totalNumberOfPages: 1,
          totalElements: 1,
        })
      )
    })

    it('should not dispatch loan requests when an error is encountered', async () => {
      const dispatch = vi.fn()
      vi.spyOn(secureapi2, 'get').mockRejectedValue(new Error('API Error'))
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      await getLoanRequests(dispatch, '')

      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(false))
      expect(dispatch).not.toHaveBeenCalledWith(setLoanRequests([]))
      expect(dispatch).not.toHaveBeenCalledWith(
        setLoanRequests(expect.anything())
      )
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching loan requests: ',
        expect.any(Error)
      )
    })

    it('should use default product ID when no params are passed', async () => {
      const dispatch = vi.fn()
      const mockResponse = {
        data: {
          data: [loanProductStub],
          pageNumber: 1,
          pageSize: 10,
          totalNumberOfPages: 1,
          totalElements: 1,
        },
      }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanRequests(dispatch, '')

      expect(secureapi2.get).toHaveBeenCalledWith(
        '/lms/loan-requests?productId=7d8dbd81-4b7e-4418-a354-0b4c387da5b4'
      )
    })

    it('should handle empty string parameters correctly', async () => {
      const dispatch = vi.fn()
      const mockResponse = {
        data: {
          data: [loanProductStub],
          pageNumber: 1,
          pageSize: 10,
          totalNumberOfPages: 1,
          totalElements: 1,
        },
      }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanRequests(dispatch, '')

      expect(secureapi2.get).toHaveBeenCalledWith(
        '/lms/loan-requests?productId=7d8dbd81-4b7e-4418-a354-0b4c387da5b4'
      )
    })

    it('should dispatch loading states with invalid params', async () => {
      const dispatch = vi.fn()
      const mockResponse = {
        data: {
          data: [],
          pageNumber: 0,
          pageSize: 10,
          totalNumberOfPages: 0,
          totalElements: 0,
        },
      }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanRequests(dispatch, 'invalidParam')

      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(true))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRequests(mockResponse.data.data)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRequestsSummary({
          pageNumber: 0,
          pageSize: 10,
          totalNumberOfPages: 0,
          totalElements: 0,
        })
      )
    })
  })

  describe('getLoanRequestById', () => {
    it('should dispatch loading state to true, fetch loan requests, return the result, and set loading state to false', async () => {
      const dispatch = vi.fn()
      const mockResponse = {
        data: {
          data: [loanProductStub],
          pageNumber: 1,
          pageSize: 10,
          totalNumberOfPages: 1,
          totalElements: 1,
        },
      }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)
      await getLoanRequestById(dispatch, '12345')
      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(true))
      expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(false))
      expect(dispatch).toHaveBeenCalledWith(
        setSelectedRequest(mockResponse.data.data[0])
      )
    }),
      it('should not dispatch loan requests when an error is encountered', async () => {
        const dispatch = vi.fn()
        vi.spyOn(secureapi2, 'get').mockRejectedValue(new Error('API error'))
        await getLoanRequestById(dispatch, '12345')

        expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(true))
        expect(dispatch).toHaveBeenCalledWith(setLoadingLoans(false))
        expect(dispatch).not.toHaveBeenCalledWith(
          setSelectedRequest(expect.anything())
        )
        expect(console.error).toHaveBeenCalledWith(
          'Error fetching loan request by id: ',
          expect.any(Error)
        )
      })
  })

  it('should dispatch customer profile and related data when API call is successful', async () => {
    const dispatch = vi.fn()
    const customerId = '123'
    const requestId = '456'
    const mockProfileData = customerLoanProfileStub
    const mockDocumentsData = [customerDocumentsStub]
    const mockChecksData = [customerChecksStub]

    secureapi2.get = vi
      .fn()
      .mockResolvedValueOnce({ data: mockProfileData })
      .mockResolvedValueOnce({ data: mockDocumentsData })
      .mockResolvedValueOnce({ data: mockChecksData })

    await getLoanCustomerProfile(dispatch, customerId, requestId)

    expect(dispatch).toHaveBeenCalledWith(setIsLoadingCustomerProfile(true))
    expect(dispatch).toHaveBeenCalledWith(setCustomerProfile(mockProfileData))
    expect(dispatch).toHaveBeenCalledWith(
      setCustomerDocuments(mockDocumentsData)
    )
    expect(dispatch).toHaveBeenCalledWith(setCustomerChecks(mockChecksData))
    expect(dispatch).toHaveBeenCalledWith(setIsLoadingCustomerProfile(false))
  })

  describe('getCustomerDocuments', () => {
    it('should dispatch setCustomerDocuments with data when API call is successful', async () => {
      const dispatch = vi.fn()
      const customerId = 'validCustomerId'
      const mockData = [customerDocumentsStub]

      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      await getCustomerDocuments(dispatch, customerId)

      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/customers/${customerId}/documents`
      )
      expect(dispatch).toHaveBeenCalledWith(setCustomerDocuments(mockData))
    })
    it('should handle API error gracefully and log an error when API call fails', async () => {
      const dispatch = vi.fn()
      const customerId = '123'
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})
      secureapi2.get = vi.fn().mockRejectedValue(new Error('API error'))
      await getCustomerDocuments(dispatch, customerId)
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching customer documents: ',
        expect.any(Error)
      )
      consoleErrorSpy.mockRestore()
    })
  })
  describe('getCustomerDocumentsByRequestId', () => {
    it('should successfully fetch customer documents when a valid requestId is provided', async () => {
      const dispatch = vi.fn()
      const requestId = 'validRequestId'
      const mockDocuments = [customerDocumentsStub]
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockDocuments })
      await getCustomerDocumentsByRequestId(dispatch, requestId)
      expect(dispatch).toHaveBeenCalledWith(setCustomerDocuments(mockDocuments))
      expect(dispatch).toHaveBeenCalledWith(setCustomerDocuments([]))
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/documents`
      )
      expect(dispatch).toHaveBeenCalledWith(setCustomerDocuments(mockDocuments))
    })
    it('should handle API errors gracefully', async () => {
      const dispatch = vi.fn()
      const requestId = 'invalidRequestId'
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      secureapi2.get = vi.fn().mockRejectedValue(new Error('API error'))

      await getCustomerDocumentsByRequestId(dispatch, requestId)

      expect(dispatch).toHaveBeenCalledWith(setCustomerDocuments([]))
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/documents`
      )
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching customer documents by request id: ',
        expect.any(Error)
      )
      expect(dispatch).toHaveBeenCalledTimes(1)
    })
  })

  describe('getCustomerChecks', () => {
    it('should dispatch setCustomerChecks with data when requestId is valid', async () => {
      const dispatch = vi.fn()
      const requestId = 'validRequestId'
      const mockData = [customerChecksStub]

      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      await getCustomerChecks(dispatch, requestId)

      expect(dispatch).toHaveBeenCalledWith(setCustomerChecks([]))
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/checks`
      )
      expect(dispatch).toHaveBeenCalledWith(setCustomerChecks(mockData))
    })
    it('should log an error when requestId is invalid', async () => {
      const dispatch = vi.fn()
      const requestId = 'invalidRequestId'
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})

      secureapi2.get = vi.fn().mockRejectedValue(new Error('Invalid requestId'))

      await getCustomerChecks(dispatch, requestId)

      expect(dispatch).toHaveBeenCalledWith(setCustomerChecks([]))
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/checks`
      )
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching customer checks: ',
        expect.any(Error)
      )

      consoleErrorSpy.mockRestore()
    })
  })
  describe('getLoanRepayments', () => {
    it('should dispatch setLoanRepaymentHistory with repayments data when loanId is valid and fetch successful', async () => {
      const dispatch = vi.fn()
      const loanId = 'valid-loan-id'
      const mockResponse = {
        data: { repayments: repaymentHistoryStub },
      }

      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getLoanRepayments(dispatch, loanId)

      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loans/${loanId}/repayments`
      )
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRepaymentHistory(mockResponse.data.repayments)
      )
    }),
      it('should log an error message when the API fails', async () => {
        const dispatch = vi.fn()
        const loanId = 'valid-loan-id'
        const consoleErrorSpy = vi
          .spyOn(console, 'error')
          .mockImplementation(() => {})
        const mockError = new Error('Network Error')

        secureapi2.get = vi.fn().mockRejectedValue(mockError)

        await getLoanRepayments(dispatch, loanId)

        expect(secureapi2.get).toHaveBeenCalledWith(
          `/lms/loans/${loanId}/repayments`
        )
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Error fetching loan repayments: ',
          expect.any(Error)
        )
        consoleErrorSpy.mockRestore()
      })
  })

  /*********************************** LOAN PRODUCTS ACTIONS ***********************************/

  describe('getLoanProducts', () => {
    it('should successfully fetch loan products and update state with product data', async () => {
      const dispatch = vi.fn()
      const mockData = {
        data: [loanProductStub],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      }
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      await getLoanProducts(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanProducts(mockData.data))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanProductsSummary({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    }),
      it('should update loan products summary in state when API call is successful', async () => {
        const dispatch = vi.fn()
        const mockData = {
          data: [loanProductStub],
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        }
        secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

        await getLoanProducts(dispatch)

        expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
        expect(dispatch).toHaveBeenCalledWith(setLoanProducts(mockData.data))
        expect(dispatch).toHaveBeenCalledWith(
          setLoanProductsSummary({
            pageNumber: 1,
            pageSize: 10,
            totalElements: 1,
            totalNumberOfPages: 1,
          })
        )
        expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      })
    // it('should decode JWT token and filter user-specific products when user has access', async () => {
    //   const dispatch = vi.fn()
    //   const mockData = {
    //     data: [loanProductStub],
    //     pageNumber: 1,
    //     pageSize: 10,
    //     totalElements: 1,
    //     totalNumberOfPages: 1,
    //   }
    //   secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })
    //   const mockAccessToken = 'mockAccessToken'
    //   localStorage.getItem = vi.fn().mockReturnValue(mockAccessToken)
    //   const mockDecodedToken = decodedTokenStub

    //   vi.mock('jwt-decode', () => ({
    //     jwtDecode: vi.fn(() => mockDecodedToken),
    //   }))

    //   const jwtDecodeSpy = vi.spyOn(await import('jwt-decode'), 'jwtDecode')

    //   await getLoanProducts(dispatch)

    //   expect(jwtDecodeSpy).toHaveBeenCalledWith(mockAccessToken)
    //   expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
    //   expect(dispatch).toHaveBeenCalledWith(setLoanProducts(mockData.data))
    //   expect(dispatch).toHaveBeenCalledWith(
    //     setLoanProductsSummary({
    //       pageNumber: 1,
    //       pageSize: 10,
    //       totalElements: 1,
    //       totalNumberOfPages: 1,
    //     })
    //   )
    //   expect(jwtDecodeSpy).toHaveBeenCalledWith(mockAccessToken)
    //   expect(dispatch).toHaveBeenCalledWith(setUserProducts([loanProductStub]))
    //   expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    //   jwtDecodeSpy.mockRestore()
    // })
    // it('should handle valid access token and update state with user products', async () => {
    //   const dispatch = vi.fn()
    //   const mockData = {
    //     data: [loanProductStub],
    //     pageNumber: 1,
    //     pageSize: 10,
    //     totalElements: 1,
    //     totalNumberOfPages: 1,
    //   }
    //   secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })
    //   localStorage.getItem = vi.fn().mockReturnValue('mockAccessToken')
    //   jwtDecode = vi.fn().mockReturnValue({ resources: [{ resourceIds: [1] }] })

    //   await getLoanProducts(dispatch)

    //   expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
    //   expect(dispatch).toHaveBeenCalledWith(setLoanProducts(mockData.data))
    //   expect(dispatch).toHaveBeenCalledWith(
    //     setLoanProductsSummary({
    //       pageNumber: 1,
    //       pageSize: 10,
    //       totalElements: 1,
    //       totalNumberOfPages: 1,
    //     })
    //   )
    //   expect(dispatch).toHaveBeenCalledWith(setUserProducts([loanProductStub]))
    //   expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    // })

    it('should toggle loading state correctly before and after API call', async () => {
      const dispatch = vi.fn()
      const mockData = {
        data: [loanProductStub],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      }
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      await getLoanProducts(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanProducts(mockData.data))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanProductsSummary({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })

    it('should not set user products when accessToken is not present in localStorage', async () => {
      const dispatch = vi.fn()
      const mockData = {
        data: [loanProductStub],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      }
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })
      localStorage.getItem = vi.fn().mockReturnValue(null)

      await getLoanProducts(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanProducts(mockData.data))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanProductsSummary({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        })
      )
      expect(dispatch).not.toHaveBeenCalledWith(
        setUserProducts(expect.anything())
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })
    it('should handle network errors or API call failures gracefully', async () => {
      const dispatch = vi.fn()
      secureapi2.get = vi.fn().mockRejectedValue(new Error('Network Error'))

      await getLoanProducts(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })
  })

  describe('getLoanProductById', () => {
    it('should fetch loan product and update state when API call is successful', async () => {
      const mockDispatch = vi.fn()
      const mockResponse = { data: { id: '123', name: 'Loan Product' } }
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      const result = await getLoanProductById('123', mockDispatch)

      expect(secureapi2.get).toHaveBeenCalledWith('/lms/products/123')
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setProductToView(mockResponse.data)
      )
      expect(result).toEqual(mockResponse.data)
    }),
      it('should handle network errors gracefully without crashing', async () => {
        const mockDispatch = vi.fn()
        const mockError = new Error('Network Error')
        const consoleErrorSpy = vi
          .spyOn(console, 'error')
          .mockImplementation(() => {})
        secureapi2.get = vi.fn().mockRejectedValue(mockError)

        await getLoanProductById('123', mockDispatch)

        expect(secureapi2.get).toHaveBeenCalledWith('/lms/products/123')
        expect(mockDispatch).toHaveBeenCalledWith(
          setIsLoadingLoanProducts(true)
        )
        expect(mockDispatch).toHaveBeenCalledWith(
          setIsLoadingLoanProducts(false)
        )
        expect(consoleErrorSpy).toHaveBeenCalledWith(
          'Error fetching loan product: ',
          mockError
        )
      })
  })

  describe('createLoanProduct', () => {
    it('should dispatch success notification and set created product when loan product is created successfully', async () => {
      const mockDispatch = vi.fn()
      const mockData = loanProductCreateStub
      const mockResponse = { data: loanProductStub }
      secureapi2.post = vi.fn().mockResolvedValue(mockResponse)
      await createLoanProduct(mockData, mockDispatch)
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingCreateProduct(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingCreateProduct(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loan product created successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setCreatedProduct(mockResponse.data)
      )
    }),
      it('should dispatch error notification when API call fails', async () => {
        const mockDispatch = vi.fn()
        const mockData = loanProductCreateStub
        const mockError = new Error('API Error')
        secureapi2.post = vi.fn().mockRejectedValue(mockError)
        await createLoanProduct(mockData, mockDispatch)
        expect(mockDispatch).toHaveBeenCalledWith(
          setIsLoadingCreateProduct(true)
        )
        expect(mockDispatch).toHaveBeenCalledWith(
          setNotification({
            message: mockError.message,
            type: 'error',
          })
        )
        expect(mockDispatch).toHaveBeenCalledWith(
          setIsLoadingCreateProduct(false)
        )
      })
  })
  describe('updateLoanProduct', () => {
    it('should update loan product and dispatch success notification when data is valid', async () => {
      const mockDispatch = vi.fn()
      const mockData = loanProductCreateStub
      const mockProductId = 'product123'
      const mockResponse = { data: loanProductStub }
      const getLoanProductById = vi.fn()

      secureapi2.put = vi.fn().mockResolvedValue(mockResponse)
      await getLoanProductById(mockProductId, mockDispatch)

      const result = await updateLoanProduct(
        mockProductId,
        mockData,
        mockDispatch
      )

      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/products/${mockProductId}`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingUpdateProduct(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loan product updated successfully',
          type: 'success',
        })
      )
      expect(getLoanProductById).toHaveBeenCalledWith(
        mockProductId,
        mockDispatch
      )
      expect(result).toEqual(mockResponse.data)
    })

    it('should dispatch error notification when network error occurs', async () => {
      const mockDispatch = vi.fn()
      const mockData = loanProductCreateStub
      const mockProductId = 'product123'
      const mockError = new Error('Network Error')
      secureapi2.put = vi.fn().mockRejectedValue(mockError)

      await updateLoanProduct('product123', mockData, mockDispatch)

      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/products/${mockProductId}`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingUpdateProduct(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
    })
  })

  /********************************* Product Categories API *************************/
  describe('getLoanProductCategories', () => {
    it('should dispatch loading state before API call', async () => {
      const dispatch = vi.fn()
      await getLoanProductCategories(dispatch)
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
    })
    it('should dispatch loading state, fetch product categories from API, and dispatch loaded state', async () => {
      const dispatch = vi.fn()
      const mockResponse = { data: 'mocked data' }
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getLoanProductCategories(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/product-categories')
      expect(dispatch).toHaveBeenCalledWith(
        setProductCategories(mockResponse.data)
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })
    it('should dispatch loading state, fetch product categories, and dispatch them to the store', async () => {
      const dispatch = vi.fn()
      const mockResponse = { data: 'mock product categories' }
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getLoanProductCategories(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/product-categories')
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      expect(dispatch).toHaveBeenCalledWith(
        setProductCategories(mockResponse.data)
      )
    })
    it('should dispatch loading state off after successful API call', async () => {
      const dispatch = vi.fn()
      const response = { data: 'mocked data' }
      secureapi2.get = vi.fn().mockResolvedValue(response)

      await getLoanProductCategories(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      expect(dispatch).toHaveBeenCalledWith(setProductCategories(response.data))
    })
    it('should reset loading state after API call failure', async () => {
      const dispatch = vi.fn()
      const error = new Error('API call failed')
      secureapi2.get = vi.fn().mockRejectedValue(error)

      await getLoanProductCategories(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })
    it('should log error and dispatch loading state when API call fails', async () => {
      const dispatch = vi.fn()
      const error = new Error('API call failed')
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})
      secureapi2.get = vi.fn().mockRejectedValue(error)

      await getLoanProductCategories(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching product categories: ',
        error
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
    })
  })

  describe('createProductCategory', () => {
    it('should create a product category and dispatch success notification when valid data is provided', async () => {
      const dispatch = vi.fn()
      const data = { code: 'PC001', name: 'Personal Loan' }
      const getLoanProductCategories = vi.fn()

      secureapi2.post = vi.fn().mockResolvedValue({})

      await createProductCategory(dispatch, data)
      await getLoanProductCategories(dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateCategory(true))
      expect(secureapi2.post).toHaveBeenCalledWith(
        '/lms/product-categories',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateCategory(false))
      expect(getLoanProductCategories).toHaveBeenCalledWith(dispatch)
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Product category created successfully',
          type: 'success',
        })
      )
    })
    it('should handle network errors and dispatch error notification', async () => {
      const dispatch = vi.fn()
      const data = { code: 'PC002', name: 'Business Loan' }
      const errorMessage = 'Network Error'
      secureapi2.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await createProductCategory(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateCategory(true))
      expect(secureapi2.post).toHaveBeenCalledWith(
        '/lms/product-categories',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCreateCategory(false))
    })
  })

  describe('updateProductCategory', () => {
    it('should update product category and dispatch success notification when API call is successful', async () => {
      const dispatch = vi.fn()
      const data = { name: 'New Category' }
      const id = '123'
      const getLoanProductCategories = vi.fn()

      secureapi2.put = vi.fn().mockResolvedValue({})
      await getLoanProductCategories(dispatch)

      await updateProductCategory(dispatch, data, id)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUpdateCategory(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/product-categories/${id}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUpdateCategory(false))
      expect(getLoanProductCategories).toHaveBeenCalledWith(dispatch)
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Product category updated successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const data = { name: 'New Category' }
      const id = '123'
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await updateProductCategory(dispatch, data, id)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUpdateCategory(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/product-categories/${id}`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingUpdateCategory(false))
    })
  })
  describe('getLoanProductCategoryTypes', () => {
    it('should fetch product category types successfully when given a valid categoryId', async () => {
      const dispatch = vi.fn()
      const categoryId = 'validCategoryId'
      const mockData = [productCategoryStub]

      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      const result = await getLoanProductCategoryTypes(dispatch, categoryId)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/product-categories/${categoryId}/types`
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      expect(dispatch).toHaveBeenCalledWith(setProductCategoryTypes(mockData))
      expect(result).toEqual(mockData)
    })
    it('should handle network errors gracefully without crashing', async () => {
      const dispatch = vi.fn()
      const categoryId = 'invalidCategoryId'

      secureapi2.get = vi.fn().mockRejectedValue(new Error('Network Error'))

      await getLoanProductCategoryTypes(dispatch, categoryId)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(true))
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/product-categories/${categoryId}/types`
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingLoanProducts(false))
      expect(console.error).toHaveBeenCalledWith(
        'Error fetching product category types: ',
        expect.any(Error)
      )
    })
  })

  /*************************************** Organization API tests **************************/
  describe('getOrganizations', () => {
    it('should fetch organizations data and update state when API call is successful', async () => {
      const dispatch = vi.fn()
      const params = 'page=1'
      const mockData = {
        data: [{ id: 1, name: 'Org1' }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      }

      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      const result = await getOrganizations(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingOrganizations(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/organizations?page=1')
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingOrganizations(false))
      expect(dispatch).toHaveBeenCalledWith(setOrganizations(mockData.data))
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationsSummary({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        })
      )
      expect(result).toEqual(mockData)
    })
    it('should handle empty params gracefully and still fetch organizations', async () => {
      const dispatch = vi.fn()
      const params = ''
      const mockData = {
        data: [{ id: 1, name: 'Org1' }],
        pageNumber: 1,
        pageSize: 10,
        totalElements: 1,
        totalNumberOfPages: 1,
      }

      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      const result = await getOrganizations(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingOrganizations(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/organizations?')
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingOrganizations(false))
      expect(dispatch).toHaveBeenCalledWith(setOrganizations(mockData.data))
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationsSummary({
          pageNumber: 1,
          pageSize: 10,
          totalElements: 1,
          totalNumberOfPages: 1,
        })
      )
      expect(result).toEqual(mockData)
    })
    it('should handle network errors gracefully without crashing', async () => {
      const dispatch = vi.fn()
      const error = new Error('Network Error')
      secureapi2.get = vi.fn().mockRejectedValue(error)
      console.error = vi.fn()

      await getOrganizations(dispatch, 'page=1')

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingOrganizations(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/organizations?page=1')
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingOrganizations(false))
      expect(console.error).toHaveBeenCalledWith(
        'Error fetching organizations: ',
        error
      )
    })
  })

  describe('createOrganization', () => {
    it('should create an organization successfully when valid data is provided', async () => {
      const dispatch = vi.fn()
      const data = {
        name: 'Test Organization',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Test Bank',
        bankCode: '123',
        status: 'active',
        swiftCode: 'TSTB123',
        bankAccountNumber: '*********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Test Country',
          town: 'Test Town',
          physicalAddress: '123 Test Street',
        },
        limit: 100000,
        limitCurrency: 'USD',
      }

      secureapi2.post = vi.fn().mockResolvedValue({ data: { id: 1 } })

      const result = await createOrganization(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(true))
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/organizations', data)
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization created successfully',
          type: 'success',
        })
      )
      expect(result).toEqual({ id: 1 })
    })
    it('should dispatch success notification upon successful organization creation', async () => {
      const dispatch = vi.fn()
      const data = {
        name: 'Test Organization',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Test Bank',
        bankCode: '123',
        status: 'active',
        swiftCode: 'TSTB123',
        bankAccountNumber: '*********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Test Country',
          town: 'Test Town',
          physicalAddress: '123 Test Street',
        },
        limit: 100000,
        limitCurrency: 'USD',
      }

      secureapi2.post = vi.fn().mockResolvedValue({ data: { id: 1 } })

      const result = await createOrganization(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(true))
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/organizations', data)
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization created successfully',
          type: 'success',
        })
      )
      expect(result).toEqual({ id: 1 })
    })

    it('should set loading state to false after successful organization creation', async () => {
      const dispatch = vi.fn()
      const data = {
        name: 'Test Organization',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Test Bank',
        bankCode: '123',
        status: 'active',
        swiftCode: 'TSTB123',
        bankAccountNumber: '*********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Test Country',
          town: 'Test Town',
          physicalAddress: '123 Test Street',
        },
        limit: 100000,
        limitCurrency: 'USD',
      }

      secureapi2.post = vi.fn().mockResolvedValue({ data: { id: 1 } })

      const result = await createOrganization(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(true))
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/organizations', data)
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization created successfully',
          type: 'success',
        })
      )
      expect(result).toEqual({ id: 1 })
    })

    it('should handle network errors gracefully during organization creation', async () => {
      const dispatch = vi.fn()
      const data = {
        name: 'Test Organization',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Test Bank',
        bankCode: '123',
        status: 'active',
        swiftCode: 'TSTB123',
        bankAccountNumber: '*********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Test Country',
          town: 'Test Town',
          physicalAddress: '123 Test Street',
        },
        limit: 100000,
        limitCurrency: 'USD',
      }

      const errorMessage = 'Network Error'
      secureapi2.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await createOrganization(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(true))
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/organizations', data)
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(false))
    })
  })
  describe('updateOrganization', () => {
    it('should dispatch success notification when organization is updated successfully', async () => {
      const dispatch = vi.fn()
      const orgId = '123'
      const params = organizationMakeStub

      secureapi2.put = vi.fn().mockResolvedValue({})

      await updateOrganization(orgId, dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/${orgId}`,
        params
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization was successfully updated',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
    it('should set loading state correctly during the update process', async () => {
      const dispatch = vi.fn()
      const orgId = '123'
      const params = { cbsIdentifier: '456', comments: 'Test comment' }

      secureapi2.put = vi.fn().mockResolvedValue({})

      await updateOrganization(orgId, dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/${orgId}`,
        params
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization was successfully updated',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
    it('should dispatch error notification when network error occurs', async () => {
      const dispatch = vi.fn()
      const orgId = '123'
      const params = { cbsIdentifier: '456', comments: 'Test comment' }
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await updateOrganization(orgId, dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/${orgId}`,
        params
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
  })

  describe('makeCreateOrganization', () => {
    it('should dispatch success notification when organization is created successfully', async () => {
      const dispatch = vi.fn()
      const data = { name: 'Test Organization' }
      secureapi2.post = vi.fn().mockResolvedValueOnce({})

      await makeCreateOrganization(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(true))
      expect(secureapi2.post).toHaveBeenCalledWith(
        '/lms/organizations/make',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization created successfully, awaiting approval',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(false))
    })
    it('should dispatch error notification when network error occurs', async () => {
      const dispatch = vi.fn()
      const data = { name: 'Test Organization' }
      const errorMessage = 'Network Error'
      secureapi2.post = vi.fn().mockRejectedValueOnce(new Error(errorMessage))

      await makeCreateOrganization(dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(true))
      expect(secureapi2.post).toHaveBeenCalledWith(
        '/lms/organizations/make',
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateOrganization(false))
    })
  })

  describe('getOrganization', () => {
    it('should dispatch setEditOrganization with data when orgID is valid', async () => {
      const mockDispatch = vi.fn()
      const mockData = { id: '123', name: 'Test Organization' }
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      await getOrganization('123', mockDispatch)

      expect(secureapi2.get).toHaveBeenCalledWith('/lms/organizations/123')
      expect(mockDispatch).toHaveBeenCalledWith(setEditOrganization(mockData))
    })
    it('should log an error when orgID is invalid', async () => {
      const mockDispatch = vi.fn()
      const consoleErrorSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {})
      const mockError = new Error('Invalid orgID')
      secureapi2.get = vi.fn().mockRejectedValue(mockError)

      await getOrganization('invalid-id', mockDispatch)

      expect(secureapi2.get).toHaveBeenCalledWith(
        '/lms/organizations/invalid-id'
      )
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error fetching organizations: ',
        mockError
      )

      consoleErrorSpy.mockRestore()
    })
  })

  describe('makeUpdateOrganization', () => {
    it('should dispatch success notification when organization is updated successfully', async () => {
      const dispatch = vi.fn()
      const orgId = '123'
      const data = { cbsIdentifier: '456', comments: 'Test comment' }

      secureapi2.put = vi.fn().mockResolvedValue({})

      await makeUpdateOrganization(dispatch, orgId, data)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/${orgId}/make`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization updated successfully, awaiting approval',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
    it('should dispatch error notification when network request fails', async () => {
      const dispatch = vi.fn()
      const orgId = '123'
      const data = { cbsIdentifier: '456', comments: 'Test comment' }
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await makeUpdateOrganization(dispatch, orgId, data)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/${orgId}/make`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
  })

  describe('approveOrganizationRequest', () => {
    it('should dispatch success notification and update approvals when approvalId is valid', async () => {
      const dispatch = vi.fn()
      const approvalId = 'validApprovalId'
      secureapi2.put = vi.fn().mockResolvedValue({})
      const getApprovals = vi.fn()

      await approveOrganizationRequest(dispatch, approvalId)
      await getApprovals(dispatch, '?channel=LMS')

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/approve/${approvalId}`,
        { comments: 'Approved' }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization request was approved successfully',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
      expect(getApprovals).toHaveBeenCalledWith(dispatch, '?channel=LMS')
    })
    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const approvalId = 'validApprovalId'
      const errorMessage = 'Network Error'
      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await approveOrganizationRequest(dispatch, approvalId)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/approve/${approvalId}`,
        { comments: 'Approved' }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
  })
  describe('rejectOrganizationRequest', () => {
    it('should dispatch success notification and update approvals when rejection is successful', async () => {
      const dispatch = vi.fn()
      const approvalID = 'valid-approval-id'

      secureapi2.put = vi.fn().mockResolvedValue({})
      const getApprovals = vi.fn().mockResolvedValue({})

      await rejectOrganizationRequest(dispatch, approvalID)
      await getApprovals(dispatch, '?channel=LMS')

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/reject/${approvalID}`,
        { comments: 'Rejected' }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization request was rejected successfully',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
      expect(getApprovals).toHaveBeenCalledWith(dispatch, '?channel=LMS')
    })

    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const approvalID = 'valid-approval-id'
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await rejectOrganizationRequest(dispatch, approvalID)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/organizations/reject/${approvalID}`,
        { comments: 'Rejected' }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingEditOrganization(false))
    })
  })

  /************************** Overide Worldcheck APIs Tets ***********************************/

  describe('initiateOverrideValidationCheck', () => {
    it('should dispatch success notification and update customer checks when API call is successful', async () => {
      const mockDispatch = vi.fn()
      const mockData = { id: '1', status: 'approved', comments: 'All good' }
      const mockRequestId = 'request123'
      const mockCheckId = 'testId123'
      const mockResponseData = { success: true }

      secureapi2.put = vi.fn().mockResolvedValue({ data: mockResponseData })
      const getCustomerChecks = vi.fn()

      const result = await initiateOverrideValidationCheck(
        mockData,
        mockRequestId,
        mockCheckId,
        mockDispatch
      )
      await getCustomerChecks(mockDispatch, mockRequestId)

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${mockRequestId}/checks/${mockCheckId}/override/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'PaymentDetails check override initiated successfully',
          type: 'success',
        })
      )
      expect(getCustomerChecks).toHaveBeenCalledWith(
        mockDispatch,
        mockRequestId
      )
      expect(result).toEqual(mockResponseData)
    })
    it('should dispatch error notification with error message when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockData = { id: '1', status: 'approved', comments: 'All good' }
      const mockRequestId = 'request123'
      const mockCheckId = 'check456'
      const mockError = new Error('API call failed')

      secureapi2.put = vi.fn().mockRejectedValue(mockError)

      await initiateOverrideValidationCheck(
        mockData,
        mockRequestId,
        mockCheckId,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${mockRequestId}/checks/${mockCheckId}/override/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: 'API call failed', type: 'error' })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockData = { id: '1', status: 'approved', comments: 'All good' }
      const mockRequestId = 'request123'
      const mockCheckId = 'check456'
      const mockErrorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(mockErrorMessage))

      await initiateOverrideValidationCheck(
        mockData,
        mockRequestId,
        mockCheckId,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${mockRequestId}/checks/${mockCheckId}/override/make`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: mockErrorMessage, type: 'error' })
      )
    })
  })

  describe('superOverrideValidationCheck', () => {
    it('should dispatch success notification and update state when validation check override is successful', async () => {
      const mockDispatch = vi.fn()
      const mockData = { id: '1', status: 'approved', comments: 'All good' }
      const mockRequestId = 'request123'
      const mockCheckId = 'check456'
      const mockResponseData = { success: true }

      secureapi2.put = vi.fn().mockResolvedValue({ data: mockResponseData })
      const getCustomerChecks = vi.fn()

      const result = await superOverrideValidationCheck(
        mockData,
        mockRequestId,
        mockCheckId,
        mockDispatch
      )
      await getCustomerChecks(mockDispatch, mockRequestId)

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${mockRequestId}/checks/${mockCheckId}/override`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'PaymentDetails check override was successful',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
      expect(getCustomerChecks).toHaveBeenCalledWith(
        mockDispatch,
        mockRequestId
      )
      expect(result).toEqual(mockResponseData)
    })
    it('should dispatch error notification and stop loading when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockData = {
        id: '1',
        status: 'approved',
        comments: 'All good',
      }
      const mockRequestId = 'request123'
      const mockCheckId = 'check456'
      const mockErrorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(mockErrorMessage))

      await superOverrideValidationCheck(
        mockData,
        mockRequestId,
        mockCheckId,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${mockRequestId}/checks/${mockCheckId}/override`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({ message: mockErrorMessage, type: 'error' })
      )
    })
  })
  describe('completeValidationOverride', () => {
    it('should dispatch success notification when approval is successful', async () => {
      const mockDispatch = vi.fn()
      const mockData = { comments: 'Approved' }
      const mockAction = 'approve'
      const mockApprovalId = '12345'
      const mockResponse = { data: { result: 'success' } }

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce(mockResponse)

      await completeValidationOverride(
        mockData,
        mockAction,
        mockApprovalId,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/checks/override/${mockAction}/${mockApprovalId}`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'World check override was approved successfully',
          type: 'success',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const mockDispatch = vi.fn()
      const mockData = { comments: 'Rejected' }
      const mockAction = 'reject'
      const mockApprovalId = '67890'
      const mockError = new Error('Network Error')

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(mockError)

      await completeValidationOverride(
        mockData,
        mockAction,
        mockApprovalId,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(true)
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/checks/override/${mockAction}/${mockApprovalId}`,
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Network Error',
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingOverrideValidationCheck(false)
      )
    })
  })

  describe('rerunCheck', () => {
    it('should return response data when check rerun is successful', async () => {
      const dispatch = vi.fn()
      const requestId = '123'
      const data = { checkId: '456' }
      const responseData = { success: true }

      secureapi2.put = vi.fn().mockResolvedValue({ data: responseData })

      const result = await rerunCheck(requestId, data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsCheckRerunLoading(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/check-rerun`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsCheckRerunLoading(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Checks rerun successfully',
          type: 'success',
        })
      )
      expect(result).toEqual(responseData)
    })
    it('should handle error and dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const requestId = '123'
      const data = { checkId: '456' }
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await rerunCheck(requestId, data, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setIsCheckRerunLoading(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/check-rerun`,
        data
      )
      expect(dispatch).toHaveBeenCalledWith(setIsCheckRerunLoading(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
  /******************* Loan Validaton APIs ****************************************************/

  describe('getAllValidations', () => {
    it('should dispatch setLoanValidations with data when API call is successful', async () => {
      const mockDispatch = vi.fn()
      const mockData = { validations: ['validation1', 'validation2'] }
      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce({ data: mockData })

      await getAllValidations(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoanValidations(mockData))
    })
    it('should dispatch setLoanValidations with empty data when API returns empty response', async () => {
      const mockDispatch = vi.fn()
      const mockData = {}
      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce({ data: mockData })

      await getAllValidations(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setLoanValidations(mockData))
    })
  })
  describe('addProductValidation', () => {
    it('should return response data when product validation is added successfully', async () => {
      const mockDispatch = vi.fn()
      const mockData = [
        {
          stage: 'initial',
          validationId: '123',
          validDays: 30,
          customerType: 'new',
        },
      ]
      const mockProductId = 'product123'
      const mockResponseData = { success: true }

      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({
        data: mockResponseData,
      })

      const result = await addProductValidation(
        mockData,
        mockProductId,
        mockDispatch
      )

      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingAddValidation(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingAddValidation(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Validations added successfully',
          type: 'success',
        })
      )
      expect(result).toEqual(mockResponseData)
    })
    it('should handle network errors and dispatch error notification', async () => {
      const mockDispatch = vi.fn()
      const mockData = [
        {
          stage: 'initial',
          validationId: '123',
          validDays: 30,
          customerType: 'new',
        },
      ]
      const mockProductId = 'product123'
      const mockErrorMessage = 'Network Error'

      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(
        new Error(mockErrorMessage)
      )
      console.error = vi.fn()

      await addProductValidation(mockData, mockProductId, mockDispatch)

      expect(console.error).toHaveBeenCalledWith(
        'Error adding validation: ',
        expect.any(Error)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setIsLoadingAddValidation(true))
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingAddValidation(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockErrorMessage,
          type: 'error',
        })
      )
    })
  })
  describe('getValidationsByProductId', () => {
    it('should dispatch correct actions when fetching validations is successful', async () => {
      const mockDispatch = vi.fn()
      const mockResponseData = [{ id: 1, validation: 'PaymentDetails 1' }]
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockResponseData })

      await getValidationsByProductId('123', mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingProductValidations(true)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setSingleProductValidations([]))
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingProductValidations(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setSingleProductValidations(mockResponseData)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setProductValidations(mockResponseData)
      )
    })
    it('should handle errors gracefully when fetching validations fails', async () => {
      const mockDispatch = vi.fn()
      const mockError = new Error('Network Error')
      secureapi2.get = vi.fn().mockRejectedValue(mockError)

      await getValidationsByProductId('123', mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingProductValidations(true)
      )
      expect(mockDispatch).toHaveBeenCalledWith(setSingleProductValidations([]))
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingProductValidations(false)
      )
      expect(console.error).toHaveBeenCalledWith(
        'Error fetching product validations: ',
        mockError
      )
    })
  })

  describe('getBankBranches', () => {
    it('should dispatch setBankBranches with data when API call is successful', async () => {
      const mockDispatch = vi.fn()
      const mockData = [
        { id: 1, name: 'Branch 1' },
        { id: 2, name: 'Branch 2' },
      ]
      vi.spyOn(secureapi2, 'get').mockResolvedValue({ data: mockData })

      await getBankBranches(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setBankBranches(mockData))
    })
    it('should dispatch setBankBranches with empty array when API returns no data', async () => {
      const mockDispatch = vi.fn()
      vi.spyOn(secureapi2, 'get').mockResolvedValue({ data: [] })

      await getBankBranches(mockDispatch)

      expect(mockDispatch).toHaveBeenCalledWith(setBankBranches([]))
    })
  })
  /******************************** LMS Reports **********************************************/
  describe('getOrganizationLimitReports', () => {
    it('should dispatch success actions when fetching reports with valid parameters', async () => {
      const dispatch = vi.fn()
      const params = 'page=1&size=10&organizationName=TestOrg'
      const mockResponse = { data: { reports: [] } }

      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getOrganizationLimitReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportLoading(true)
      )
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loan-reports/organization-limit?${params}`
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReport(mockResponse.data)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportSuccess(true)
      )
    })
    it('should handle empty or null parameters without errors', async () => {
      const dispatch = vi.fn()
      const params = ''
      const mockResponse = { data: { reports: [] } }

      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getOrganizationLimitReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportLoading(true)
      )
      expect(secureapi2.get).toHaveBeenCalledWith(
        `/lms/loan-reports/organization-limit?`
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReport(mockResponse.data)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportSuccess(true)
      )
    })
  })

  describe('generateOrganizationLimitReport', () => {
    it('should dispatch success actions and download the report when API call is successful', async () => {
      const dispatch = vi.fn()
      const params = 'page=1&size=10'

      const mockResponse = new Blob(['mock data'], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })

      vi.spyOn(secureapi2, 'get').mockResolvedValue({ data: mockResponse })
      const mockUrl = 'mock-url'
      global.URL.createObjectURL = vi.fn(() => mockUrl)

      const clickSpy = vi.fn()
      const mockAnchorElement = {
        href: '',
        download: '',
        click: clickSpy,
      }
      document.createElement = vi.fn().mockReturnValue(mockAnchorElement as any)

      await generateOrganizationLimitReport({ dispatch, params })
      mockAnchorElement.click()

      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportSuccess(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportSuccess(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Organization limit report generated successfully',
          type: 'success',
        })
      )

      expect(global.URL.createObjectURL).toHaveBeenCalledWith(mockResponse)
      expect(mockAnchorElement.href).toBe(mockUrl)
      expect(mockAnchorElement.download).toBe('Organization_Limit_Report.xlsx')

      expect(clickSpy).toHaveBeenCalled()
    })

    it('should dispatch error actions and notification when API call fails', async () => {
      const dispatch = vi.fn()
      const params = 'page=1&size=10'
      const errorMessage = 'Network Error'

      vi.spyOn(secureapi2, 'get').mockRejectedValue(new Error(errorMessage))

      await generateOrganizationLimitReport({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportSuccess(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setOrganizationLimitReportExportSuccess(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('getLoanRequestReports', () => {
    it('should dispatch success actions and notification when valid parameters are provided', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
        status: 'approved',
        organizationName: 'TestOrg',
        requestStartDate: '2023-01-01',
        requestEndDate: '2023-12-31',
      }
      const mockResponse = { data: { reports: [] } }
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getLoanRequestReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(true))
      expect(secureapi2.get).toHaveBeenCalledWith(
        expect.stringContaining(
          '/lms/loan-reports/requests?page=1&size=10&status=approved&organizationName=TestOrg&requestStartDate=2023-01-01&requestEndDate=2023-12-31'
        )
      )
      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(false))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRequestReportsResponse(mockResponse.data)
      )
      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Requests report was succesfully retrieved',
          type: 'success',
        })
      )
    })
    it('should handle missing optional parameters without errors', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
      }
      const mockResponse = { data: { reports: [] } }
      secureapi2.get = vi.fn().mockResolvedValue(mockResponse)

      await getLoanRequestReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(true))
      expect(secureapi2.get).toHaveBeenCalledWith(
        expect.stringContaining('/lms/loan-reports/requests?page=1&size=10')
      )
      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(false))
      expect(dispatch).toHaveBeenCalledWith(
        setLoanRequestReportsResponse(mockResponse.data)
      )
      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Requests report was succesfully retrieved',
          type: 'success',
        })
      )
    })
  })
  describe('generateLoanRequestReports', () => {
    it('should generate loan request reports successfully when valid parameters are provided', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
        status: 'approved',
        organizationName: 'TestOrg',
        requestStartDate: '2023-01-01',
        requestEndDate: '2023-12-31',
      }

      const mockResponse = new ArrayBuffer(8)
      secureapi2.get = vi.fn().mockResolvedValue({ data: mockResponse })
      const downloadBlob = vi.fn()

      await generateLoanRequestReports({ dispatch, params })
      await downloadBlob(Blob, 'Loan_Requests_Report.xlsx')

      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedRequestReportLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedRequestReportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(setGeneratedLoanReportSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Requests report was succesfully generated',
          type: 'success',
        })
      )
      //  expect(downloadBlob).toHaveBeenCalledWith(
      //    expect.any(Blob),
      //    'Loan_Requests_Report.xlsx'
      //  )
    })
    it('should handle API errors gracefully and notify user of failure', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
      }

      const errorMessage = 'Network Error'
      secureapi2.get = vi.fn().mockRejectedValue(new Error(errorMessage))

      await generateLoanRequestReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedRequestReportLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedRequestReportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedLoanReportSuccess(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
  describe('getLoanReports', () => {
    it('should retrieve loan reports successfully with all parameters', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 2,
        size: 20,
        status: 'approved',
        organizationName: 'TestOrg',
        creationStartDate: '2023-01-01',
        creationEndDate: '2023-12-31',
        maturityStartDate: '2024-01-01',
        maturityEndDate: '2024-12-31',
      }

      const mockResponse = { data: [{ id: 1, name: 'Loan 1' }] }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportLoading(false))
      expect(dispatch).toHaveBeenCalledWith(setLoanReports(mockResponse.data))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportsSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loans report was succesfully retrieved',
          type: 'success',
        })
      )
    })
    it('should retrieve loan reports successfully with missing optional parameters', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
        status: 'pending', // optional
        // other optional params are missing
      }

      const mockResponse = { data: [{ id: 2, name: 'Loan 2' }] }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportLoading(false))
      expect(dispatch).toHaveBeenCalledWith(setLoanReports(mockResponse.data))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportsSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loans report was succesfully retrieved',
          type: 'success',
        })
      )
    })
    it('should retrieve loan reports successfully with no optional parameters', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
      }

      const mockResponse = { data: [{ id: 3, name: 'Loan 3' }] }
      vi.spyOn(secureapi2, 'get').mockResolvedValue(mockResponse)

      await getLoanReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportLoading(false))
      expect(dispatch).toHaveBeenCalledWith(setLoanReports(mockResponse.data))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportsSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loans report was succesfully retrieved',
          type: 'success',
        })
      )
    })
    it('should handle errors when retrieving loan reports', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
      }

      const errorMessage = 'Failed to fetch reports'
      vi.spyOn(secureapi2, 'get').mockRejectedValue(new Error(errorMessage))

      await getLoanReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportLoading(false))
      expect(dispatch).toHaveBeenCalledWith(setLoanReportsSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
  describe('generateLoanReports', () => {
    it('should generate loan reports successfully when valid parameters are provided', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
        status: 'approved',
        organizationName: 'TestOrg',
        creationStartDate: '2023-01-01',
        creationEndDate: '2023-12-31',
        maturityStartDate: '2023-06-01',
        maturityEndDate: '2023-12-31',
      }

      secureapi2.get = vi.fn().mockResolvedValue({
        data: new ArrayBuffer(8),
      })

      await generateLoanReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setGeneratedLoanReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setGeneratedLoanReportSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loan report was succesfully generated',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedLoanReportLoading(false)
      )
    })
    it('should handle missing optional parameters gracefully', async () => {
      const dispatch = vi.fn()
      const params = {
        page: 1,
        size: 10,
      }

      secureapi2.get = vi.fn().mockResolvedValue({
        data: new ArrayBuffer(8),
      })

      await generateLoanReports({ dispatch, params })

      expect(dispatch).toHaveBeenCalledWith(setGeneratedLoanReportLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setGeneratedLoanReportSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loan report was succesfully generated',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGeneratedLoanReportLoading(false)
      )
    })
  })

  /**************************** document uploads on validation checks *************************/

  describe('uploadDocument', () => {
    it('should upload document and dispatch success notification when valid data is provided', async () => {
      const mockDispatch = vi.fn()
      const mockData = {
        documentType: 'ID',
        document: 'base64string',
        comments: 'Test document',
      }
      const mockResponse = { data: { success: true } }
      secureapi2.put = vi.fn().mockResolvedValue(mockResponse)

      const result = await uploadDocument(
        mockData,
        mockDispatch,
        '123',
        'normal'
      )

      expect(secureapi2.put).toHaveBeenCalledWith(
        '/lms/loan-requests/123/documents/make',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingUploadDocument(true)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingUploadDocument(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'PaymentDetails document uploaded successfully',
          type: 'success',
        })
      )
      expect(result).toEqual(mockResponse.data)
    })
    it('should handle API errors and dispatch error notification', async () => {
      const mockDispatch = vi.fn()
      const mockData = {
        documentType: 'ID',
        document: 'base64string',
        comments: 'Test document',
      }
      const mockError = new Error('Network Error')
      secureapi2.put = vi.fn().mockRejectedValue(mockError)

      await uploadDocument(mockData, mockDispatch, '123', 'normal')

      expect(secureapi2.put).toHaveBeenCalledWith(
        '/lms/loan-requests/123/documents/make',
        mockData
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingUploadDocument(true)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingUploadDocument(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Network Error',
          type: 'error',
        })
      )
    })
  })

  /***************** Cancel Loan Requests tets******************/
  describe('cancelLoanRequest', () => {
    const dispatch = vi.fn()
    const requestId = '12345'
    const data = { comments: 'Cancel request' }

    it('should cancel loan request successfully with type "super"', async () => {
      const type = 'super'
      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await cancelLoanRequest(requestId, data, dispatch, type)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: 'Loan request cancelled', type: 'success' })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/cancel`,
        data
      )
    })

    it('should cancel loan request successfully with type "make"', async () => {
      const dispatch = vi.fn()
      const type = 'make'
      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await cancelLoanRequest(requestId, data, dispatch, type)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Loan request cancelled, awaiting approval',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/cancel/make`,
        data
      )
    })

    it('should handle error when cancelling loan request', async () => {
      const type = 'make'
      const errorMessage = 'Failed to cancel loan request'
      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await cancelLoanRequest(requestId, data, dispatch, type)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
      expect(console.error).toHaveBeenCalledWith(
        'Error cancelling loan request: ',
        expect.any(Error)
      )
    })
  })
  describe('acceptCancelLoanRequest', () => {
    it('should dispatch success notification when loan cancellation request is approved', async () => {
      const dispatch = vi.fn()
      const requestId = '123'
      const approvalId = '456'
      const comments = 'Approved'

      secureapi2.put = vi.fn().mockResolvedValueOnce({})

      await acceptCancelLoanRequest({
        requestId,
        approvalId,
        comments,
        dispatch,
      })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/cancel/accept/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setApprovalActionsLMS(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Cancel request has been approved successfully.',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
    })

    it('should dispatch error notification when network failure occurs', async () => {
      const dispatch = vi.fn()
      const requestId = '123'
      const approvalId = '456'
      const comments = 'Approved'
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValueOnce(new Error(errorMessage))

      await acceptCancelLoanRequest({
        requestId,
        approvalId,
        comments,
        dispatch,
      })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/cancel/accept/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
    })
  })

  describe('rejectCancelLoanRequest', () => {
    it('should dispatch success actions when API call is successful', async () => {
      const dispatch = vi.fn()
      const requestId = '123'
      const approvalId = '456'
      const comments = 'Valid reason'

      secureapi2.put = vi.fn().mockResolvedValue({})

      await rejectCancelLoanRequest({
        requestId,
        approvalId,
        comments,
        dispatch,
      })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/cancel/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(setApprovalActionsLMS(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Cancel request has been rejected successfully.',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
    })
    it('should dispatch error notification when API call fails', async () => {
      const dispatch = vi.fn()
      const requestId = '123'
      const approvalId = '456'
      const comments = 'Valid reason'
      const errorMessage = 'Network Error'

      secureapi2.put = vi.fn().mockRejectedValue(new Error(errorMessage))

      await rejectCancelLoanRequest({
        requestId,
        approvalId,
        comments,
        dispatch,
      })

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(true))
      expect(secureapi2.put).toHaveBeenCalledWith(
        `/lms/loan-requests/${requestId}/cancel/reject/${approvalId}`,
        { comments }
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingCancelRequest(false))
    })
  })

  /******************************* update a loan request customer profile **************************/

  describe('updateCustomerProfile', () => {
    it('should dispatch success notification when customer profile is updated successfully', async () => {
      const mockDispatch = vi.fn()
      const mockData = {
        pepPipRemarks: 'Remarks',
        pipPepCategory: 'Category',
        comments: 'Some comments',
      }
      const mockCustomerId = '12345'
      const mockType = 'regular'

      vi.spyOn(secureapi2, 'patch').mockResolvedValueOnce({})

      await updateCustomerProfile(
        mockData,
        mockCustomerId,
        mockDispatch,
        mockType
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingCustomerProfile(true)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingCustomerProfile(false)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Customer profile updated successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when network error occurs', async () => {
      const mockDispatch = vi.fn()
      const mockData = {
        pepPipRemarks: 'Remarks',
        pipPepCategory: 'Category',
        comments: 'Some comments',
      }
      const mockCustomerId = '12345'
      const mockType = 'regular'
      const errorMessage = 'Network Error'

      vi.spyOn(secureapi2, 'patch').mockRejectedValueOnce(
        new Error(errorMessage)
      )

      await updateCustomerProfile(
        mockData,
        mockCustomerId,
        mockDispatch,
        mockType
      )

      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingCustomerProfile(true)
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(mockDispatch).toHaveBeenCalledWith(
        setIsLoadingCustomerProfile(false)
      )
    })
  })
  describe('getRequestCheckReport', () => {
    it('should dispatch success actions and set response when API call succeeds', async () => {
      const dispatch = vi.fn()
      const params = '?param=value'
      const mockResponse = { data: { report: 'reportData' } }
      const downloadBlob = vi.fn()

      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await getRequestCheckReport(dispatch, params)
      await downloadBlob(Blob, 'Request_Checks_Report.xlsx')

      expect(dispatch).toHaveBeenCalledWith(setRequestCheckReportsLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setRequestCheckReportsSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsLoading(false)
      )
      expect(downloadBlob).toHaveBeenCalledWith(
        Blob,
        'Request_Checks_Report.xlsx'
      )
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsResponse(mockResponse.data)
      )
    })
    it('should handle errors and dispatch failure actions when API call fails', async () => {
      const dispatch = vi.fn()
      const params = '?param=value'
      const mockError = new Error('Network Error')

      vi.spyOn(secureapi2, 'get').mockRejectedValueOnce(mockError)

      await getRequestCheckReport(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setRequestCheckReportsLoading(true))
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(setLoanRequestReportSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: mockError.message,
          type: 'error',
        })
      )
    })
  })
  describe('generateRequestChecksReport', () => {
    it('should dispatch success actions and set response when API call succeeds', async () => {
      const dispatch = vi.fn()
      const params = '?param=value'
      const mockResponse = { data: { report: 'reportData' } }

      vi.spyOn(secureapi2, 'get').mockResolvedValueOnce(mockResponse)

      await getRequestCheckReport(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setRequestCheckReportsLoading(true))
      expect(dispatch).toHaveBeenCalledWith(setRequestCheckReportsSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsResponse(mockResponse.data)
      )
    })
    it('should dispatch error actions and notification when API call fails', async () => {
      const dispatch = vi.fn()
      const params = '?param=value'
      const errorMessage = 'Network Error'
      vi.spyOn(secureapi2, 'get').mockRejectedValue(new Error(errorMessage))

      await generateRequestChecksReport(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsExportLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsExportLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setRequestCheckReportsExportSuccess(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  /********************************* Broker Management APIs Tests ***********************/
  describe('getBrokers', () => {
    it('should dispatch setBrokers with data when fetching brokers is successful', async () => {
      const dispatch = vi.fn()
      const params = 'status=active'
      const mockData = [{ id: 1, name: 'Broker A' }]
      secureapi2.get = vi.fn().mockResolvedValue({ data: { data: mockData } })

      await getBrokers(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingBrokers(true))
      expect(secureapi2.get).toHaveBeenCalledWith(`/lms/brokers?${params}`)
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingBrokers(false))
      expect(dispatch).toHaveBeenCalledWith(setBrokers(mockData))
    })
    it('should handle empty or null query parameters gracefully', async () => {
      const dispatch = vi.fn()
      const params = ''
      const mockData = [{ id: 2, name: 'Broker B' }]
      secureapi2.get = vi.fn().mockResolvedValue({ data: { data: mockData } })

      await getBrokers(dispatch, params)

      expect(dispatch).toHaveBeenCalledWith(setIsLoadingBrokers(true))
      expect(secureapi2.get).toHaveBeenCalledWith('/lms/brokers?')
      expect(dispatch).toHaveBeenCalledWith(setIsLoadingBrokers(false))
      expect(dispatch).toHaveBeenCalledWith(setBrokers(mockData))
    })
  })
  describe('createBroker', () => {
    //   it('should dispatch success actions and return broker data when valid data is provided', async () => {
    //     const dispatch = vi.fn()
    //     const brokerData = {
    //       name: 'John Doe',
    //       email: '<EMAIL>',
    //       mobile: '**********',
    //       bankName: 'Bank A',
    //       status: 'active',
    //       bankCode: '001',
    //       swiftCode: 'ABC123',
    //       bankAccountNumber: '*********',
    //       branchCode: '002',
    //       accountBranchName: 'Main Branch',
    //       physicalAddress: {
    //         country: 'USA',
    //         town: 'DC',
    //         physicalAddress: '6th Boulevard',
    //       },
    //       products: [loanProductCreateStub],
    //       callBackUrl: 'http://callback.url',
    //     }
    //     const response = { data: brokerData }
    //     secureapi2.post = vi.fn().mockResolvedValue(response)

    //     const result = await createBroker(dispatch, brokerData, 'make')

    //     expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))
    //     expect(secureapi2.post).toHaveBeenCalledWith('/lms/brokers', brokerData)
    //     expect(dispatch).toHaveBeenCalledWith(setCreateBrokerSuccess(true))
    //     expect(dispatch).toHaveBeenCalledWith(setCreatedBroker(response.data))
    //     expect(dispatch).toHaveBeenCalledWith(
    //       setNotification({
    //         message: 'Broker created successfully.',
    //         type: 'success',
    //       })
    //     )
    //     expect(result).toEqual(response.data)
    //   })
    // Successfully creates a broker and assigns products

    // Successfully creates a broker with valid data and type
    it('should dispatch success actions when broker is created successfully', async () => {
      const dispatch = vi.fn()

      const brokerData = {
        name: 'John Doe',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Bank A',
        status: 'active',
        bankCode: '001',
        swiftCode: 'ABC123',
        bankAccountNumber: '*********',
        branchCode: '002',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'USA',
          town: 'DC',
          physicalAddress: '6th Boulevard',
        },
        products: [loanProductCreateStub],
        callBackUrl: 'http://callback.url',
      }

      const type = 'make'
      const response = { data: { id: 1, name: 'Test Broker' } }

      secureapi2.post = vi.fn().mockResolvedValue(response)

      await createBroker(dispatch, brokerData, type)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))
      expect(secureapi2.post).toHaveBeenCalledWith(
        '/lms/brokers/make',
        brokerData
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setCreateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(setCreatedBroker(response.data))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker created successfully.',
          type: 'success',
        })
      )
    })

    it('should dispatch failure actions and error notification when network error occurs', async () => {
      const dispatch = vi.fn()
      const brokerData = {
        name: 'John Doe',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Bank A',
        status: 'active',
        bankCode: '001',
        swiftCode: 'ABC123',
        bankAccountNumber: '*********',
        branchCode: '002',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'USA',
          town: 'DC',
          physicalAddress: '6th Boulevard',
        },
        products: [loanProductCreateStub],
        callBackUrl: 'http://callback.url',
      }

      const errorMessage = 'Network Error'
      const type = 'make'
      secureapi2.post = vi.fn().mockRejectedValue(new Error(errorMessage))

      await createBroker(dispatch, brokerData, type)

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))
      expect(secureapi2.post).toHaveBeenCalledWith('/lms/brokers', brokerData)
      expect(dispatch).toHaveBeenCalledWith(setCreateBrokerFailure(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(false))
    })
  })
  describe('updateBroker', () => {
    const dispatch = vi.fn()
    const brokerID = 'broker-123'
    const typeMake = 'make'
    const typeNormal = 'normal'
    const data = {
      accountBranchName: 'Main Branch',
      bankAccountNumber: '*********',
      bankCode: '001',
      bankName: 'Test Bank',
      branchCode: '001',
      callBackUrl: 'http://callback.url',
      email: '<EMAIL>',
      mobile: '**********',
      name: 'Test Broker',
      physicalAddress: {
        street: '123 Test St',
        city: 'Test City',
        country: 'Test Country',
      },
      product: [],
      status: 'active',
      swiftCode: 'TSTB123',
    }

    afterEach(() => {
      vi.clearAllMocks()
    })
    it('should update broker details successfully with type "make"', async () => {
      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await updateBroker(brokerID, dispatch, data, typeMake)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker Details update successful pending approval',
          type: 'success',
        })
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `lms/brokers/${brokerID}/make`,
        data
      )
    })

    it('should update broker details successfully without type "make"', async () => {
      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await updateBroker(brokerID, dispatch, data, typeNormal)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker Details update successful',
          type: 'success',
        })
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `lms/brokers/${brokerID}/`,
        data
      )
    })
    it('should handle error when updating broker details', async () => {
      const errorMessage = 'Failed to update broker details'
      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await updateBroker(brokerID, dispatch, data, typeNormal)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })

  describe('generateBrokerSecret', () => {
    it('should generate broker secret and dispatch success notification when API call is successful', async () => {
      const brokerID = '123'
      const dispatch = vi.fn()
      const data = {
        nominatedEmailAddress: '<EMAIL>',
        requestType: 'RESET',
      }
      const response = { data: { secret: 'broker-secret' } }

      vi.spyOn(secureapi2, 'put').mockResolvedValue(response)

      const result = await generateBrokerSecret(brokerID, dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(
        setIsGenerateBrokerSecretLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGenerateBrokerSecretSuccess(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGenerateBrokerSecretFailure(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker secret generated successfully',
          type: 'success',
        })
      )
      expect(result).toEqual(response.data)
    })
    it('should handle network errors and dispatch error notification when API call fails', async () => {
      const brokerID = '123'
      const dispatch = vi.fn()
      const data = {
        nominatedEmailAddress: '<EMAIL>',
        requestType: 'generate',
      }
      const errorMessage = 'Network Error'

      vi.spyOn(secureapi2, 'put').mockRejectedValue(new Error(errorMessage))

      await generateBrokerSecret(brokerID, dispatch, data)

      expect(dispatch).toHaveBeenCalledWith(
        setIsGenerateBrokerSecretLoading(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGenerateBrokerSecretSuccess(false)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setGenerateBrokerSecretFailure(true)
      )
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
  describe('createBrokerAndAssignProducts', () => {
    it('should create a broker and assign products successfully when API calls succeed', async () => {
      const dispatch = vi.fn()
      const brokerData = {
        name: 'Test Broker',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Test Bank',
        bankCode: 'TB123',
        swiftCode: 'TB123456',
        bankAccountNumber: '*********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Test Country',
          town: 'Test Town',
          physicalAddress: '123 Test Street',
        },
      }
      const selectedProducts = [{ id: 'prod1' }, { id: 'prod2' }]
      const type = 'maker'

      secureapi2.post = vi
        .fn()
        .mockResolvedValueOnce({ data: { id: 'broker1' } })
        .mockResolvedValueOnce({})
        .mockResolvedValueOnce({})

      const result = await createBrokerAndAssignProducts(
        dispatch,
        brokerData,
        selectedProducts,
        type
      )

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setCreateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(setCreatedBroker({ id: 'broker1' }))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker created successfully pending approval',
          type: 'success',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setAssignBrokerProductLoading(true))
      expect(dispatch).toHaveBeenCalledWith(
        setAssignBrokerProductLoading(false)
      )
      expect(dispatch).toHaveBeenCalledWith(setAssignBrokerProductSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Product assignment completed successfully.',
          type: 'success',
        })
      )
      expect(result).toEqual({ id: 'broker1' })
    })
    it('should handle API failure gracefully when broker creation fails', async () => {
      const dispatch = vi.fn()
      const brokerData = {
        name: 'Test Broker',
        email: '<EMAIL>',
        mobile: '**********',
        bankName: 'Test Bank',
        bankCode: 'TB123',
        swiftCode: 'TB123456',
        bankAccountNumber: '*********',
        branchCode: '001',
        accountBranchName: 'Main Branch',
        physicalAddress: {
          country: 'Test Country',
          town: 'Test Town',
          physicalAddress: '123 Test Street',
        },
      }
      const selectedProducts = [{ id: 'prod1' }, { id: 'prod2' }]
      const type = 'maker'

      secureapi2.post = vi.fn().mockRejectedValue(new Error('API Error'))

      await createBrokerAndAssignProducts(
        dispatch,
        brokerData,
        selectedProducts,
        type
      )

      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setCreateBrokerFailure(true))
      expect(dispatch).toHaveBeenCalledWith(setAssignBrokerProductFailure(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'API Error',
          type: 'error',
        })
      )
      expect(dispatch).toHaveBeenCalledWith(setLoadingCreateBroker(false))
    })
  })
  describe('assignProductToBroker', () => {
    it('should assign products to a broker successfully when API call succeeds', async () => {
      const brokerID = 'broker123'
      const selectedProducts = [{ id: 'product1' }, { id: 'product2' }]
      const dispatch = vi.fn()
      const postMock = vi.spyOn(secureapi2, 'post').mockResolvedValue({})

      await assignProductToBroker(brokerID, selectedProducts, dispatch)

      expect(postMock).toHaveBeenCalledTimes(selectedProducts.length)
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker Product(s) Assigned successfully',
          type: 'success',
        })
      )

      postMock.mockRestore()
    })
    it('should handle empty selectedProducts array without errors', async () => {
      const brokerID = 'broker123'
      const selectedProducts = []
      const dispatch = vi.fn()

      await assignProductToBroker(brokerID, selectedProducts, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Broker Product(s) Assigned successfully',
          type: 'success',
        })
      )
    })
  })
  describe('getBrokerProducts', () => {
    it('should dispatch setBrokerProducts and update status to success when API call is successful', async () => {
      const brokerID = '123'
      const dispatch = vi.fn()
      const mockData = [{ id: 1, name: 'Product A' }]

      secureapi2.get = vi.fn().mockResolvedValue({ data: mockData })

      await getBrokerProducts(brokerID, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))
      expect(dispatch).toHaveBeenCalledWith(setBrokerProducts(mockData))
    })
    it('should dispatch setNotification and update status to failure when API call fails', async () => {
      const brokerID = '123'
      const dispatch = vi.fn()
      const errorMessage = 'Network Error'

      secureapi2.get = vi.fn().mockRejectedValue(new Error(errorMessage))

      await getBrokerProducts(brokerID, dispatch)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({ message: errorMessage, type: 'error' })
      )
    })
  })
  describe('removeProductFromBroker', () => {
    it('should dispatch success notification when product is removed successfully', async () => {
      const brokerID = 'validBrokerID'
      const productID = 'validProductID'
      const dispatch = vi.fn()
      vi.spyOn(secureapi2, 'delete').mockResolvedValueOnce({})

      await removeProductFromBroker(brokerID, productID, dispatch)

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Product deleted successfully',
          type: 'success',
        })
      )
    })
    it('should dispatch error notification when API call fails', async () => {
      const brokerID = 'validBrokerID'
      const productID = 'validProductID'
      const dispatch = vi.fn()
      const errorMessage = 'Network Error'
      vi.spyOn(secureapi2, 'delete').mockRejectedValueOnce(
        new Error(errorMessage)
      )

      await removeProductFromBroker(brokerID, productID, dispatch)

      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
  describe('approveUpdateBroker', () => {
    it('should dispatch success actions and notification when broker update is approved', async () => {
      const mockDispatch = vi.fn()
      const brokerID = 'validBrokerID'
      const approvalID = 'validApprovalID'
      const type = 'accept'
      const comments = 'Approved'

      vi.spyOn(secureapi2, 'put').mockResolvedValue({})

      await approveUpdateBroker(
        brokerID,
        approvalID,
        mockDispatch,
        type,
        comments
      )

      expect(mockDispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(mockDispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))
      expect(mockDispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Update broker request completed successfully',
          type: 'success',
        })
      )
    })
  })

  describe('approveMakeCreateBroker', () => {
    const dispatch = vi.fn()
    const approvalId = 'approval-123'
    const comments = 'Stubbed comment'
    const typeAccept = 'accept'
    const typeReject = 'reject'

    afterEach(() => {
      vi.clearAllMocks()
    })

    it('should approve a broker creation request successfully', async () => {
      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await approveMakeCreateBroker(approvalId, dispatch, typeAccept, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))
      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create broker request completed successfully',
          type: 'success',
        })
      )
      expect(secureapi2.put).toHaveBeenCalledWith(
        `lms/brokers/approve/${approvalId}`,
        { comments }
      )
    })

    it('should reject a broker creation request successfully', async () => {
      vi.spyOn(secureapi2, 'put').mockResolvedValueOnce({})

      await approveMakeCreateBroker(approvalId, dispatch, typeReject, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))

      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: 'Create broker request rejected successfully',
          type: 'success',
        })
      )

      expect(secureapi2.put).toHaveBeenCalledWith(
        `lms/brokers/reject/${approvalId}`,
        { comments }
      )
    })

    it('should handle error when approving or rejecting a broker creation request', async () => {
      const errorMessage = 'Failed to approve/reject broker request'
      vi.spyOn(secureapi2, 'put').mockRejectedValueOnce(new Error(errorMessage))

      await approveMakeCreateBroker(approvalId, dispatch, typeAccept, comments)

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(true))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(false))

      expect(dispatch).toHaveBeenCalledWith(setLoadingUpdateBroker(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerSuccess(false))
      expect(dispatch).toHaveBeenCalledWith(setUpdateBrokerFailure(true))
      expect(dispatch).toHaveBeenCalledWith(
        setNotification({
          message: errorMessage,
          type: 'error',
        })
      )
    })
  })
})
