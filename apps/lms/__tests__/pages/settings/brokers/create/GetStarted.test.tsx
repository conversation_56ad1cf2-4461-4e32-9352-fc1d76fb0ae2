import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent } from '@testing-library/react'
import { renderWithProviders } from '../../../../utils/brokerTestUtils'
import { GetStarted } from '../../../../../src/app/settings/brokers/create/GetStarted'

// Mock the icons
vi.mock('@dtbx/ui/icons', () => ({
  Broker: () => <div data-testid="broker-icon">Broker Icon</div>,
  ExistingBroker: () => <div data-testid="existing-broker-icon">Existing Broker Icon</div>,
}))

// Mock the BrokerOption component
vi.mock('../../../../../src/app/settings/brokers/create/BrokerOption', () => ({
  default: ({ imageSrc, title, description, onClick }: any) => (
    <div data-testid="broker-option" onClick={onClick}>
      {imageSrc}
      <h3>{title}</h3>
      <p>{description}</p>
    </div>
  ),
}))

// Mock window.history.back
Object.defineProperty(window, 'history', {
  value: {
    back: vi.fn(),
  },
  writable: true,
})

const mockSetCurrentStageFn = vi.fn()

describe('GetStarted Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockSetCurrentStageFn.mockClear()
  })

  it('should render the component with correct title and description', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    expect(screen.getByText('Get started')).toBeInTheDocument()
    expect(screen.getByText("Select the kind of broker you'd like to create.")).toBeInTheDocument()
  })

  it('should render both creation options', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    // Check for "Create a new broker" option
    expect(screen.getByText('Create a new broker')).toBeInTheDocument()
    expect(screen.getByText('Create a new broker by entering all necessary details.')).toBeInTheDocument()

    // Check for "Create a broker using existing organisation details" option
    expect(screen.getByText('Create a broker using existing organisation details')).toBeInTheDocument()
    expect(screen.getByText("The broker's information will be auto-filled using the selected organization's data.")).toBeInTheDocument()
  })

  it('should render broker option components', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    const brokerOptions = screen.getAllByTestId('broker-option')
    expect(brokerOptions).toHaveLength(2)
  })

  it('should render broker icons', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    expect(screen.getByTestId('broker-icon')).toBeInTheDocument()
    expect(screen.getByTestId('existing-broker-icon')).toBeInTheDocument()
  })

  it('should handle "Create a new broker" option click', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    const createNewBrokerOption = screen.getByText('Create a new broker').closest('[data-testid="broker-option"]')
    fireEvent.click(createNewBrokerOption!)

    expect(mockSetCurrentStageFn).toHaveBeenCalledWith('CreateBrokerForm')
  })

  it('should handle "Create from existing organization" option click', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    const createFromExistingOption = screen.getByText('Create a broker using existing organisation details').closest('[data-testid="broker-option"]')
    fireEvent.click(createFromExistingOption!)

    expect(mockSetCurrentStageFn).toHaveBeenCalledWith('FromExistingOrganization')
  })

  it('should render back button and handle click', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    const backButton = screen.getByRole('button')
    expect(backButton).toBeInTheDocument()

    fireEvent.click(backButton)
    expect(window.history.back).toHaveBeenCalled()
  })

  it('should render with proper styling and layout', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    // Check for main container
    const mainContainer = screen.getByText('Get started').closest('div')
    expect(mainContainer).toBeInTheDocument()

    // Check for options container
    const optionsContainer = screen.getAllByTestId('broker-option')[0].closest('div')
    expect(optionsContainer).toBeInTheDocument()
  })

  it('should have proper component structure', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    // Check that both broker options are rendered
    const brokerOptions = screen.getAllByTestId('broker-option')
    expect(brokerOptions).toHaveLength(2)

    // Check that each option has the required elements
    brokerOptions.forEach(option => {
      expect(option).toBeInTheDocument()
    })
  })

  it('should render option descriptions correctly', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    expect(screen.getByText('Create a new broker by entering all necessary details.')).toBeInTheDocument()
    expect(screen.getByText("The broker's information will be auto-filled using the selected organization's data.")).toBeInTheDocument()
  })

  it('should handle multiple option clicks', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    const createNewOption = screen.getByText('Create a new broker').closest('[data-testid="broker-option"]')
    const createFromExistingOption = screen.getByText('Create a broker using existing organisation details').closest('[data-testid="broker-option"]')

    fireEvent.click(createNewOption!)
    expect(mockSetCurrentStageFn).toHaveBeenCalledWith('CreateBrokerForm')

    fireEvent.click(createFromExistingOption!)
    expect(mockSetCurrentStageFn).toHaveBeenCalledWith('FromExistingOrganization')

    expect(mockSetCurrentStageFn).toHaveBeenCalledTimes(2)
  })

  it('should maintain responsive design', () => {
    renderWithProviders(<GetStarted setCurrentStage={mockSetCurrentStageFn} />)

    // The component should render without errors and maintain its structure
    expect(screen.getByText('Get started')).toBeInTheDocument()
    expect(screen.getAllByTestId('broker-option')).toHaveLength(2)
  })
})
