## Package Maintenance
This monorepo uses [Changesets](https://github.com/changesets/changesets/blob/main/packages/cli/README.md) to manage versioning, changelogs, and publishing of shared packages (@dtbx/ui, @dtbx/store, etc.). This allows you to version and publish packages independently.
### Contributor Workflow: Adding a Changeset

If your Pull Request includes changes to any of the shared packages, you must include a changeset.

1. Make your code changes.
2. Run the changeset command from the project root:
```
pnpm changeset
```
3. Follow the prompts:
   Follow the prompts to create a changeset. This will create a markdown file in the `.changeset` folder. You can edit this file to add more details about the changes you made.
- Select the packages you've changed using the spacebar.
- Choose the correct SemVer bump type (Major, Minor, or Patch) for each package.
- Write a clear, concise summary of the changes. This will be used in the package's CHANGELOG.md

4. Commit the generated file: A new markdown file will be created in the .changeset directory. Add this file to your commit.
```
git add .
git commit -m "feat(ui): add new DataTable component and changeset"
```
### Maintainer Workflow: Releasing Packages
After a PR with changesets is merged into the main branch, a maintainer can release the new versions.
1. Create the release PR: The version-packages command consumes the changeset files, updates package versions and changelogs, and creates a new commit.
```
pnpm version-packages
```
Push this new commit and its tags to a new branch and open a "Version Packages" PR for review.
2. Publish to the registry: Once the versioning PR is merged, run the release command. This will build the publishable packages and push them to the registry.
   To publish the packages, you can use the following command:
```
pnpm release
```